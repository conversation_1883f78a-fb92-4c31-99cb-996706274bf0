#!/usr/bin/env python3
"""Start admin dashboard with comprehensive error handling"""

import os
import sys
import subprocess
import time

def install_missing_packages():
    """Install any missing packages"""
    packages = [
        'flask==3.0.0',
        'pandas==2.1.3', 
        'requests==2.31.0',
        'beautifulsoup4==4.12.2',
        'feedparser==6.0.10'
    ]
    
    python_exe = "C:/Users/<USER>/Desktop/freelance/toolscrape/.venv/Scripts/python.exe"
    
    for package in packages:
        try:
            print(f"Checking {package}...")
            subprocess.check_call([python_exe, "-m", "pip", "install", package], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        except:
            pass

def main():
    print("🚀 Starting Admin Dashboard with Error Handling...")
    
    # Install missing packages first
    print("📦 Ensuring dependencies are installed...")
    install_missing_packages()
    
    try:
        # Import the admin dashboard
        print("📥 Importing admin dashboard...")
        import admin_dashboard
        app = admin_dashboard.app
        
        print("✅ Import successful!")
        
        # Check routes
        routes = {rule.endpoint: rule.rule for rule in app.url_map.iter_rules()}
        api_routes = [k for k in routes.keys() if 'api_scraping' in k]
        
        print(f"🛠️  API scraping routes found: {api_routes}")
        
        if 'admin_api_scraping' in routes:
            print("✅ admin_api_scraping route registered!")
        else:
            print("❌ admin_api_scraping route missing!")
            print("Available routes with 'admin':", [k for k in routes.keys() if 'admin' in k])
        
        print("\n🌐 Starting server on http://localhost:5001")
        print("🔐 Login: admin / admin123")
        print("📋 Dashboard: http://localhost:5001/admin")
        print("🔌 API Scraping: http://localhost:5001/admin/api-scraping")
        
        # Start the server
        print("\n🔄 Starting Flask server...")
        app.run(host='127.0.0.1', port=5001, debug=True, use_reloader=False)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
        # Try to identify the specific issue
        if "feedparser" in str(e):
            print("\n💡 Installing feedparser...")
            try:
                subprocess.check_call([
                    "C:/Users/<USER>/Desktop/freelance/toolscrape/.venv/Scripts/python.exe", 
                    "-m", "pip", "install", "feedparser"
                ])
                print("✅ feedparser installed, please restart")
            except:
                print("❌ Failed to install feedparser")
        
        input("Press Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Dashboard stopped by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        input("Press Enter to exit...")
