"""
AI Tools Scraper - Main Runner
Comprehensive scraping solution for AI tools from multiple sources
"""

import sys
import logging
import time
from ai_tools_scraper import AIToolsScraper
from extended_scraper import Extended<PERSON><PERSON>oolsScraper
from database_analyzer import DatabaseAnalyzer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraping.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def main():
    """Main function to run all scraping operations"""
    logger.info("Starting AI Tools Comprehensive Scraping System")
    
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                AI TOOLS SCRAPER SYSTEM                       ║
    ║            Comprehensive Database Builder                    ║
    ╚══════════════════════════════════════════════════════════════╝
    
    This system will scrape AI tools from the following sources:
    
    🌐 Web Sources:
    ✓ bestaito.com (Free AI tools)
    ✓ bestaitools.com 
    ✓ 10bestaitools.com
    ✓ bestfreeaitools.io
    ✓ best-ai-tools.org
    ✓ aisitelist.com
    ✓ futuretools.io
    ✓ opentools.ai
    
    📚 Repository Sources:
    ✓ GitHub: mahseema/awesome-ai-tools (315+ tools)
    
    🤗 AI Model Sources:
    ✓ HuggingFace Top Models (DeepSeek-R1, Llama-3.1, FLUX.1, etc.)
    
    Starting scraping process...
    """)
    
    total_tools_scraped = 0
    
    try:
        # Phase 1: Main website scraping
        logger.info("Phase 1: Scraping main AI tools websites...")
        print("\n🔄 Phase 1: Scraping main AI tools websites...")
        
        main_scraper = AIToolsScraper()
        main_tools = main_scraper.scrape_all_sources()
        total_tools_scraped += len(main_tools)
        
        print(f"✅ Phase 1 Complete: {len(main_tools)} tools scraped from main sources")
        main_scraper.close()
        
        # Small delay between phases
        time.sleep(3)
        
        # Phase 2: Extended scraping (GitHub, HuggingFace, additional sources)
        logger.info("Phase 2: Extended scraping (GitHub, HuggingFace, additional sources)...")
        print("\n🔄 Phase 2: Extended scraping (GitHub, HuggingFace, additional sources)...")
        
        extended_scraper = ExtendedAIToolsScraper()
        extended_tools = extended_scraper.run_extended_scraping()
        total_tools_scraped += len(extended_tools)
        
        print(f"✅ Phase 2 Complete: {len(extended_tools)} additional tools/models scraped")
        extended_scraper.close()
        
        # Phase 3: Analysis and export
        logger.info("Phase 3: Database analysis and export...")
        print("\n🔄 Phase 3: Database analysis and export...")
        
        analyzer = DatabaseAnalyzer()
        
        # Generate comprehensive report
        report_file = analyzer.generate_report()
        
        # Export data in multiple formats
        csv_file = analyzer.export_all_to_csv()
        json_file = analyzer.export_to_json()
        
        # Get final statistics
        stats = analyzer.get_comprehensive_statistics()
        
        analyzer.close()
        
        # Final summary
        print(f"""
        ╔══════════════════════════════════════════════════════════════╗
        ║                    SCRAPING COMPLETED!                      ║
        ╚══════════════════════════════════════════════════════════════╝
        
        📊 FINAL STATISTICS:
        ═══════════════════════════════════════════════════════════════
        Total AI Tools in Database: {stats['total_tools']}
        GitHub Repositories: {stats['github_repos']}
        HuggingFace Models: {stats['huggingface_models']}
        Free Tools: {stats['free_tools']}
        Open Source Tools: {stats['open_source_tools']}
        Tools with URLs: {stats['tools_with_urls']}
        
        📁 EXPORTED FILES:
        ═══════════════════════════════════════════════════════════════
        📈 Report: {report_file}
        📋 CSV Data: {csv_file}
        🗄️ JSON Data: {json_file}
        🗃️ Database: ai_tools_database.db
        📝 Log File: scraping.log
        
        🎯 TOP SOURCES:
        ═══════════════════════════════════════════════════════════════""")
        
        for source, count in list(stats['tools_by_source'].items())[:10]:
            print(f"        {source}: {count} tools")
        
        print(f"""
        🏷️ TOP CATEGORIES:
        ═══════════════════════════════════════════════════════════════""")
        
        for category, count in list(stats['tools_by_category'].items())[:10]:
            if category:
                print(f"        {category}: {count} tools")
        
        print(f"""
        ╔══════════════════════════════════════════════════════════════╗
        ║  SUCCESS! Your comprehensive AI tools database is ready!    ║
        ║                                                              ║
        ║  You can now:                                                ║
        ║  • Import the CSV files into Excel/Google Sheets            ║
        ║  • Use the JSON for web applications                        ║
        ║  • Query the SQLite database directly                       ║
        ║  • Analyze trends with the generated reports                ║
        ╚══════════════════════════════════════════════════════════════╝
        """)
        
        logger.info("All scraping operations completed successfully!")
        
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
        print("\n⚠️ Scraping interrupted by user. Partial data may be available.")
        
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        print(f"\n❌ Error occurred: {e}")
        print("Check scraping.log for detailed error information.")
        
    finally:
        print("\n🔚 Scraping session ended.")

if __name__ == "__main__":
    main()
