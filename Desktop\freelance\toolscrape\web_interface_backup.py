"""
Enhanced Flask web interface to view the AI tools database
"""

from flask import Flask, render_template, request, jsonify, send_file
import sqlite3
import pandas as pd
from datetime import datetime
import os

app = Flask(__name__)

def get_db_connection():
    conn = sqlite3.connect('ai_tools_database.db')
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/')
def index():
    """Enhanced main dashboard with comprehensive statistics"""
    conn = get_db_connection()
    
    # Get comprehensive statistics
    stats = {}
    stats['total_tools'] = conn.execute('SELECT COUNT(*) FROM ai_tools').fetchone()[0]
    stats['free_tools'] = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE free_tier = 1').fetchone()[0]
    stats['open_source'] = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE open_source = 1').fetchone()[0]
    stats['github_repos'] = conn.execute('SELECT COUNT(*) FROM github_repos').fetchone()[0] if conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='github_repos'").fetchone() else 0
    stats['huggingface_models'] = conn.execute('SELECT COUNT(*) FROM huggingface_models').fetchone()[0] if conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='huggingface_models'").fetchone() else 0
    stats['tools_with_urls'] = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE url IS NOT NULL AND url != ""').fetchone()[0]
    
    # Get recent tools (last 50 for better overview)
    recent_tools = conn.execute('''
        SELECT name, description, category, source_website, url, free_tier, open_source, scraped_date 
        FROM ai_tools 
        ORDER BY scraped_date DESC 
        LIMIT 50
    ''').fetchall()
    
    # Get comprehensive category counts
    categories = conn.execute('''
        SELECT category, COUNT(*) as count,
               SUM(CASE WHEN free_tier = 1 THEN 1 ELSE 0 END) as free_count,
               SUM(CASE WHEN open_source = 1 THEN 1 ELSE 0 END) as open_source_count
        FROM ai_tools 
        WHERE category IS NOT NULL AND category != ''
        GROUP BY category 
        ORDER BY count DESC 
        LIMIT 15
    ''').fetchall()
    
    # Get source counts with additional details
    sources = conn.execute('''
        SELECT source_website, COUNT(*) as count,
               SUM(CASE WHEN free_tier = 1 THEN 1 ELSE 0 END) as free_count,
               SUM(CASE WHEN open_source = 1 THEN 1 ELSE 0 END) as open_source_count,
               MIN(scraped_date) as first_scraped,
               MAX(scraped_date) as last_scraped
        FROM ai_tools 
        GROUP BY source_website 
        ORDER BY count DESC
    ''').fetchall()
    
    # Get top tools by category
    top_tools_by_category = {}
    for cat in ['Text', 'Image', 'Audio', 'Code', 'Video']:
        tools = conn.execute('''
            SELECT name, description, url, free_tier, open_source
            FROM ai_tools 
            WHERE category = ?
            ORDER BY name
            LIMIT 5
        ''', (cat,)).fetchall()
        if tools:
            top_tools_by_category[cat] = tools
    
    conn.close()
    
    return render_template('index.html', 
                         stats=stats, 
                         recent_tools=recent_tools,
                         categories=categories,
                         sources=sources,
                         top_tools_by_category=top_tools_by_category)
    ''').fetchall()
    
    conn.close()
    
    return render_template('index.html', 
    conn.close()
    
    return render_template('index.html', 
                         stats=stats, 
                         recent_tools=recent_tools,
                         categories=categories,
                         sources=sources,
                         top_tools_by_category=top_tools_by_category)

@app.route('/tools')
def tools():
    """Enhanced tools listing page with better filtering"""
    page = request.args.get('page', 1, type=int)
    category = request.args.get('category', '')
    source = request.args.get('source', '')
    search = request.args.get('search', '')
    free_only = request.args.get('free_only', '')
    open_source_only = request.args.get('open_source_only', '')
    per_page = 24  # Increased for better display
    
    conn = get_db_connection()
    
    # Build query with enhanced filtering
    query = 'SELECT * FROM ai_tools WHERE 1=1'
    params = []
    
    if category:
        query += ' AND category LIKE ?'
        params.append(f'%{category}%')
    
    if source:
        query += ' AND source_website LIKE ?'
        params.append(f'%{source}%')
    
    if search:
        query += ' AND (name LIKE ? OR description LIKE ?)'
        params.extend([f'%{search}%', f'%{search}%'])
    
    if free_only:
        query += ' AND free_tier = 1'
    
    if open_source_only:
        query += ' AND open_source = 1'
    
    # Get total count
    count_query = query.replace('SELECT *', 'SELECT COUNT(*)')
    total = conn.execute(count_query, params).fetchone()[0]
    
    # Add pagination
    query += ' ORDER BY name LIMIT ? OFFSET ?'
    params.extend([per_page, (page - 1) * per_page])
    
    tools = conn.execute(query, params).fetchall()
    
    # Get all categories and sources for filters
    all_categories = conn.execute('''
        SELECT DISTINCT category 
        FROM ai_tools 
        WHERE category IS NOT NULL AND category != ''
        ORDER BY category
    ''').fetchall()
    
    all_sources = conn.execute('''
        SELECT DISTINCT source_website 
        FROM ai_tools 
        ORDER BY source_website
    ''').fetchall()
    
    conn.close()
    
    # Calculate pagination
    total_pages = (total + per_page - 1) // per_page
    
    return render_template('tools.html',
                         tools=tools,
                         page=page,
                         total_pages=total_pages,
                         total=total,
                         category=category,
                         source=source,
                         search=search,
                         free_only=free_only,
                         open_source_only=open_source_only,
                         categories=all_categories,
                         sources=all_sources)

@app.route('/api/tools')
def api_tools():
    """API endpoint for tools data"""
    conn = get_db_connection()
    tools = conn.execute('SELECT * FROM ai_tools ORDER BY name').fetchall()
    conn.close()
    
    return jsonify([dict(tool) for tool in tools])

@app.route('/api/stats')
def api_stats():
    """API endpoint for statistics"""
    conn = get_db_connection()
    
    stats = {}
    stats['total_tools'] = conn.execute('SELECT COUNT(*) FROM ai_tools').fetchone()[0]
    stats['free_tools'] = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE free_tier = 1').fetchone()[0]
    stats['open_source'] = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE open_source = 1').fetchone()[0]
    
    conn.close()
    
    return jsonify(stats)

@app.route('/export/csv')
def export_csv():
    """Export data as CSV"""
    conn = get_db_connection()
    df = pd.read_sql_query('SELECT * FROM ai_tools', conn)
    conn.close()
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'ai_tools_export_{timestamp}.csv'
    df.to_csv(filename, index=False)
    
    return send_file(filename, as_attachment=True)

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    app.run(debug=True, host='0.0.0.0', port=5000)
