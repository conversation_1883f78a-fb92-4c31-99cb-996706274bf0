#!/usr/bin/env python3
"""
Validation script for the master database to ensure it's properly integrated
"""

import sqlite3
import json
from datetime import datetime

def validate_master_database():
    """Validate the master database structure and content"""
    print("="*60)
    print("MASTER DATABASE VALIDATION REPORT")
    print("="*60)
    print(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        conn = sqlite3.connect('ai_tools_master.db')
        conn.row_factory = sqlite3.Row
        
        # 1. Check database structure
        print(f"\n1. DATABASE STRUCTURE:")
        tables = conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()
        print(f"   Tables found: {[table[0] for table in tables]}")
        
        # 2. Check ai_tools table schema
        schema = conn.execute("PRAGMA table_info(ai_tools)").fetchall()
        print(f"\n2. AI_TOOLS TABLE SCHEMA:")
        for col in schema:
            print(f"   {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULLABLE'}")
        
        # 3. Data validation
        print(f"\n3. DATA VALIDATION:")
        
        # Basic counts
        total_tools = conn.execute('SELECT COUNT(*) FROM ai_tools').fetchone()[0]
        free_tools = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE free_tier = 1').fetchone()[0]
        tools_with_urls = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE website_url IS NOT NULL AND website_url != ""').fetchone()[0]
        tools_with_names = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE name IS NOT NULL AND name != ""').fetchone()[0]
        
        print(f"   Total tools: {total_tools}")
        print(f"   Free tools: {free_tools}")
        print(f"   Tools with URLs: {tools_with_urls}")
        print(f"   Tools with names: {tools_with_names}")
        
        # Data quality checks
        duplicate_names = conn.execute('''
            SELECT COUNT(*) FROM (
                SELECT name FROM ai_tools 
                GROUP BY LOWER(name) 
                HAVING COUNT(*) > 1
            )
        ''').fetchone()[0]
        
        empty_descriptions = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE description IS NULL OR description = ""').fetchone()[0]
        missing_categories = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE category IS NULL OR category = ""').fetchone()[0]
        
        print(f"   Potential duplicate names: {duplicate_names}")
        print(f"   Empty descriptions: {empty_descriptions}")
        print(f"   Missing categories: {missing_categories}")
        
        # 4. Category distribution
        print(f"\n4. TOP 10 CATEGORIES:")
        categories = conn.execute('''
            SELECT category, COUNT(*) as count 
            FROM ai_tools 
            WHERE category IS NOT NULL AND category != ''
            GROUP BY category 
            ORDER BY count DESC 
            LIMIT 10
        ''').fetchall()
        
        for cat in categories:
            print(f"   {cat[0]}: {cat[1]} tools")
        
        # 5. Source distribution
        print(f"\n5. TOP 10 SOURCES:")
        sources = conn.execute('''
            SELECT source_website, COUNT(*) as count 
            FROM ai_tools 
            GROUP BY source_website 
            ORDER BY count DESC 
            LIMIT 10
        ''').fetchall()
        
        for source in sources:
            print(f"   {source[0]}: {source[1]} tools")
        
        # 6. Sample tools for verification
        print(f"\n6. SAMPLE TOOLS (First 5):")
        sample_tools = conn.execute('''
            SELECT name, category, source_website, free_tier, website_url 
            FROM ai_tools 
            WHERE name IS NOT NULL 
            ORDER BY name 
            LIMIT 5
        ''').fetchall()
        
        for tool in sample_tools:
            free_status = "Free" if tool[3] else "Paid"
            url_status = "✓" if tool[4] else "✗"
            print(f"   {tool[0]} | {tool[1]} | {tool[2]} | {free_status} | URL: {url_status}")
        
        # 7. Web interface compatibility check
        print(f"\n7. WEB INTERFACE COMPATIBILITY:")
        
        # Check if required columns exist for web interface
        required_columns = ['id', 'name', 'description', 'category', 'website_url', 'free_tier', 'source_website', 'created_at']
        existing_columns = [col[1] for col in schema]
        
        missing_columns = [col for col in required_columns if col not in existing_columns]
        if missing_columns:
            print(f"   ⚠️  Missing columns: {missing_columns}")
        else:
            print(f"   ✅ All required columns present")
        
        # Test a typical web interface query
        try:
            test_query = conn.execute('''
                SELECT name, description, category, source_website, website_url, free_tier 
                FROM ai_tools 
                ORDER BY name 
                LIMIT 1
            ''').fetchone()
            print(f"   ✅ Web interface query test: PASSED")
        except Exception as e:
            print(f"   ❌ Web interface query test: FAILED - {e}")
        
        conn.close()
        
        print(f"\n8. VALIDATION SUMMARY:")
        if total_tools > 0 and tools_with_names == total_tools:
            print(f"   ✅ Database validation: PASSED")
            print(f"   ✅ Ready for web interface integration")
        else:
            print(f"   ❌ Database validation: FAILED")
            print(f"   ❌ Issues found that need attention")
        
    except Exception as e:
        print(f"   ❌ Database connection failed: {e}")
    
    print("="*60)

if __name__ == "__main__":
    validate_master_database()
