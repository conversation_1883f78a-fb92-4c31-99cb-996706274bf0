#!/usr/bin/env python3
"""Check database tables for API scraping functionality"""

import sqlite3

def check_db_tables():
    """Check if API scraping tables exist in the database"""
    try:
        conn = sqlite3.connect('ai_tools_master.db')
        cursor = conn.cursor()
        
        # Check for API-related tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%api%'")
        api_tables = cursor.fetchall()
        print('API-related tables:')
        for table in api_tables:
            print(f"- {table[0]}")
        
        # Check all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        all_tables = cursor.fetchall()
        print('\nAll tables:')
        for table in all_tables:
            print(f"- {table[0]}")
            
        # Check if specific API scraping tables exist
        required_tables = [
            'api_scraping_configs',
            'api_scraping_results'
        ]
        
        print('\nRequired API scraping tables status:')
        for table in required_tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            exists = cursor.fetchone() is not None
            status = '✅ Exists' if exists else '❌ Missing'
            print(f"- {table}: {status}")
            
            # If table exists, show its structure
            if exists:
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                print(f"  Columns:")
                for col in columns:
                    print(f"    - {col[1]} ({col[2]})")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    check_db_tables()
