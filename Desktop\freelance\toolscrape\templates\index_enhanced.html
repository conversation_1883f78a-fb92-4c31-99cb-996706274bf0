<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Enhanced AI Tools Database - Premium Collection</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #e2e8f0;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            padding: 30px 0;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4);
            border-radius: 20px;
            opacity: 0.1;
            z-index: -1;
        }
        
        .header h1 {
            font-size: 4rem;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
        }
        
        .header p {
            font-size: 1.4rem;
            opacity: 0.9;
            margin-bottom: 25px;
        }
        
        .header .subtitle {
            font-size: 1.1rem;
            opacity: 0.8;
            background: rgba(59, 130, 246, 0.1);
            padding: 10px 20px;
            border-radius: 50px;
            display: inline-block;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        
        .enhanced-badge {
            position: absolute;
            top: 10px;
            right: 20px;
            background: linear-gradient(45deg, #f59e0b, #ef4444);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
            padding: 10px 20px;
            border-radius: 20px;
            display: inline-block;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-weight: 500;
        }
        
        .stat-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .section {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            font-size: 1.4rem;
        }
        
        .recent-tools {
            grid-column: 1 / -1;
        }
        
        .tool-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background 0.3s ease;
        }
        
        .tool-item:hover {
            background: #f8f9ff;
        }
        
        .tool-item:last-child {
            border-bottom: none;
        }
        
        .tool-main {
            flex: 1;
        }
        
        .tool-name {
            font-weight: bold;
            color: #667eea;
            font-size: 1.1rem;
            margin-bottom: 5px;
        }
        
        .tool-description {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .tool-meta {
            text-align: right;
            font-size: 0.8rem;
            color: #999;
            min-width: 120px;
        }
        
        .tool-badges {
            margin-top: 8px;
        }
        
        .badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
            margin-right: 5px;
        }
        
        .badge-free {
            background: #4CAF50;
            color: white;
        }
        
        .badge-open-source {
            background: #FF9800;
            color: white;
        }
        
        .badge-category {
            background: #2196F3;
            color: white;
        }
        
        .category-item, .source-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .category-item:last-child, .source-item:last-child {
            border-bottom: none;
        }
        
        .category-main, .source-main {
            flex: 1;
        }
        
        .category-name, .source-name {
            font-weight: 500;
            color: #333;
        }
        
        .category-details, .source-details {
            font-size: 0.8rem;
            color: #666;
            margin-top: 2px;
        }
        
        .count-badge {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .nav-buttons {
            text-align: center;
            margin-top: 40px;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            margin: 0 10px;
            transition: all 0.3s;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
        }
        
        .featured-categories {
            grid-column: 1 / -1;
            margin-bottom: 20px;
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .category-section {
            background: #f8f9ff;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .category-section h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .category-tool {
            padding: 8px 0;
            border-bottom: 1px solid #e0e6ff;
            font-size: 0.9rem;
        }
        
        .category-tool:last-child {
            border-bottom: none;
        }
        
        .category-tool-name {
            font-weight: 500;
            color: #333;
        }
        
        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .category-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .tool-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .tool-meta {
                text-align: left;
                margin-top: 10px;
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI Tools Database</h1>
            <p>The most comprehensive collection of AI tools from across the web</p>
            <div class="subtitle">
                Updated daily • {{ stats.total_tools }} tools and growing • Free & Open Source
            </div>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">🔧</div>
                <div class="stat-number">{{ stats.total_tools }}</div>
                <div class="stat-label">Total AI Tools</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">💚</div>
                <div class="stat-number">{{ stats.free_tools }}</div>
                <div class="stat-label">Free Tools</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⭐</div>
                <div class="stat-number">{{ stats.open_source }}</div>
                <div class="stat-label">Open Source</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🔗</div>
                <div class="stat-number">{{ stats.tools_with_urls }}</div>
                <div class="stat-label">With Live URLs</div>
            </div>
            {% if stats.github_repos > 0 %}
            <div class="stat-card">
                <div class="stat-icon">📁</div>
                <div class="stat-number">{{ stats.github_repos }}</div>
                <div class="stat-label">GitHub Repos</div>
            </div>
            {% endif %}
            {% if stats.huggingface_models > 0 %}
            <div class="stat-card">
                <div class="stat-icon">🤗</div>
                <div class="stat-number">{{ stats.huggingface_models }}</div>
                <div class="stat-label">HuggingFace Models</div>
            </div>
            {% endif %}
        </div>
        
        {% if top_tools_by_category %}
        <div class="section featured-categories">
            <h2>🌟 Featured Tools by Category</h2>
            <div class="category-grid">
                {% for category, tools in top_tools_by_category.items() %}
                <div class="category-section">
                    <h3>{{ category }} Tools</h3>
                    {% for tool in tools %}
                    <div class="category-tool">
                        <div class="category-tool-name">{{ tool.name }}</div>
                        {% if tool.description %}
                        <div style="color: #666; font-size: 0.8rem;">{{ tool.description[:80] }}{% if tool.description|length > 80 %}...{% endif %}</div>
                        {% endif %}
                        <div class="tool-badges" style="margin-top: 5px;">
                            {% if tool.free_tier %}<span class="badge badge-free">Free</span>{% endif %}
                            {% if tool.open_source %}<span class="badge badge-open-source">Open Source</span>{% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <div class="content-grid">
            <div class="section">
                <h2>📊 Top Categories</h2>
                {% for category in categories %}
                <div class="category-item">
                    <div class="category-main">
                        <div class="category-name">{{ category.category }}</div>
                        <div class="category-details">
                            {% if category.free_count > 0 %}{{ category.free_count }} free{% endif %}
                            {% if category.open_source_count > 0 %}{% if category.free_count > 0 %} • {% endif %}{{ category.open_source_count }} open source{% endif %}
                        </div>
                    </div>
                    <span class="count-badge">{{ category.count }}</span>
                </div>
                {% endfor %}
            </div>
            
            <div class="section">
                <h2>🌐 Data Sources</h2>
                {% for source in sources %}
                <div class="source-item">
                    <div class="source-main">
                        <div class="source-name">{{ source.source_website }}</div>
                        <div class="source-details">
                            {% if source.free_count > 0 %}{{ source.free_count }} free{% endif %}
                            {% if source.open_source_count > 0 %}{% if source.free_count > 0 %} • {% endif %}{{ source.open_source_count }} open source{% endif %}
                        </div>
                    </div>
                    <span class="count-badge">{{ source.count }}</span>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="section recent-tools">
            <h2>🆕 Recently Added Tools (Latest {{ recent_tools|length }})</h2>
            {% for tool in recent_tools[:20] %}
            <div class="tool-item">
                <div class="tool-main">
                    <div class="tool-name">
                        {% if tool.url %}
                        <a href="{{ tool.url }}" target="_blank" style="color: #667eea; text-decoration: none;">{{ tool.name }}</a>
                        {% else %}
                        {{ tool.name }}
                        {% endif %}
                    </div>
                    <div class="tool-description">{{ tool.description[:150] }}{% if tool.description and tool.description|length > 150 %}...{% endif %}</div>
                    <div class="tool-badges">
                        {% if tool.category %}<span class="badge badge-category">{{ tool.category }}</span>{% endif %}
                        {% if tool.free_tier %}<span class="badge badge-free">Free</span>{% endif %}
                        {% if tool.open_source %}<span class="badge badge-open-source">Open Source</span>{% endif %}
                    </div>
                </div>
                <div class="tool-meta">
                    <div>{{ tool.source_website }}</div>
                    <div>{{ tool.scraped_date[:10] if tool.scraped_date else '' }}</div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="nav-buttons">
            <a href="/tools" class="btn">🔍 Browse All {{ stats.total_tools }} Tools</a>
            <a href="/categories" class="btn btn-secondary">📂 View by Categories</a>
            <a href="/export/csv" class="btn">📥 Export Database</a>
        </div>
    </div>
</body>
</html>
