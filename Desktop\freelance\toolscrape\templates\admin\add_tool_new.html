{% extends "admin/base.html" %}

{% block title %}➕ Add New Tool{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">➕ Add New Tool</h1>
    <p class="page-subtitle">Add a new AI tool to the database</p>
</div>

<div class="form-container">
    <form method="POST" class="tool-form">
        <div class="form-group">
            <label for="name">Tool Name *</label>
            <input type="text" id="name" name="name" required>
            <div class="form-help">Enter the full name of the AI tool</div>
        </div>
        
        <div class="form-group">
            <label for="description">Description</label>
            <textarea id="description" name="description" placeholder="Describe what this tool does and its key features..." rows="4"></textarea>
            <div class="form-help">Provide a detailed description of the tool's functionality</div>
        </div>
        
        <div class="form-group">
            <label for="category">Category</label>
            <select id="category" name="category">
                <option value="">Select a category</option>
                <option value="Writing">Writing</option>
                <option value="Design">Design</option>
                <option value="AI Tools">AI Tools</option>
                <option value="Development">Development</option>
                <option value="Image">Image</option>
                <option value="Video">Video</option>
                <option value="Audio">Audio</option>
                <option value="Marketing">Marketing</option>
                <option value="Business">Business</option>
                <option value="Education">Education</option>
                <option value="Research">Research</option>
                <option value="Other">Other</option>
            </select>
            <div class="form-help">Choose the most appropriate category for this tool</div>
        </div>
        
        <div class="form-group">
            <label for="website_url">Website URL</label>
            <input type="url" id="website_url" name="website_url" placeholder="https://example.com">
            <div class="form-help">The official website or main URL for this tool</div>
        </div>
        
        <div class="form-group">
            <label for="source_website">Source Website</label>
            <input type="text" id="source_website" name="source_website" value="Manual Entry" readonly>
            <div class="form-help">Source where this tool information was obtained</div>
        </div>
        
        <div class="form-group">
            <div class="checkbox-group">
                <input type="checkbox" id="free_tier" name="free_tier" value="1">
                <label for="free_tier">This tool has a free tier or is completely free</label>
            </div>
        </div>
        
        <div class="form-actions">
            <a href="{{ url_for('admin_tools') }}" class="btn btn-secondary">Cancel</a>
            <button type="submit" class="btn btn-primary">➕ Add Tool</button>
        </div>
    </form>
</div>

<style>
/* Form specific styles */
.form-container {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.tool-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.checkbox-group input {
    transform: scale(1.2);
}

.checkbox-group label {
    margin: 0;
    color: #94a3b8;
    font-weight: 500;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 40px;
}

.form-help {
    font-size: 0.9rem;
    color: #94a3b8;
    margin-top: 5px;
}

@media (max-width: 768px) {
    .form-container {
        padding: 25px;
        margin: 0 15px;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}
