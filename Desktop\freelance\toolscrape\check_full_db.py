import sqlite3

# Connect to the master database with consolidated tools
conn = sqlite3.connect('ai_tools_master.db')

print("============================================================")
print("AI TOOLS DATABASE - MASTER CONSOLIDATED VERSION REPORT")
print("============================================================")
print(f"Generated on: 2025-07-14")

# Get basic statistics
total_tools = conn.execute('SELECT COUNT(*) FROM ai_tools').fetchone()[0]
free_tools = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE free_tier = 1').fetchone()[0]
tools_with_urls = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE website_url IS NOT NULL AND website_url != ""').fetchone()[0]

print(f"\nOVERVIEW:")
print(f"  Total AI Tools: {total_tools}")
print(f"  Free Tools: {free_tools}")
print(f"  Tools with URLs: {tools_with_urls}")

# Get category breakdown
print(f"\nTOOLS BY CATEGORY:")
for row in conn.execute('SELECT category, COUNT(*) FROM ai_tools WHERE category IS NOT NULL GROUP BY category ORDER BY COUNT(*) DESC').fetchall():
    print(f"  {row[0]}: {row[1]}")

# Get source breakdown
print(f"\nTOOLS BY SOURCE:")
for row in conn.execute('SELECT source_website, COUNT(*) FROM ai_tools GROUP BY source_website ORDER BY COUNT(*) DESC').fetchall():
    print(f"  {row[0]}: {row[1]}")

# Get sample tools
print(f"\nSAMPLE TOOLS:")
for row in conn.execute('SELECT name, category, source_website, free_tier FROM ai_tools LIMIT 10').fetchall():
    free_status = "Free" if row[3] else "Paid"
    print(f"  {row[0]} - {row[1]} - {row[2]} - {free_status}")

conn.close()
print("\n============================================================")
