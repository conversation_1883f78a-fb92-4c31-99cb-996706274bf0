"""
Master Database Consolidator
Consolidates all AI tools data into a single, clean, deduplicated database
"""

import sqlite3
import pandas as pd
from datetime import datetime
import re
import logging
from difflib import SequenceMatcher
import os

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseConsolidator:
    def __init__(self):
        self.master_db = 'ai_tools_master.db'
        self.all_tools = []
        self.duplicate_count = 0
        self.processed_count = 0
        
    def similarity(self, a, b):
        """Calculate similarity between two strings"""
        return SequenceMatcher(None, a.lower(), b.lower()).ratio()
    
    def normalize_category(self, category):
        """Normalize category names"""
        if not category:
            return "Other"
        
        category = str(category).strip().title()
        
        # Category mapping for consistency
        category_map = {
            "Ai Tools": "AI Tools",
            "Artificial Intelligence": "AI Tools", 
            "Machine Learning": "AI Tools",
            "Text": "Writing",
            "Content": "Writing",
            "Content Creation": "Writing",
            "Copywriting": "Writing",
            "Text Generation": "Writing",
            "Image Generation": "Image",
            "Photo": "Image",
            "Graphics": "Design",
            "Graphic Design": "Design",
            "UI/UX": "Design",
            "Video Generation": "Video",
            "Video Editing": "Video",
            "Audio Generation": "Audio",
            "Music": "Audio",
            "Voice": "Audio",
            "Speech": "Audio",
            "Code": "Development",
            "Programming": "Development",
            "Developer Tools": "Development",
            "Coding": "Development",
            "Software": "Development",
            "Automation": "Productivity",
            "Business Tools": "Business",
            "Marketing Tools": "Marketing",
            "SEO": "Marketing",
            "Social Media": "Marketing",
            "Analytics": "Business",
            "Data": "Business",
            "Education": "Education",
            "Learning": "Education",
            "Research": "Education",
            "Health": "Healthcare",
            "Medical": "Healthcare",
            "Finance": "Finance",
            "E-commerce": "Business",
            "Sales": "Business"
        }
        
        return category_map.get(category, category)
    
    def normalize_pricing(self, pricing):
        """Normalize pricing model"""
        if not pricing:
            return "Unknown"
        
        pricing = str(pricing).lower().strip()
        
        if any(word in pricing for word in ['free', 'gratis', 'no cost', 'open source']):
            return "Free"
        elif any(word in pricing for word in ['premium', 'paid', 'subscription', 'pro']):
            return "Premium"
        elif any(word in pricing for word in ['freemium', 'free trial', 'trial']):
            return "Freemium"
        else:
            return "Unknown"
    
    def is_duplicate(self, new_tool, existing_tools):
        """Check if a tool is a duplicate based on name and URL similarity"""
        new_name = str(new_tool.get('name', '')).lower().strip()
        new_url = str(new_tool.get('website_url', '')).lower().strip()
        
        if not new_name:
            return True  # Skip tools without names
        
        for existing in existing_tools:
            existing_name = str(existing.get('name', '')).lower().strip()
            existing_url = str(existing.get('website_url', '')).lower().strip()
            
            # Check name similarity
            name_similarity = self.similarity(new_name, existing_name)
            
            # Check URL similarity (if both have URLs)
            url_similarity = 0
            if new_url and existing_url:
                url_similarity = self.similarity(new_url, existing_url)
            
            # Consider duplicate if:
            # 1. Names are very similar (>85%) OR
            # 2. URLs are very similar (>90%) OR
            # 3. Names are somewhat similar (>70%) AND URLs are similar (>70%)
            if (name_similarity > 0.85 or 
                url_similarity > 0.90 or 
                (name_similarity > 0.70 and url_similarity > 0.70)):
                
                logger.info(f"Duplicate found: '{new_name}' similar to '{existing_name}' (name: {name_similarity:.2f}, url: {url_similarity:.2f})")
                return True
        
        return False
    
    def load_database(self, db_path, source_name):
        """Load tools from a database"""
        logger.info(f"Loading data from {db_path}...")
        
        if not os.path.exists(db_path):
            logger.warning(f"Database {db_path} not found, skipping...")
            return []
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Get table schema to handle different column names
            cursor.execute("PRAGMA table_info(ai_tools)")
            columns = [col[1] for col in cursor.fetchall()]
            
            query = "SELECT * FROM ai_tools"
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            tools = []
            for _, row in df.iterrows():
                tool = {
                    'name': row.get('name', ''),
                    'description': row.get('description', ''),
                    'website_url': row.get('website_url', row.get('url', '')),
                    'category': self.normalize_category(row.get('category', '')),
                    'pricing_model': self.normalize_pricing(row.get('pricing_model', row.get('pricing', ''))),
                    'free_tier': bool(row.get('free_tier', False)),
                    'source_website': row.get('source_website', source_name),
                    'source_db': source_name,
                    'created_at': row.get('created_at', row.get('scraped_date', datetime.now().isoformat())),
                    'tags': row.get('tags', ''),
                    'features': row.get('features', ''),
                    'rating': row.get('rating', None),
                    'api_available': bool(row.get('api_available', False)),
                    'trial_available': bool(row.get('trial_available', False))
                }
                tools.append(tool)
            
            logger.info(f"Loaded {len(tools)} tools from {db_path}")
            return tools
            
        except Exception as e:
            logger.error(f"Error loading {db_path}: {e}")
            return []
    
    def load_csv_files(self):
        """Load tools from large CSV files"""
        csv_files = [
            'ultra_aggressive_scrape_results_20250713_204249.csv',
            'massive_scrape_results_20250713_200109.csv',
            'comprehensive_scrape_results_20250713_201247.csv'
        ]
        
        all_csv_tools = []
        
        for csv_file in csv_files:
            if os.path.exists(csv_file):
                logger.info(f"Loading data from {csv_file}...")
                try:
                    df = pd.read_csv(csv_file)
                    
                    for _, row in df.iterrows():
                        tool = {
                            'name': row.get('name', ''),
                            'description': row.get('description', ''),
                            'website_url': row.get('website_url', row.get('url', '')),
                            'category': self.normalize_category(row.get('category', '')),
                            'pricing_model': self.normalize_pricing(row.get('pricing_model', '')),
                            'free_tier': bool(row.get('free_tier', False)),
                            'source_website': row.get('source_website', 'csv_import'),
                            'source_db': f'csv_{csv_file}',
                            'created_at': row.get('created_at', datetime.now().isoformat()),
                            'tags': row.get('tags', ''),
                            'features': row.get('features', ''),
                            'rating': row.get('rating', None),
                            'api_available': bool(row.get('api_available', False)),
                            'trial_available': bool(row.get('trial_available', False))
                        }
                        all_csv_tools.append(tool)
                    
                    logger.info(f"Loaded {len(df)} tools from {csv_file}")
                    
                except Exception as e:
                    logger.error(f"Error loading {csv_file}: {e}")
        
        return all_csv_tools
    
    def create_master_database(self):
        """Create the master database schema"""
        conn = sqlite3.connect(self.master_db)
        cursor = conn.cursor()
        
        # Drop existing table if it exists
        cursor.execute('DROP TABLE IF EXISTS ai_tools')
        cursor.execute('DROP TABLE IF EXISTS consolidation_stats')
        
        # Create main table with all necessary columns
        cursor.execute('''
            CREATE TABLE ai_tools (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                website_url TEXT,
                category TEXT,
                subcategory TEXT,
                pricing_model TEXT,
                free_tier BOOLEAN DEFAULT 0,
                trial_available BOOLEAN DEFAULT 0,
                api_available BOOLEAN DEFAULT 0,
                rating REAL,
                features TEXT,
                tags TEXT,
                use_cases TEXT,
                source_website TEXT,
                source_db TEXT,
                created_at TIMESTAMP,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_verified BOOLEAN DEFAULT 0,
                popularity_score INTEGER DEFAULT 0
            )
        ''')
        
        # Create consolidation stats table
        cursor.execute('''
            CREATE TABLE consolidation_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                total_tools_processed INTEGER,
                duplicates_removed INTEGER,
                final_tool_count INTEGER,
                sources_consolidated INTEGER,
                consolidation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX idx_name ON ai_tools(name)')
        cursor.execute('CREATE INDEX idx_category ON ai_tools(category)')
        cursor.execute('CREATE INDEX idx_source ON ai_tools(source_website)')
        cursor.execute('CREATE INDEX idx_url ON ai_tools(website_url)')
        
        conn.commit()
        conn.close()
        logger.info("Master database schema created")
    
    def consolidate_all_data(self):
        """Main consolidation function"""
        logger.info("Starting database consolidation...")
        
        # Create master database
        self.create_master_database()
        
        # Load data from all sources
        all_tools = []
        
        # Load from existing databases
        db_sources = [
            ('ai_tools_database_fixed.db', 'fixed_db'),
            ('ai_tools_database.db', 'original_db')
        ]
        
        for db_path, source_name in db_sources:
            tools = self.load_database(db_path, source_name)
            all_tools.extend(tools)
        
        # Load from CSV files
        csv_tools = self.load_csv_files()
        all_tools.extend(csv_tools)
        
        logger.info(f"Total tools loaded: {len(all_tools)}")
        
        # Deduplicate and insert into master database
        self.deduplicate_and_insert(all_tools)
        
        # Generate final report
        self.generate_report()
    
    def deduplicate_and_insert(self, all_tools):
        """Remove duplicates and insert into master database"""
        logger.info("Starting deduplication process...")
        
        conn = sqlite3.connect(self.master_db)
        cursor = conn.cursor()
        
        unique_tools = []
        
        for i, tool in enumerate(all_tools):
            self.processed_count += 1
            
            if self.processed_count % 100 == 0:
                logger.info(f"Processed {self.processed_count}/{len(all_tools)} tools...")
            
            if not self.is_duplicate(tool, unique_tools):
                unique_tools.append(tool)
                
                # Insert into database
                cursor.execute('''
                    INSERT INTO ai_tools (
                        name, description, website_url, category, pricing_model,
                        free_tier, trial_available, api_available, rating,
                        features, tags, source_website, source_db, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    tool['name'], tool['description'], tool['website_url'],
                    tool['category'], tool['pricing_model'], tool['free_tier'],
                    tool['trial_available'], tool['api_available'], tool['rating'],
                    tool['features'], tool['tags'], tool['source_website'],
                    tool['source_db'], tool['created_at']
                ))
            else:
                self.duplicate_count += 1
        
        # Insert consolidation stats
        cursor.execute('''
            INSERT INTO consolidation_stats (
                total_tools_processed, duplicates_removed, final_tool_count, sources_consolidated
            ) VALUES (?, ?, ?, ?)
        ''', (len(all_tools), self.duplicate_count, len(unique_tools), len(set(t['source_db'] for t in all_tools))))
        
        conn.commit()
        conn.close()
        
        logger.info(f"Deduplication complete. Removed {self.duplicate_count} duplicates")
        logger.info(f"Final database contains {len(unique_tools)} unique tools")
    
    def generate_report(self):
        """Generate final consolidation report"""
        conn = sqlite3.connect(self.master_db)
        
        # Basic stats
        total_tools = conn.execute('SELECT COUNT(*) FROM ai_tools').fetchone()[0]
        free_tools = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE free_tier = 1').fetchone()[0]
        tools_with_urls = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE website_url IS NOT NULL AND website_url != ""').fetchone()[0]
        
        print("\n" + "="*60)
        print("MASTER DATABASE CONSOLIDATION REPORT")
        print("="*60)
        print(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"\nCONSOLIDATION SUMMARY:")
        print(f"  Total tools processed: {self.processed_count}")
        print(f"  Duplicates removed: {self.duplicate_count}")
        print(f"  Final unique tools: {total_tools}")
        print(f"  Free tools: {free_tools}")
        print(f"  Tools with URLs: {tools_with_urls}")
        print(f"  Deduplication rate: {(self.duplicate_count/self.processed_count)*100:.1f}%")
        
        # Category breakdown
        print(f"\nTOOLS BY CATEGORY:")
        for row in conn.execute('SELECT category, COUNT(*) FROM ai_tools GROUP BY category ORDER BY COUNT(*) DESC').fetchall():
            print(f"  {row[0]}: {row[1]}")
        
        # Source breakdown
        print(f"\nTOOLS BY SOURCE:")
        for row in conn.execute('SELECT source_website, COUNT(*) FROM ai_tools GROUP BY source_website ORDER BY COUNT(*) DESC LIMIT 15').fetchall():
            print(f"  {row[0]}: {row[1]}")
        
        conn.close()
        print("="*60)
        print(f"Master database saved as: {self.master_db}")
        print("="*60)

if __name__ == "__main__":
    consolidator = DatabaseConsolidator()
    consolidator.consolidate_all_data()
