<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 API Scraping Results - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
            color: #e2e8f0;
        }
        
        .admin-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px 0;
        }
        
        .header-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-logo {
            font-size: 1.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .admin-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .nav-link {
            color: #94a3b8;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }
        
        .logout-btn {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-header {
            margin-bottom: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .page-subtitle {
            color: #94a3b8;
            font-size: 1.1rem;
            margin-top: 10px;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
            color: white;
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #e2e8f0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        
        .config-info {
            background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
            padding: 30px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 30px;
        }
        
        .config-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .meta-item {
            text-align: center;
        }
        
        .meta-value {
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 5px;
        }
        
        .meta-label {
            color: #94a3b8;
            font-weight: 500;
        }
        
        .content-section {
            background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
            padding: 30px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .results-table th,
        .results-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .results-table th {
            background: rgba(59, 130, 246, 0.1);
            font-weight: 600;
            color: #3b82f6;
        }
        
        .results-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-completed {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
        }
        
        .status-failed {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }
        
        .status-running {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .tool-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .tool-card:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
        }
        
        .tool-name {
            font-weight: 600;
            margin-bottom: 10px;
            color: #e2e8f0;
        }
        
        .tool-description {
            color: #94a3b8;
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 15px;
        }
        
        .tool-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
        }
        
        .tool-category {
            background: rgba(139, 92, 246, 0.2);
            color: #8b5cf6;
            padding: 2px 8px;
            border-radius: 4px;
        }
        
        .tool-url {
            color: #3b82f6;
            text-decoration: none;
        }
        
        .tool-url:hover {
            text-decoration: underline;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }
        
        .pagination a {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.1);
            color: #e2e8f0;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .pagination a:hover,
        .pagination a.active {
            background: #3b82f6;
            color: white;
        }
        
        .flash-messages {
            margin-bottom: 30px;
        }
        
        .flash-message {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .flash-success {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }
        
        .flash-error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #94a3b8;
        }
        
        .empty-state h3 {
            margin-bottom: 10px;
            color: #e2e8f0;
        }
        
        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            
            .action-buttons {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .config-meta {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .results-table {
                font-size: 0.9rem;
            }
            
            .results-table th,
            .results-table td {
                padding: 10px;
            }
            
            .tools-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="header-content">
            <div class="admin-logo">📊 API Results</div>
            <nav class="admin-nav">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-link">Dashboard</a>
                <a href="{{ url_for('admin_tools') }}" class="nav-link">Tools</a>
                <a href="{{ url_for('admin_scraping') }}" class="nav-link">Scraping</a>
                <a href="{{ url_for('admin_api_scraping') }}" class="nav-link active">API Scraping</a>
                <a href="{{ url_for('admin_publishing') }}" class="nav-link">Publishing</a>
                <a href="{{ url_for('admin_analytics') }}" class="nav-link">Analytics</a>
                <a href="{{ url_for('admin_logout') }}" class="logout-btn">Logout</a>
            </nav>
        </div>
    </header>

    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages">
                    {% for category, message in messages %}
                        <div class="flash-message flash-{{ 'success' if category == 'success' else 'error' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <div class="page-header">
            <div>
                <h1 class="page-title">📊 API Scraping Results</h1>
                <p class="page-subtitle">Results for "{{ config.config_name }}"</p>
            </div>
            <div class="action-buttons">
                <a href="{{ url_for('admin_api_scraping') }}" class="btn btn-secondary">
                    ← Back to API Scraping
                </a>
                <a href="{{ url_for('admin_edit_api_config', config_id=config.id) }}" class="btn btn-primary">
                    ✏️ Edit Configuration
                </a>
                <button onclick="runConfiguration()" class="btn btn-success">
                    ▶️ Run Now
                </button>
            </div>
        </div>

        <!-- Configuration Overview -->
        <div class="config-info">
            <h2 style="margin-bottom: 25px; color: #3b82f6;">🔧 Configuration Overview</h2>
            <div class="config-meta">
                <div class="meta-item">
                    <div class="meta-value">{{ results|length }}</div>
                    <div class="meta-label">Total Executions</div>
                </div>
                <div class="meta-item">
                    <div class="meta-value">{{ results|selectattr('status', 'equalto', 'completed')|list|length }}</div>
                    <div class="meta-label">Successful Runs</div>
                </div>
                <div class="meta-item">
                    <div class="meta-value">{{ tools|length }}</div>
                    <div class="meta-label">Tools Discovered</div>
                </div>
                <div class="meta-item">
                    <div class="meta-value">{{ config.api_type.upper() }}</div>
                    <div class="meta-label">API Type</div>
                </div>
                <div class="meta-item">
                    <div class="meta-value">{{ config.rate_limit }}s</div>
                    <div class="meta-label">Rate Limit</div>
                </div>
                <div class="meta-item">
                    <div class="meta-value">{{ 'Active' if config.is_active else 'Inactive' }}</div>
                    <div class="meta-label">Status</div>
                </div>
            </div>
        </div>

        <!-- Execution Results -->
        <div class="content-section">
            <h2 class="section-title">📈 Execution History</h2>
            
            {% if results %}
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>Started</th>
                            <th>Status</th>
                            <th>Tools Found</th>
                            <th>Execution Time</th>
                            <th>Error Message</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for result in results %}
                        <tr>
                            <td>{{ result.started_at[:19] if result.started_at else 'Unknown' }}</td>
                            <td>
                                <span class="status-badge status-{{ result.status }}">
                                    {{ result.status.title() }}
                                </span>
                            </td>
                            <td>
                                <strong>{{ result.tools_found or 0 }}</strong>
                            </td>
                            <td>
                                {% if result.execution_time %}
                                    {{ "%.2f"|format(result.execution_time) }}s
                                {% else %}
                                    <span style="color: #94a3b8;">N/A</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if result.error_message %}
                                    <span style="color: #ef4444; font-size: 0.9rem;">
                                        {{ result.error_message[:100] }}{% if result.error_message|length > 100 %}...{% endif %}
                                    </span>
                                {% else %}
                                    <span style="color: #94a3b8;">-</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="empty-state">
                    <h3>No execution results yet</h3>
                    <p>This configuration hasn't been run yet. Click "Run Now" to start scraping!</p>
                </div>
            {% endif %}
        </div>

        <!-- Discovered Tools -->
        {% if tools %}
        <div class="content-section">
            <h2 class="section-title">🔍 Discovered Tools ({{ tools|length }})</h2>
            
            <div class="tools-grid">
                {% for tool in tools[:20] %}
                <div class="tool-card">
                    <div class="tool-name">{{ tool.name }}</div>
                    <div class="tool-description">
                        {{ tool.description[:150] }}{% if tool.description|length > 150 %}...{% endif %}
                    </div>
                    <div class="tool-meta">
                        <span class="tool-category">{{ tool.category }}</span>
                        {% if tool.url %}
                            <a href="{{ tool.url }}" target="_blank" class="tool-url">Visit →</a>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
            
            {% if tools|length > 20 %}
                <div style="text-align: center; margin-top: 30px; color: #94a3b8;">
                    Showing first 20 tools of {{ tools|length }} total. 
                    <a href="{{ url_for('admin_tools') }}?source=API-{{ config.id }}" style="color: #3b82f6;">View all tools →</a>
                </div>
            {% endif %}
        </div>
        {% else %}
        <div class="content-section">
            <div class="empty-state">
                <h3>No tools discovered yet</h3>
                <p>Run this configuration to start discovering AI tools from the API source</p>
                <button onclick="runConfiguration()" class="btn btn-success" style="margin-top: 20px;">
                    ▶️ Run Configuration
                </button>
            </div>
        </div>
        {% endif %}
    </div>

    <script>
        function runConfiguration() {
            if (!confirm('Run this API configuration now?')) {
                return;
            }

            const configId = {{ config.id }};
            
            // Show loading state
            document.body.style.opacity = '0.6';
            document.body.style.pointerEvents = 'none';
            
            fetch(`/admin/api-scraping/run/${configId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                document.body.style.opacity = '1';
                document.body.style.pointerEvents = 'auto';
                
                if (data.success) {
                    alert(`Scraping completed! Found ${data.tools_found} tools in ${data.execution_time.toFixed(2)} seconds.`);
                    location.reload();
                } else {
                    alert(`Error: ${data.error}`);
                }
            })
            .catch(error => {
                document.body.style.opacity = '1';
                document.body.style.pointerEvents = 'auto';
                alert(`Error: ${error.message}`);
            });
        }

        // Auto-refresh every 30 seconds to show real-time updates
        setInterval(() => {
            if (!document.body.style.pointerEvents || document.body.style.pointerEvents !== 'none') {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
