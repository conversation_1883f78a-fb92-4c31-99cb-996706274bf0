<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Tools Database</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        
        .section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }
        
        .recent-tools {
            grid-column: 1 / -1;
        }
        
        .tool-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .tool-item:last-child {
            border-bottom: none;
        }
        
        .tool-name {
            font-weight: bold;
            color: #667eea;
        }
        
        .tool-description {
            color: #666;
            font-size: 0.9rem;
            margin-top: 2px;
        }
        
        .tool-meta {
            text-align: right;
            font-size: 0.8rem;
            color: #999;
        }
        
        .category-item, .source-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .category-item:last-child, .source-item:last-child {
            border-bottom: none;
        }
        
        .count-badge {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .nav-buttons {
            text-align: center;
            margin-top: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 0 10px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #764ba2;
        }
        
        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI Tools Database</h1>
            <p>Comprehensive collection of AI tools from across the web</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_tools }}</div>
                <div class="stat-label">Total AI Tools</div>
            </div>
        </div>
        
        <div class="content-grid">
            <div class="section">
                <h2>📊 Top Categories</h2>
                {% for category in categories %}
                <div class="category-item">
                    <span>{{ category.category }}</span>
                    <span class="count-badge">{{ category.count }}</span>
                </div>
                {% endfor %}
            </div>
            
            <div class="section">
                <h2>🌐 Sources</h2>
                {% for source in sources %}
                <div class="source-item">
                    <span>{{ source.source_website }}</span>
                    <span class="count-badge">{{ source.count }}</span>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="section recent-tools">
            <h2>🆕 Recently Added Tools</h2>
            {% for tool in recent_tools %}
            <div class="tool-item">
                <div>
                    <div class="tool-name">{{ tool.name }}</div>
                    <div class="tool-description">{{ tool.description[:100] }}{% if tool.description|length > 100 %}...{% endif %}</div>
                </div>
                <div class="tool-meta">
                    <div>{{ tool.category }}</div>
                    <div>{{ tool.source_website }}</div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="nav-buttons">
            <a href="/tools" class="btn">🔍 Browse All Tools</a>
            <a href="/export/csv" class="btn">📥 Export CSV</a>
        </div>
    </div>
</body>
</html>
