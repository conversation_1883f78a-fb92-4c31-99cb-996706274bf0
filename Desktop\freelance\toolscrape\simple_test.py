#!/usr/bin/env python3
import sys
import os

try:
    # Import the admin dashboard
    from admin_dashboard import app
    
    print("✅ Import successful!")
    
    # Check routes
    routes = {}
    for rule in app.url_map.iter_rules():
        routes[rule.endpoint] = rule.rule
    
    print(f"Total routes: {len(routes)}")
    
    # Check API scraping specifically
    if 'admin_api_scraping' in routes:
        print(f"✅ admin_api_scraping route found: {routes['admin_api_scraping']}")
        
        # Test the route
        with app.test_client() as client:
            response = client.get('/admin/api-scraping')
            print(f"Route test result: {response.status_code}")
    else:
        print("❌ admin_api_scraping route NOT found!")
        
        # Show similar routes
        similar_routes = {k: v for k, v in routes.items() if 'scraping' in k}
        print(f"Routes with 'scraping': {similar_routes}")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("Test complete.")
