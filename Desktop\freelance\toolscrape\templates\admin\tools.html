<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Tools Management - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
            color: #e2e8f0;
        }
        
        .admin-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px 0;
        }
        
        .header-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-logo {
            font-size: 1.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .admin-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .nav-link {
            color: #94a3b8;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }
        
        .logout-btn {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #f59e0b, #d97706);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
        }
        
        .filters-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .filters-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .form-group label {
            font-weight: 600;
            color: #cbd5e1;
            font-size: 0.9rem;
        }
        
        .form-control {
            padding: 12px 16px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.05);
            color: #e2e8f0;
            font-size: 14px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .bulk-actions {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px 20px;
            margin-bottom: 20px;
            display: none;
            align-items: center;
            gap: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .bulk-actions.show {
            display: flex;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 800;
            color: #3b82f6;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #94a3b8;
            margin-top: 5px;
        }
        
        .tools-table {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px 15px;
            text-align: left;
            font-weight: 600;
            color: #e2e8f0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .table td {
            padding: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            color: #cbd5e1;
        }
        
        .table tr:hover {
            background: rgba(255, 255, 255, 0.02);
        }
        
        .tool-name {
            font-weight: 600;
            color: #e2e8f0;
        }
        
        .tool-url {
            color: #3b82f6;
            text-decoration: none;
        }
        
        .tool-url:hover {
            text-decoration: underline;
        }
        
        .badge {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .badge-free {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
        }
        
        .badge-verified {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }
        
        .badge-premium {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
        }
        
        .actions {
            display: flex;
            gap: 8px;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 30px;
        }
        
        .pagination a, .pagination span {
            padding: 8px 12px;
            border-radius: 6px;
            text-decoration: none;
            color: #cbd5e1;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .pagination a:hover, .pagination .current {
            background: #3b82f6;
            color: white;
        }
        
        .checkbox {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }
        
        .sort-link {
            color: #e2e8f0;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .sort-link:hover {
            color: #3b82f6;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #94a3b8;
        }
        
        @media (max-width: 768px) {
            .filters-row {
                grid-template-columns: 1fr;
            }
            
            .page-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .header-actions {
                justify-content: center;
            }
            
            .table {
                font-size: 0.9rem;
            }
            
            .table th, .table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="header-content">
            <div class="admin-logo">🔧 AI Tools Admin</div>
            <nav class="admin-nav">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-link">Dashboard</a>
                <a href="{{ url_for('admin_tools') }}" class="nav-link active">Tools</a>
                <a href="{{ url_for('admin_scraping') }}" class="nav-link">Scraping</a>
                <a href="{{ url_for('admin_publishing') }}" class="nav-link">Publishing</a>
                <a href="{{ url_for('admin_analytics') }}" class="nav-link">Analytics</a>
                <a href="{{ url_for('admin_logout') }}" class="logout-btn">Logout</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="page-header">
            <h1 class="page-title">Tools Management</h1>
            <div class="header-actions">
                <a href="{{ url_for('admin_add_tool') }}" class="btn btn-primary">
                    <span>➕</span> Add Tool
                </a>
                <a href="{{ url_for('admin_import_export') }}" class="btn btn-success">
                    <span>📊</span> Import/Export
                </a>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{{ filter_stats.total }}</div>
                <div class="stat-label">Total Tools</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ filter_stats.free }}</div>
                <div class="stat-label">Free Tools</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ filter_stats.verified }}</div>
                <div class="stat-label">Verified</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ filter_stats.with_url }}</div>
                <div class="stat-label">With URLs</div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section">
            <form method="GET" id="filtersForm">
                <div class="filters-row">
                    <div class="form-group">
                        <label for="search">Search</label>
                        <input type="text" id="search" name="search" class="form-control" 
                               value="{{ search }}" placeholder="Search tools...">
                    </div>
                    <div class="form-group">
                        <label for="category">Category</label>
                        <select id="category" name="category" class="form-control">
                            <option value="">All Categories</option>
                            {% for cat in categories %}
                            <option value="{{ cat.category }}" {% if category == cat.category %}selected{% endif %}>
                                {{ cat.category }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="sort_by">Sort By</label>
                        <select id="sort_by" name="sort_by" class="form-control">
                            <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Name</option>
                            <option value="created_at" {% if sort_by == 'created_at' %}selected{% endif %}>Date Added</option>
                            <option value="category" {% if sort_by == 'category' %}selected{% endif %}>Category</option>
                            <option value="rating" {% if sort_by == 'rating' %}selected{% endif %}>Rating</option>
                            <option value="popularity_score" {% if sort_by == 'popularity_score' %}selected{% endif %}>Popularity</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="sort_order">Order</label>
                        <select id="sort_order" name="sort_order" class="form-control">
                            <option value="asc" {% if sort_order == 'asc' %}selected{% endif %}>Ascending</option>
                            <option value="desc" {% if sort_order == 'desc' %}selected{% endif %}>Descending</option>
                        </select>
                    </div>
                </div>
                <div class="filters-row">
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="free_only" value="1" {% if free_only %}checked{% endif %}>
                            Free Tools Only
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="verified_only" value="1" {% if verified_only %}checked{% endif %}>
                            Verified Only
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="has_url" value="1" {% if has_url %}checked{% endif %}>
                            Has Website URL
                        </label>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Apply Filters</button>
                        <a href="{{ url_for('admin_tools') }}" class="btn btn-secondary">Clear</a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Bulk Actions -->
        <div class="bulk-actions" id="bulkActions">
            <form method="POST" action="{{ url_for('admin_bulk_operations') }}" id="bulkForm">
                <span><span id="selectedCount">0</span> tools selected</span>
                <select name="bulk_action" class="form-control" style="width: auto;">
                    <option value="">Choose Action...</option>
                    <option value="delete">Delete Selected</option>
                    <option value="verify">Mark as Verified</option>
                    <option value="publish">Add to Publishing Queue</option>
                    <option value="category_update">Update Category</option>
                </select>
                <input type="text" name="new_category" class="form-control" 
                       placeholder="New category..." style="width: auto; display: none;" id="newCategoryInput">
                <button type="submit" class="btn btn-warning btn-sm">Apply</button>
                <button type="button" class="btn btn-secondary btn-sm" onclick="clearSelection()">Cancel</button>
            </form>
        </div>

        <!-- Tools Table -->
        <div class="tools-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" class="checkbox" id="selectAll">
                        </th>
                        <th>
                            <a href="{{ url_for('admin_tools', sort_by='name', sort_order='asc' if sort_by != 'name' or sort_order == 'desc' else 'desc', 
                                              search=search, category=category, free_only=free_only, verified_only=verified_only, has_url=has_url) }}" 
                               class="sort-link">
                                Name 
                                {% if sort_by == 'name' %}
                                    <span>{{ '↑' if sort_order == 'asc' else '↓' }}</span>
                                {% endif %}
                            </a>
                        </th>
                        <th>Category</th>
                        <th>Website</th>
                        <th>Status</th>
                        <th>
                            <a href="{{ url_for('admin_tools', sort_by='created_at', sort_order='asc' if sort_by != 'created_at' or sort_order == 'desc' else 'desc',
                                              search=search, category=category, free_only=free_only, verified_only=verified_only, has_url=has_url) }}" 
                               class="sort-link">
                                Added 
                                {% if sort_by == 'created_at' %}
                                    <span>{{ '↑' if sort_order == 'asc' else '↓' }}</span>
                                {% endif %}
                            </a>
                        </th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for tool in tools %}
                    <tr>
                        <td>
                            <input type="checkbox" class="checkbox tool-checkbox" name="selected_tools" value="{{ tool.id }}">
                        </td>
                        <td>
                            <div class="tool-name">{{ tool.name or 'Unnamed Tool' }}</div>
                            {% if tool.description %}
                            <div style="font-size: 0.8rem; color: #94a3b8; margin-top: 2px;">
                                {{ tool.description[:100] }}{% if tool.description|length > 100 %}...{% endif %}
                            </div>
                            {% endif %}
                        </td>
                        <td>{{ tool.category or 'Uncategorized' }}</td>
                        <td>
                            {% if tool.website_url %}
                            <a href="{{ tool.website_url }}" target="_blank" class="tool-url">Visit</a>
                            {% else %}
                            <span style="color: #6b7280;">No URL</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if tool.free_tier %}
                            <span class="badge badge-free">Free</span>
                            {% else %}
                            <span class="badge badge-premium">Premium</span>
                            {% endif %}
                            {% if tool.is_verified %}
                            <span class="badge badge-verified">Verified</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if tool.created_at %}
                            {{ tool.created_at.split(' ')[0] if ' ' in tool.created_at else tool.created_at }}
                            {% else %}
                            Unknown
                            {% endif %}
                        </td>
                        <td>
                            <div class="actions">
                                <a href="{{ url_for('admin_edit_tool', tool_id=tool.id) }}" class="btn btn-primary btn-sm">Edit</a>
                                <form method="POST" action="{{ url_for('admin_delete_tool', tool_id=tool.id) }}" 
                                      style="display: inline;" onsubmit="return confirm('Are you sure?')">
                                    <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if total_pages > 1 %}
        <div class="pagination">
            {% if page > 1 %}
            <a href="{{ url_for('admin_tools', page=page-1, search=search, category=category, 
                              free_only=free_only, verified_only=verified_only, has_url=has_url,
                              sort_by=sort_by, sort_order=sort_order) }}">« Previous</a>
            {% endif %}
            
            {% for p in range(1, total_pages + 1) %}
                {% if p == page %}
                <span class="current">{{ p }}</span>
                {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 2 and p <= page + 2) %}
                <a href="{{ url_for('admin_tools', page=p, search=search, category=category,
                                  free_only=free_only, verified_only=verified_only, has_url=has_url,
                                  sort_by=sort_by, sort_order=sort_order) }}">{{ p }}</a>
                {% elif p == 4 or p == total_pages - 3 %}
                <span>...</span>
                {% endif %}
            {% endfor %}
            
            {% if page < total_pages %}
            <a href="{{ url_for('admin_tools', page=page+1, search=search, category=category,
                              free_only=free_only, verified_only=verified_only, has_url=has_url,
                              sort_by=sort_by, sort_order=sort_order) }}">Next »</a>
            {% endif %}
        </div>
        {% endif %}
    </div>

    <script>
        // Bulk selection functionality
        const selectAllCheckbox = document.getElementById('selectAll');
        const toolCheckboxes = document.querySelectorAll('.tool-checkbox');
        const bulkActions = document.getElementById('bulkActions');
        const selectedCount = document.getElementById('selectedCount');
        const bulkForm = document.getElementById('bulkForm');
        const newCategoryInput = document.getElementById('newCategoryInput');

        function updateBulkActions() {
            const selectedTools = document.querySelectorAll('.tool-checkbox:checked');
            const count = selectedTools.length;
            
            selectedCount.textContent = count;
            
            if (count > 0) {
                bulkActions.classList.add('show');
                // Add selected tool IDs to form
                selectedTools.forEach(checkbox => {
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'selected_tools';
                    hiddenInput.value = checkbox.value;
                    hiddenInput.className = 'selected-tool-input';
                    bulkForm.appendChild(hiddenInput);
                });
            } else {
                bulkActions.classList.remove('show');
                // Remove existing hidden inputs
                document.querySelectorAll('.selected-tool-input').forEach(input => {
                    input.remove();
                });
            }
        }

        function clearSelection() {
            toolCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            selectAllCheckbox.checked = false;
            updateBulkActions();
        }

        selectAllCheckbox.addEventListener('change', function() {
            toolCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });

        toolCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // Update select all checkbox
                const checkedCount = document.querySelectorAll('.tool-checkbox:checked').length;
                selectAllCheckbox.checked = checkedCount === toolCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < toolCheckboxes.length;
                
                updateBulkActions();
            });
        });

        // Show/hide category input based on selected action
        document.querySelector('select[name="bulk_action"]').addEventListener('change', function() {
            if (this.value === 'category_update') {
                newCategoryInput.style.display = 'inline-block';
                newCategoryInput.required = true;
            } else {
                newCategoryInput.style.display = 'none';
                newCategoryInput.required = false;
            }
        });

        // Auto-submit filters on change
        document.getElementById('filtersForm').addEventListener('change', function() {
            this.submit();
        });

        // Confirm bulk actions
        bulkForm.addEventListener('submit', function(e) {
            const action = document.querySelector('select[name="bulk_action"]').value;
            const count = document.querySelectorAll('.tool-checkbox:checked').length;
            
            if (!action) {
                e.preventDefault();
                alert('Please select an action');
                return;
            }
            
            if (action === 'delete') {
                if (!confirm(`Are you sure you want to delete ${count} tools? This action cannot be undone.`)) {
                    e.preventDefault();
                }
            } else if (action === 'category_update') {
                const newCategory = newCategoryInput.value.trim();
                if (!newCategory) {
                    e.preventDefault();
                    alert('Please enter a new category');
                    return;
                }
                if (!confirm(`Are you sure you want to update the category for ${count} tools to "${newCategory}"?`)) {
                    e.preventDefault();
                }
            } else {
                if (!confirm(`Are you sure you want to apply this action to ${count} tools?`)) {
                    e.preventDefault();
                }
            }
        });
    </script>
</body>
</html>
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #e2e8f0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .filters-section {
            background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }
        
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
        }
        
        .filter-group label {
            color: #94a3b8;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .filter-group input, .filter-group select {
            padding: 10px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
            color: #e2e8f0;
            font-size: 1rem;
        }
        
        .filter-group input:focus, .filter-group select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .tools-table {
            background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
            border-radius: 20px;
            overflow: hidden;
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }
        
        .table-header {
            background: rgba(59, 130, 246, 0.1);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .table-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #f1f5f9;
        }
        
        .table-info {
            color: #94a3b8;
            font-size: 0.9rem;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        th {
            background: rgba(255, 255, 255, 0.05);
            font-weight: 600;
            color: #94a3b8;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        th a {
            color: #94a3b8;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        th a:hover {
            color: #3b82f6;
        }
        
        .tool-name {
            font-weight: 600;
            color: #f1f5f9;
            margin-bottom: 5px;
        }
        
        .tool-description {
            color: #94a3b8;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .tool-category {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .tool-free {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .tool-paid {
            background: rgba(249, 115, 22, 0.2);
            color: #f97316;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 30px;
        }
        
        .pagination a {
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            color: #e2e8f0;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .pagination a:hover, .pagination a.active {
            background: #3b82f6;
            color: white;
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .alert-success {
            background: rgba(34, 197, 94, 0.1);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.2);
        }
        
        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }
        
        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                gap: 20px;
                align-items: flex-start;
            }
            
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <div class="header-content">
            <div class="admin-logo">🚀 AI Tools Admin</div>
            <nav class="admin-nav">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-link">Dashboard</a>
                <a href="{{ url_for('admin_tools') }}" class="nav-link active">Tools</a>
                <a href="{{ url_for('admin_scraping') }}" class="nav-link">Scraping</a>
                <a href="{{ url_for('admin_logout') }}" class="logout-btn">Logout</a>
            </nav>
        </div>
    </div>
    
    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'success' if category == 'success' else 'error' }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="page-header">
            <h1 class="page-title">🔧 Tools Management</h1>
            <a href="{{ url_for('admin_add_tool') }}" class="btn btn-primary">+ Add New Tool</a>
        </div>
        
        <div class="filters-section">
            <form method="GET">
                <div class="filters-grid">
                    <div class="filter-group">
                        <label for="search">Search Tools</label>
                        <input type="text" id="search" name="search" value="{{ search }}" placeholder="Search by name or description...">
                    </div>
                    
                    <div class="filter-group">
                        <label for="category">Category</label>
                        <select id="category" name="category">
                            <option value="">All Categories</option>
                            {% for cat in categories %}
                            <option value="{{ cat.category }}" {% if category == cat.category %}selected{% endif %}>
                                {{ cat.category }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="sort_by">Sort By</label>
                        <select id="sort_by" name="sort_by">
                            <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Name</option>
                            <option value="category" {% if sort_by == 'category' %}selected{% endif %}>Category</option>
                            <option value="created_at" {% if sort_by == 'created_at' %}selected{% endif %}>Date Added</option>
                            <option value="free_tier" {% if sort_by == 'free_tier' %}selected{% endif %}>Free/Paid</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="sort_order">Order</label>
                        <select id="sort_order" name="sort_order">
                            <option value="asc" {% if sort_order == 'asc' %}selected{% endif %}>Ascending</option>
                            <option value="desc" {% if sort_order == 'desc' %}selected{% endif %}>Descending</option>
                        </select>
                    </div>
                </div>
                
                <div style="display: flex; gap: 10px; align-items: center;">
                    <label style="display: flex; align-items: center; gap: 5px; color: #94a3b8;">
                        <input type="checkbox" name="free_only" {% if free_only %}checked{% endif %}> Show Free Only
                    </label>
                    <button type="submit" class="btn btn-primary">Apply Filters</button>
                    <a href="{{ url_for('admin_tools') }}" class="btn btn-secondary">Clear</a>
                </div>
            </form>
        </div>
        
        <div class="tools-table">
            <div class="table-header">
                <div class="table-title">AI Tools</div>
                <div class="table-info">{{ total }} tools found</div>
            </div>
            
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>
                                <a href="?sort_by=name&sort_order={{ 'desc' if sort_by == 'name' and sort_order == 'asc' else 'asc' }}">
                                    Tool Info {% if sort_by == 'name' %}{{ '↓' if sort_order == 'desc' else '↑' }}{% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?sort_by=category&sort_order={{ 'desc' if sort_by == 'category' and sort_order == 'asc' else 'asc' }}">
                                    Category {% if sort_by == 'category' %}{{ '↓' if sort_order == 'desc' else '↑' }}{% endif %}
                                </a>
                            </th>
                            <th>Pricing</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for tool in tools %}
                        <tr>
                            <td>
                                <div class="tool-name">{{ tool.name }}</div>
                                <div class="tool-description">
                                    {{ tool.description[:100] }}{% if tool.description|length > 100 %}...{% endif %}
                                </div>
                            </td>
                            <td>
                                {% if tool.category %}
                                <span class="tool-category">{{ tool.category }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if tool.free_tier %}
                                <span class="tool-free">Free</span>
                                {% else %}
                                <span class="tool-paid">Paid</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="{{ url_for('admin_edit_tool', tool_id=tool.id) }}" class="btn btn-secondary btn-sm">Edit</a>
                                    <form method="POST" action="{{ url_for('admin_delete_tool', tool_id=tool.id) }}" style="display: inline;">
                                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this tool?')">Delete</button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="4" style="text-align: center; padding: 40px; color: #94a3b8;">
                                No tools found matching your criteria
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        {% if total_pages > 1 %}
        <div class="pagination">
            {% if page > 1 %}
            <a href="?page={{ page - 1 }}&search={{ search }}&category={{ category }}&sort_by={{ sort_by }}&sort_order={{ sort_order }}">← Previous</a>
            {% endif %}
            
            {% for p in range(1, total_pages + 1) %}
                {% if p == page %}
                <a href="#" class="active">{{ p }}</a>
                {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 1 and p <= page + 1) %}
                <a href="?page={{ p }}&search={{ search }}&category={{ category }}&sort_by={{ sort_by }}&sort_order={{ sort_order }}">{{ p }}</a>
                {% elif p == 4 or p == total_pages - 3 %}
                <span>...</span>
                {% endif %}
            {% endfor %}
            
            {% if page < total_pages %}
            <a href="?page={{ page + 1 }}&search={{ search }}&category={{ category }}&sort_by={{ sort_by }}&sort_order={{ sort_order }}">Next →</a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</body>
</html>
