"""
Test version of admin dashboard with API scraping temporarily disabled
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, session, send_file
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
import os
import requests
from bs4 import BeautifulSoup
import json
import re
from urllib.parse import urljoin, urlparse
import time
import io
import csv
from werkzeug.utils import secure_filename
import hashlib

app = Flask(__name__)
app.secret_key = 'admin_dashboard_secret_key_2025'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

def get_db_connection():
    conn = sqlite3.connect('ai_tools_master.db')
    conn.row_factory = sqlite3.Row
    return conn

def require_auth(f):
    def wrapper(*args, **kwargs):
        if 'admin_logged_in' not in session:
            return redirect(url_for('admin_login'))
        return f(*args, **kwargs)
    wrapper.__name__ = f.__name__
    return wrapper

@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        # Simple hardcoded authentication for now
        if username == 'admin' and password == 'admin123':
            session['admin_logged_in'] = True
            flash('Successfully logged in!', 'success')
            return redirect(url_for('admin_dashboard'))
        else:
            flash('Invalid credentials', 'error')
    
    return render_template('admin/login.html')

@app.route('/admin/dashboard')
@app.route('/admin')
@app.route('/admin/')
@require_auth
def admin_dashboard():
    """Main admin dashboard"""
    return render_template('admin/dashboard.html')

@app.route('/admin/api-scraping')
@require_auth
def admin_api_scraping():
    """API scraping management page - temporarily disabled"""
    return "<h1>API Scraping - Under Development</h1><p>This feature is being debugged.</p>"

if __name__ == '__main__':
    print("Starting minimal admin dashboard...")
    app.run(host='127.0.0.1', port=5001, debug=True)
