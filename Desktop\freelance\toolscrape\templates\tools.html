<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browse AI Tools - Complete Database</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f7fa;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .filters {
            background: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .filters h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1.3rem;
        }
        
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            align-items: end;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
        }
        
        .filter-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        
        .filter-group select,
        .filter-group input {
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .filter-group select:focus,
        .filter-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .checkbox-group {
            display: flex;
            gap: 20px;
            align-items: center;
            margin-top: 10px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .btn-filter {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.3s;
        }
        
        .btn-filter:hover {
            background: #5a6fd8;
        }
        
        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .results-info {
            font-size: 1.1rem;
            color: #666;
        }
        
        .results-info strong {
            color: #333;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .tool-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #667eea;
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .tool-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .tool-name {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .tool-name a {
            color: #667eea;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .tool-name a:hover {
            color: #5a6fd8;
        }
        
        .tool-category {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .tool-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
            font-size: 0.95rem;
        }
        
        .tool-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }
        
        .tool-badges {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .badge {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .badge-free {
            background: #4CAF50;
            color: white;
        }
        
        .badge-open-source {
            background: #FF9800;
            color: white;
        }
        
        .badge-source {
            background: #e9ecef;
            color: #666;
        }
        
        .tool-source {
            font-size: 0.8rem;
            color: #999;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin: 40px 0;
        }
        
        .pagination a,
        .pagination span {
            padding: 10px 15px;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .pagination a {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
        }
        
        .pagination a:hover {
            background: #667eea;
            color: white;
        }
        
        .pagination .current {
            background: #667eea;
            color: white;
            border: 2px solid #667eea;
        }
        
        .pagination .disabled {
            background: #f8f9fa;
            color: #999;
            border: 2px solid #e9ecef;
            cursor: not-allowed;
        }
        
        .back-btn {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            margin-bottom: 20px;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        .no-results {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .no-results h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: #333;
        }
        
        @media (max-width: 768px) {
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .tools-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .results-header {
                text-align: center;
            }
            
            .tool-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .checkbox-group {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Browse AI Tools</h1>
        <p>Explore our complete database of {{ total }} AI tools</p>
    </div>
    
    <div class="container">
        <a href="/" class="back-btn">← Back to Dashboard</a>
        
        <div class="filters">
            <h3>🔧 Filter & Search Tools</h3>
            <form method="GET">
                <div class="filters-grid">
                    <div class="filter-group">
                        <label for="search">Search Tools:</label>
                        <input type="text" id="search" name="search" value="{{ search }}" placeholder="Search by name or description...">
                    </div>
                    
                    <div class="filter-group">
                        <label for="category">Category:</label>
                        <select id="category" name="category">
                            <option value="">All Categories</option>
                            {% for cat in categories %}
                            <option value="{{ cat.category }}" {% if category == cat.category %}selected{% endif %}>
                                {{ cat.category }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="source">Source:</label>
                        <select id="source" name="source">
                            <option value="">All Sources</option>
                            {% for src in sources %}
                            <option value="{{ src.source_website }}" {% if source == src.source_website %}selected{% endif %}>
                                {{ src.source_website }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <button type="submit" class="btn-filter">Apply Filters</button>
                    </div>
                </div>
                
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="free_only" name="free_only" value="1" {% if free_only %}checked{% endif %}>
                        <label for="free_only">Free Tools Only</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="open_source_only" name="open_source_only" value="1" {% if open_source_only %}checked{% endif %}>
                        <label for="open_source_only">Open Source Only</label>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="results-header">
            <div class="results-info">
                Showing <strong>{{ tools|length }}</strong> of <strong>{{ total }}</strong> tools
                {% if category or source or search or free_only or open_source_only %}
                (filtered)
                {% endif %}
            </div>
            <div class="results-info">
                Page <strong>{{ page }}</strong> of <strong>{{ total_pages }}</strong>
            </div>
        </div>
        
        {% if tools %}
        <div class="tools-grid">
            {% for tool in tools %}
            <div class="tool-card">
                <div class="tool-header">
                    <div>
                        <div class="tool-name">
                            {% if tool.website_url %}
                            <a href="{{ tool.website_url }}" target="_blank">{{ tool.name }}</a>
                            {% else %}
                            {{ tool.name }}
                            {% endif %}
                        </div>
                    </div>
                    {% if tool.category %}
                    <div class="tool-category">{{ tool.category }}</div>
                    {% endif %}
                </div>
                
                {% if tool.description %}
                <div class="tool-description">
                    {{ tool.description[:200] }}{% if tool.description|length > 200 %}...{% endif %}
                </div>
                {% endif %}
                
                <div class="tool-meta">
                    <div class="tool-badges">
                        {% if tool.free_tier %}
                        <span class="badge badge-free">Free</span>
                        {% endif %}
                        {% if tool.open_source %}
                        <span class="badge badge-open-source">Open Source</span>
                        {% endif %}
                        <span class="badge badge-source">{{ tool.source_website }}</span>
                    </div>
                    {% if tool.scraped_date %}
                    <div class="tool-source">
                        Added: {{ tool.scraped_date[:10] }}
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if total_pages > 1 %}
        <div class="pagination">
            {% if page > 1 %}
            <a href="?page={{ page - 1 }}&category={{ category }}&source={{ source }}&search={{ search }}&free_only={{ free_only }}&open_source_only={{ open_source_only }}">« Previous</a>
            {% else %}
            <span class="disabled">« Previous</span>
            {% endif %}
            
            {% for p in range(1, total_pages + 1) %}
                {% if p == page %}
                <span class="current">{{ p }}</span>
                {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 1 and p <= page + 1) %}
                <a href="?page={{ p }}&category={{ category }}&source={{ source }}&search={{ search }}&free_only={{ free_only }}&open_source_only={{ open_source_only }}">{{ p }}</a>
                {% elif p == 4 and page > 6 %}
                <span>...</span>
                {% elif p == total_pages - 3 and page < total_pages - 5 %}
                <span>...</span>
                {% endif %}
            {% endfor %}
            
            {% if page < total_pages %}
            <a href="?page={{ page + 1 }}&category={{ category }}&source={{ source }}&search={{ search }}&free_only={{ free_only }}&open_source_only={{ open_source_only }}">Next »</a>
            {% else %}
            <span class="disabled">Next »</span>
            {% endif %}
        </div>
        {% endif %}
        
        {% else %}
        <div class="no-results">
            <h3>No tools found</h3>
            <p>Try adjusting your filters or search terms.</p>
        </div>
        {% endif %}
    </div>
</body>
</html>
