{% extends "admin/base.html" %}

{% block title %}🔌 API Scraping{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">🔌 API Scraping</h1>
    <p class="page-subtitle">
        Configure and manage API-based data collection from external sources to automatically discover new AI tools.
    </p>
</div>

<!-- Quick Stats -->
<div class="content-section">
    <div class="section-header">
        <h2 class="section-title">📊 API Scraping Overview</h2>
        <a href="#" class="btn btn-primary">➕ Add New API Config</a>
    </div>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ configs|length if configs else '0' }}</div>
            <div class="stat-label">API Configurations</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ configs|selectattr('is_active')|list|length if configs else '0' }}</div>
            <div class="stat-label">Active Sources</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ recent_results|length if recent_results else '0' }}</div>
            <div class="stat-label">Recent Runs</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ recent_results|sum(attribute='tools_found') if recent_results else '0' }}</div>
            <div class="stat-label">Tools Discovered</div>
        </div>
    </div>
</div>

<!-- API Configurations -->
<div class="content-section">
    <div class="section-header">
        <h2 class="section-title">🔧 API Configurations</h2>
        <div style="display: flex; gap: 10px;">
            <select class="form-select" style="width: 150px;">
                <option>All Types</option>
                <option>GitHub API</option>
                <option>Product Hunt</option>
                <option>HuggingFace</option>
                <option>REST API</option>
                <option>GraphQL</option>
                <option>RSS Feed</option>
            </select>
            <a href="#" class="btn btn-secondary">🔄 Refresh All</a>
        </div>
    </div>
    
    {% if configs %}
    <div class="data-table-container">
        <table class="data-table">
            <thead>
                <tr>
                    <th>Configuration</th>
                    <th>API Type</th>
                    <th>Status</th>
                    <th>Last Run</th>
                    <th>Success Rate</th>
                    <th>Total Results</th>
                    <th style="width: 150px;">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for config in configs %}
                <tr>
                    <td>
                        <div>
                            <strong style="color: #e2e8f0; font-size: 1rem;">{{ config.config_name }}</strong>
                            <div style="color: #94a3b8; font-size: 0.85rem; margin-top: 4px;">
                                {{ config.api_url[:50] }}{% if config.api_url|length > 50 %}...{% endif %}
                            </div>
                        </div>
                    </td>
                    <td>
                        <span style="background: rgba(139, 92, 246, 0.2); color: #c4b5fd; padding: 4px 10px; border-radius: 6px; font-size: 0.8rem; font-weight: 500; text-transform: uppercase;">
                            {{ config.api_type }}
                        </span>
                    </td>
                    <td>
                        {% if config.is_active %}
                        <span style="background: rgba(16, 185, 129, 0.2); color: #6ee7b7; padding: 4px 10px; border-radius: 6px; font-size: 0.8rem; font-weight: 500;">
                            🟢 Active
                        </span>
                        {% else %}
                        <span style="background: rgba(71, 85, 105, 0.3); color: #94a3b8; padding: 4px 10px; border-radius: 6px; font-size: 0.8rem; font-weight: 500;">
                            ⏸️ Paused
                        </span>
                        {% endif %}
                    </td>
                    <td style="color: #94a3b8; font-size: 0.85rem;">
                        {% if config.last_run %}
                        {{ config.last_run.split(' ')[0] }}
                        <br><small style="color: #64748b;">{{ config.last_run.split(' ')[1][:5] if ' ' in config.last_run else '' }}</small>
                        {% else %}
                        <span style="color: #64748b; font-style: italic;">Never</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if config.success_rate is not none %}
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <div style="flex: 1; background: rgba(71, 85, 105, 0.3); border-radius: 4px; height: 6px; overflow: hidden;">
                                <div style="background: #10b981; height: 100%; width: {{ config.success_rate }}%; transition: width 0.3s;"></div>
                            </div>
                            <span style="color: #6ee7b7; font-size: 0.8rem; font-weight: 600;">{{ config.success_rate|round }}%</span>
                        </div>
                        {% else %}
                        <span style="color: #94a3b8;">N/A</span>
                        {% endif %}
                    </td>
                    <td style="text-align: center;">
                        <div style="color: #60a5fa; font-size: 1.1rem; font-weight: 600;">
                            {{ config.total_results if config.total_results else '0' }}
                        </div>
                    </td>
                    <td>
                        <div style="display: flex; gap: 5px; flex-wrap: wrap;">
                            <button class="btn btn-success" style="padding: 6px 8px; font-size: 0.75rem;">
                                ▶️ Run
                            </button>
                            <a href="#" class="btn btn-secondary" style="padding: 6px 8px; font-size: 0.75rem;">
                                ✏️ Edit
                            </a>
                            {% if config.is_active %}
                            <button class="btn btn-warning" style="padding: 6px 8px; font-size: 0.75rem;">
                                ⏸️
                            </button>
                            {% else %}
                            <button class="btn btn-success" style="padding: 6px 8px; font-size: 0.75rem;">
                                ▶️
                            </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div style="text-align: center; padding: 60px 20px; color: #94a3b8;">
        <div style="font-size: 4rem; margin-bottom: 20px;">🔌</div>
        <h3 style="margin-bottom: 10px; color: #e2e8f0;">No API configurations yet</h3>
        <p>Create your first API scraping configuration to start discovering tools automatically</p>
        <a href="#" class="btn btn-primary" style="margin-top: 20px;">Create First Configuration</a>
    </div>
    {% endif %}
</div>

<!-- Recent Activity -->
<div class="content-section">
    <div class="section-header">
        <h2 class="section-title">📋 Recent Scraping Results</h2>
        <a href="#" class="btn btn-secondary">View All Results</a>
    </div>
    
    {% if recent_results %}
    <div class="data-table-container">
        <table class="data-table">
            <thead>
                <tr>
                    <th>Configuration</th>
                    <th>Status</th>
                    <th>Tools Found</th>
                    <th>Execution Time</th>
                    <th>Started At</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for result in recent_results %}
                <tr>
                    <td>
                        <strong style="color: #e2e8f0;">{{ result.config_name }}</strong>
                    </td>
                    <td>
                        {% if result.status == 'completed' %}
                        <span style="background: rgba(16, 185, 129, 0.2); color: #6ee7b7; padding: 4px 8px; border-radius: 4px; font-size: 0.8rem;">
                            ✅ Completed
                        </span>
                        {% elif result.status == 'running' %}
                        <span style="background: rgba(59, 130, 246, 0.2); color: #93c5fd; padding: 4px 8px; border-radius: 4px; font-size: 0.8rem;">
                            🔄 Running
                        </span>
                        {% elif result.status == 'failed' %}
                        <span style="background: rgba(239, 68, 68, 0.2); color: #fca5a5; padding: 4px 8px; border-radius: 4px; font-size: 0.8rem;">
                            ❌ Failed
                        </span>
                        {% else %}
                        <span style="background: rgba(245, 158, 11, 0.2); color: #fcd34d; padding: 4px 8px; border-radius: 4px; font-size: 0.8rem;">
                            ⏳ {{ result.status|title }}
                        </span>
                        {% endif %}
                    </td>
                    <td style="text-align: center;">
                        <div style="color: #60a5fa; font-weight: 600;">
                            {{ result.tools_found if result.tools_found else '0' }}
                        </div>
                    </td>
                    <td style="color: #94a3b8; font-size: 0.9rem;">
                        {% if result.execution_time %}
                        {{ "%.2f"|format(result.execution_time) }}s
                        {% else %}
                        N/A
                        {% endif %}
                    </td>
                    <td style="color: #94a3b8; font-size: 0.85rem;">
                        {% if result.started_at %}
                        {{ result.started_at.split(' ')[0] }}
                        <br><small style="color: #64748b;">{{ result.started_at.split(' ')[1][:5] if ' ' in result.started_at else '' }}</small>
                        {% else %}
                        N/A
                        {% endif %}
                    </td>
                    <td>
                        <a href="#" class="btn btn-secondary" style="padding: 6px 12px; font-size: 0.8rem;">
                            👁️ View Details
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div style="text-align: center; padding: 40px 20px; color: #94a3b8;">
        <div style="font-size: 2.5rem; margin-bottom: 15px;">📊</div>
        <h4 style="margin-bottom: 8px; color: #e2e8f0;">No recent activity</h4>
        <p>Run your first API scraping job to see results here</p>
    </div>
    {% endif %}
</div>

<!-- Available API Types -->
<div class="content-section">
    <div class="section-header">
        <h2 class="section-title">🌐 Supported API Types</h2>
    </div>
    
    <div class="stats-grid" style="grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));">
        <div class="stat-card" style="text-align: left; cursor: pointer;" onclick="createConfig('github')">
            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 15px;">
                <div style="font-size: 2rem;">🐙</div>
                <h3 style="color: #3b82f6;">GitHub API</h3>
            </div>
            <p style="color: #94a3b8; font-size: 0.9rem; line-height: 1.4;">
                Discover repositories, trending projects, and developer tools from GitHub's extensive database.
            </p>
        </div>
        
        <div class="stat-card" style="text-align: left; cursor: pointer;" onclick="createConfig('producthunt')">
            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 15px;">
                <div style="font-size: 2rem;">🚀</div>
                <h3 style="color: #f59e0b;">Product Hunt</h3>
            </div>
            <p style="color: #94a3b8; font-size: 0.9rem; line-height: 1.4;">
                Find the latest AI tools and products launched on Product Hunt's platform.
            </p>
        </div>
        
        <div class="stat-card" style="text-align: left; cursor: pointer;" onclick="createConfig('huggingface')">
            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 15px;">
                <div style="font-size: 2rem;">🤗</div>
                <h3 style="color: #10b981;">HuggingFace</h3>
            </div>
            <p style="color: #94a3b8; font-size: 0.9rem; line-height: 1.4;">
                Access models, datasets, and spaces from the HuggingFace AI community.
            </p>
        </div>
        
        <div class="stat-card" style="text-align: left; cursor: pointer;" onclick="createConfig('rest')">
            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 15px;">
                <div style="font-size: 2rem;">🔗</div>
                <h3 style="color: #8b5cf6;">REST API</h3>
            </div>
            <p style="color: #94a3b8; font-size: 0.9rem; line-height: 1.4;">
                Connect to any REST API endpoint to fetch and parse tool data automatically.
            </p>
        </div>
        
        <div class="stat-card" style="text-align: left; cursor: pointer;" onclick="createConfig('rss')">
            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 15px;">
                <div style="font-size: 2rem;">📡</div>
                <h3 style="color: #ef4444;">RSS Feeds</h3>
            </div>
            <p style="color: #94a3b8; font-size: 0.9rem; line-height: 1.4;">
                Monitor RSS feeds from AI news sites, blogs, and announcement channels.
            </p>
        </div>
        
        <div class="stat-card" style="text-align: left; cursor: pointer;" onclick="createConfig('graphql')">
            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 15px;">
                <div style="font-size: 2rem;">⚡</div>
                <h3 style="color: #06b6d4;">GraphQL</h3>
            </div>
            <p style="color: #94a3b8; font-size: 0.9rem; line-height: 1.4;">
                Execute GraphQL queries to fetch structured data from GraphQL APIs.
            </p>
        </div>
    </div>
</div>

<script>
function createConfig(apiType) {
    // This would open a modal or redirect to create a new config
    alert('Create new ' + apiType + ' configuration (functionality to be implemented)');
}

document.addEventListener('DOMContentLoaded', function() {
    // Add progress bar animations
    const progressBars = document.querySelectorAll('[style*="width:"]');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });
});
</script>
{% endblock %}
