#!/usr/bin/env python3
"""Start admin dashboard with fixed API scraping route"""

import os
import sys
import time
import webbrowser
import threading

def main():
    print("🚀 Starting Fixed Admin Dashboard...")
    
    try:
        # Import the admin dashboard
        print("📥 Importing admin dashboard...")
        from admin_dashboard import app
        
        print("✅ Import successful!")
        
        # Verify routes
        routes = {rule.endpoint: rule.rule for rule in app.url_map.iter_rules()}
        api_routes = {k: v for k, v in routes.items() if 'api_scraping' in k}
        
        print(f"🛠️  API scraping routes found: {len(api_routes)}")
        for endpoint, rule in api_routes.items():
            print(f"   {endpoint}: {rule}")
        
        if 'admin_api_scraping' in routes:
            print("✅ admin_api_scraping route successfully registered!")
        else:
            print("❌ admin_api_scraping route still not found!")
            return
        
        # Test URL generation
        with app.app_context():
            from flask import url_for
            try:
                url = url_for('admin_api_scraping')
                print(f"✅ URL generation works: {url}")
            except Exception as e:
                print(f"❌ URL generation failed: {e}")
                return
        
        print("\n🌐 Starting server on http://localhost:5001")
        print("🔐 Login credentials: admin / admin123")
        
        # Open browser after a delay
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open("http://localhost:5001/admin/login")
                print("🌐 Browser opened")
            except Exception as e:
                print(f"Could not open browser: {e}")
        
        threading.Thread(target=open_browser, daemon=True).start()
        
        print("\n🔄 Starting Flask server...")
        print("Press Ctrl+C to stop")
        print("-" * 40)
        
        # Start the app
        app.run(host='127.0.0.1', port=5001, debug=False, use_reloader=False)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
