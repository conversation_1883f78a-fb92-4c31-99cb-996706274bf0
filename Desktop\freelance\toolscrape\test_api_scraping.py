#!/usr/bin/env python3
"""Test API scraping functionality with authentication"""

import requests
import json

def test_api_scraping():
    """Test the API scraping page with proper authentication"""
    base_url = "http://localhost:5001"
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    try:
        # First, login to get authentication
        print("🔐 Logging in...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        login_response = session.post(f"{base_url}/admin/login", data=login_data)
        
        if login_response.status_code == 200 and 'login' not in login_response.url:
            print("✅ Login successful")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        # Now test the API scraping page
        print("🔍 Testing API scraping page...")
        api_response = session.get(f"{base_url}/admin/api-scraping")
        
        print(f"Status Code: {api_response.status_code}")
        print(f"Content Length: {len(api_response.text)} characters")
        
        if api_response.status_code == 200:
            # Check if it's the actual page or an error page
            content = api_response.text
            
            if "API Scraping Management" in content:
                print("✅ API Scraping page loaded successfully")
                
                # Check for key elements
                if "config" in content.lower():
                    print("✅ Configuration elements found")
                if "template" in content.lower():
                    print("✅ Template rendering working")
                if "error" in content.lower():
                    print("⚠️ Error messages detected in content")
                    
                # Save the content for inspection
                with open('api_scraping_page_content.html', 'w', encoding='utf-8') as f:
                    f.write(content)
                print("📄 Page content saved to api_scraping_page_content.html")
                
                return True
            else:
                print("❌ API Scraping page content not as expected")
                print("First 500 characters:")
                print(content[:500])
                return False
        else:
            print(f"❌ API Scraping page failed: {api_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API scraping: {e}")
        return False

if __name__ == "__main__":
    success = test_api_scraping()
    if success:
        print("\n🎉 API Scraping functionality test passed!")
    else:
        print("\n❌ API Scraping functionality test failed!")
