import requests
import json
import sqlite3
import time
from bs4 import BeautifulSoup
import re
import logging

logger = logging.getLogger(__name__)

class ExtendedAIToolsScraper:
    def __init__(self, db_path="ai_tools_database.db"):
        self.conn = sqlite3.connect(db_path)
        self.cursor = self.conn.cursor()
        self.session = requests.Session()
        
        # Create additional tables for GitHub repos and HuggingFace models
        self.init_extended_tables()
    
    def init_extended_tables(self):
        """Initialize additional tables for GitHub repos and HuggingFace models"""
        
        # GitHub repositories table
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS github_repos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                full_name TEXT,
                description TEXT,
                url TEXT,
                homepage TEXT,
                language TEXT,
                stars INTEGER,
                forks INTEGER,
                open_issues INTEGER,
                license TEXT,
                topics TEXT,
                created_at TEXT,
                updated_at TEXT,
                size INTEGER,
                watchers INTEGER,
                has_wiki BOOLEAN,
                has_pages BOOLEAN,
                archived <PERSON><PERSON><PERSON><PERSON><PERSON>,
                disabled <PERSON><PERSON><PERSON><PERSON><PERSON>,
                pushed_at TEXT,
                clone_url TEXT,
                ssh_url TEXT,
                html_url TEXT,
                scraped_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # HuggingFace models table
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS huggingface_models (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_id TEXT NOT NULL,
                model_name TEXT,
                author TEXT,
                description TEXT,
                pipeline_tag TEXT,
                task TEXT,
                library_name TEXT,
                language TEXT,
                license TEXT,
                tags TEXT,
                downloads INTEGER,
                likes INTEGER,
                created_at TEXT,
                last_modified TEXT,
                private BOOLEAN,
                gated BOOLEAN,
                inference TEXT,
                model_size TEXT,
                parameters TEXT,
                model_url TEXT,
                scraped_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        self.conn.commit()
    
    def scrape_github_awesome_ai_tools(self):
        """Scrape the awesome-ai-tools GitHub repository"""
        logger.info("Scraping GitHub awesome-ai-tools repository...")
        
        try:
            # Get repository information
            repo_url = "https://api.github.com/repos/mahseema/awesome-ai-tools"
            response = self.session.get(repo_url)
            repo_data = response.json()
            
            # Save repository data
            self.save_github_repo(repo_data)
            
            # Get README content to extract tools
            readme_url = "https://raw.githubusercontent.com/mahseema/awesome-ai-tools/main/README.md"
            readme_response = self.session.get(readme_url)
            readme_content = readme_response.text
            
            # Parse tools from README
            tools = self.parse_awesome_list(readme_content, "mahseema/awesome-ai-tools")
            
            return tools
            
        except Exception as e:
            logger.error(f"Error scraping GitHub awesome-ai-tools: {e}")
            return []
    
    def parse_awesome_list(self, content, source):
        """Parse tools from awesome list markdown content"""
        tools = []
        
        lines = content.split('\n')
        current_category = "General"
        
        for line in lines:
            line = line.strip()
            
            # Detect category headers
            if line.startswith('##') and not line.startswith('###'):
                current_category = line.replace('##', '').strip()
                continue
            
            # Parse tool entries (usually start with -)
            if line.startswith('-') or line.startswith('*'):
                tool_match = re.search(r'\[([^\]]+)\]\(([^)]+)\)\s*-?\s*(.*)', line)
                if tool_match:
                    name = tool_match.group(1).strip()
                    url = tool_match.group(2).strip()
                    description = tool_match.group(3).strip()
                    
                    # Clean description
                    description = re.sub(r'^[-–—]\s*', '', description)
                    description = description.strip('.')
                    
                    tools.append({
                        'name': name,
                        'description': description,
                        'website_url': url,
                        'category': current_category,
                        'source_website': source,
                        'open_source': True if 'github.com' in url else None
                    })
        
        logger.info(f"Parsed {len(tools)} tools from awesome list")
        return tools
    
    def save_github_repo(self, repo_data):
        """Save GitHub repository data"""
        try:
            self.cursor.execute('''
                INSERT OR REPLACE INTO github_repos (
                    name, full_name, description, url, homepage, language,
                    stars, forks, open_issues, license, topics, created_at,
                    updated_at, size, watchers, has_wiki, has_pages,
                    archived, disabled, pushed_at, clone_url, ssh_url, html_url
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                repo_data.get('name'),
                repo_data.get('full_name'),
                repo_data.get('description'),
                repo_data.get('url'),
                repo_data.get('homepage'),
                repo_data.get('language'),
                repo_data.get('stargazers_count'),
                repo_data.get('forks_count'),
                repo_data.get('open_issues_count'),
                repo_data.get('license', {}).get('name') if repo_data.get('license') else None,
                ','.join(repo_data.get('topics', [])),
                repo_data.get('created_at'),
                repo_data.get('updated_at'),
                repo_data.get('size'),
                repo_data.get('watchers_count'),
                repo_data.get('has_wiki'),
                repo_data.get('has_pages'),
                repo_data.get('archived'),
                repo_data.get('disabled'),
                repo_data.get('pushed_at'),
                repo_data.get('clone_url'),
                repo_data.get('ssh_url'),
                repo_data.get('html_url')
            ))
            self.conn.commit()
        except Exception as e:
            logger.error(f"Error saving GitHub repo: {e}")
    
    def scrape_huggingface_models(self):
        """Scrape top HuggingFace models"""
        logger.info("Scraping HuggingFace models...")
        
        models = []
        model_names = [
            "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B",
            "meta-llama/Llama-3.1-8B-Instruct",
            "black-forest-labs/FLUX.1-dev",
            "THUDM/glm-4-9b-chat",
            "HuggingFaceTB/SmolLM2-1.7B-Instruct",
            "01-ai/Yi-Coder-9B-Chat",
            "nanonets/nanonets-ocr-v2",
            "jinaai/jina-embeddings-v3"
        ]
        
        for model_name in model_names:
            try:
                # Get model information from HuggingFace API
                api_url = f"https://huggingface.co/api/models/{model_name}"
                response = self.session.get(api_url)
                
                if response.status_code == 200:
                    model_data = response.json()
                    
                    # Extract model information
                    model_info = {
                        'model_id': model_name,
                        'model_name': model_data.get('id', '').split('/')[-1],
                        'author': model_data.get('id', '').split('/')[0] if '/' in model_data.get('id', '') else '',
                        'description': model_data.get('description', ''),
                        'pipeline_tag': model_data.get('pipeline_tag', ''),
                        'library_name': model_data.get('library_name', ''),
                        'language': ','.join(model_data.get('language', [])) if isinstance(model_data.get('language'), list) else model_data.get('language', ''),
                        'license': model_data.get('license', ''),
                        'tags': ','.join(model_data.get('tags', [])),
                        'downloads': model_data.get('downloads', 0),
                        'likes': model_data.get('likes', 0),
                        'created_at': model_data.get('createdAt', ''),
                        'last_modified': model_data.get('lastModified', ''),
                        'private': model_data.get('private', False),
                        'gated': model_data.get('gated', False),
                        'model_url': f"https://huggingface.co/{model_name}"
                    }
                    
                    # Estimate parameters from model name
                    param_match = re.search(r'(\d+\.?\d*)[Bb]', model_name)
                    if param_match:
                        model_info['parameters'] = f"{param_match.group(1)}B parameters"
                    
                    models.append(model_info)
                    self.save_huggingface_model(model_info)
                    
                    time.sleep(1)  # Rate limiting
                    
            except Exception as e:
                logger.error(f"Error scraping model {model_name}: {e}")
                continue
        
        logger.info(f"Scraped {len(models)} HuggingFace models")
        return models
    
    def save_huggingface_model(self, model_data):
        """Save HuggingFace model data"""
        try:
            self.cursor.execute('''
                INSERT OR REPLACE INTO huggingface_models (
                    model_id, model_name, author, description, pipeline_tag,
                    library_name, language, license, tags, downloads, likes,
                    created_at, last_modified, private, gated, parameters, model_url
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                model_data.get('model_id'),
                model_data.get('model_name'),
                model_data.get('author'),
                model_data.get('description'),
                model_data.get('pipeline_tag'),
                model_data.get('library_name'),
                model_data.get('language'),
                model_data.get('license'),
                model_data.get('tags'),
                model_data.get('downloads'),
                model_data.get('likes'),
                model_data.get('created_at'),
                model_data.get('last_modified'),
                model_data.get('private'),
                model_data.get('gated'),
                model_data.get('parameters'),
                model_data.get('model_url')
            ))
            self.conn.commit()
        except Exception as e:
            logger.error(f"Error saving HuggingFace model: {e}")
    
    def scrape_futuretools_io(self):
        """Scrape tools from FutureTools.io"""
        logger.info("Scraping FutureTools.io...")
        tools = []
        
        try:
            url = "https://www.futuretools.io/"
            response = self.session.get(url, timeout=30)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find tool cards
            tool_cards = soup.find_all(['div', 'article'], class_=re.compile(r'tool|card|item'))
            
            for card in tool_cards[:50]:
                try:
                    name = ""
                    title_elem = card.find(['h1', 'h2', 'h3', 'h4'])
                    if title_elem:
                        name = title_elem.get_text().strip()
                    
                    description = ""
                    desc_elem = card.find(['p', 'div'], class_=re.compile(r'description|excerpt'))
                    if desc_elem:
                        description = desc_elem.get_text().strip()
                    
                    # Extract URL
                    url = ""
                    link_elem = card.find('a', href=True)
                    if link_elem:
                        url = link_elem['href']
                        if url and not url.startswith('http'):
                            url = f"https://www.futuretools.io{url}"
                    
                    # Extract category/tags
                    category = "AI Tools"
                    tag_elem = card.find(['span', 'div'], class_=re.compile(r'tag|category'))
                    if tag_elem:
                        category = tag_elem.get_text().strip()
                    
                    if name:
                        tools.append({
                            'name': name,
                            'description': description,
                            'website_url': url,
                            'category': category,
                            'source_website': 'futuretools.io'
                        })
                except Exception as e:
                    continue
                    
        except Exception as e:
            logger.error(f"Error scraping FutureTools.io: {e}")
            
        return tools
    
    def scrape_opentools_ai(self):
        """Scrape tools from OpenTools.ai"""
        logger.info("Scraping OpenTools.ai...")
        tools = []
        
        try:
            url = "https://opentools.ai/"
            response = self.session.get(url, timeout=30)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find tool listings
            tool_elements = soup.find_all(['div', 'article'], class_=re.compile(r'tool|card|listing'))
            
            for element in tool_elements[:50]:
                try:
                    name = ""
                    title_elem = element.find(['h1', 'h2', 'h3', 'h4'])
                    if title_elem:
                        name = title_elem.get_text().strip()
                    
                    description = ""
                    desc_elem = element.find(['p', 'div'], class_=re.compile(r'description|excerpt'))
                    if desc_elem:
                        description = desc_elem.get_text().strip()
                    
                    # Extract URL
                    url = ""
                    link_elem = element.find('a', href=True)
                    if link_elem:
                        url = link_elem['href']
                        if url and not url.startswith('http'):
                            url = f"https://opentools.ai{url}"
                    
                    if name:
                        tools.append({
                            'name': name,
                            'description': description,
                            'website_url': url,
                            'source_website': 'opentools.ai',
                            'category': 'AI Tools'
                        })
                except Exception as e:
                    continue
                    
        except Exception as e:
            logger.error(f"Error scraping OpenTools.ai: {e}")
            
        return tools
    
    def save_tools_to_main_db(self, tools):
        """Save tools to main ai_tools table"""
        for tool in tools:
            try:
                # Check if tool already exists
                self.cursor.execute(
                    "SELECT id FROM ai_tools WHERE name = ? AND source_website = ?",
                    (tool['name'], tool['source_website'])
                )
                
                if self.cursor.fetchone():
                    # Update existing tool
                    self.cursor.execute('''
                        UPDATE ai_tools SET 
                            description = ?, category = ?, website_url = ?, 
                            open_source = ?, last_updated = CURRENT_TIMESTAMP
                        WHERE name = ? AND source_website = ?
                    ''', (
                        tool.get('description', ''),
                        tool.get('category', ''),
                        tool.get('website_url', ''),
                        tool.get('open_source', False),
                        tool['name'],
                        tool['source_website']
                    ))
                else:
                    # Insert new tool
                    self.cursor.execute('''
                        INSERT INTO ai_tools (
                            name, description, category, website_url, 
                            open_source, source_website
                        ) VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        tool['name'],
                        tool.get('description', ''),
                        tool.get('category', ''),
                        tool.get('website_url', ''),
                        tool.get('open_source', False),
                        tool['source_website']
                    ))
            except Exception as e:
                logger.error(f"Error saving tool {tool.get('name', 'Unknown')}: {e}")
                continue
        
        self.conn.commit()
    
    def run_extended_scraping(self):
        """Run all extended scraping operations"""
        logger.info("Starting extended scraping...")
        
        all_tools = []
        
        # Scrape GitHub awesome list
        github_tools = self.scrape_github_awesome_ai_tools()
        all_tools.extend(github_tools)
        
        # Scrape HuggingFace models
        self.scrape_huggingface_models()
        
        # Scrape additional sources
        futuretools_tools = self.scrape_futuretools_io()
        all_tools.extend(futuretools_tools)
        
        opentools_tools = self.scrape_opentools_ai()
        all_tools.extend(opentools_tools)
        
        # Save all tools
        self.save_tools_to_main_db(all_tools)
        
        logger.info(f"Extended scraping completed! Additional tools: {len(all_tools)}")
        return all_tools
    
    def close(self):
        """Close database connection"""
        self.conn.close()

if __name__ == "__main__":
    extended_scraper = ExtendedAIToolsScraper()
    
    try:
        # Run extended scraping
        extended_scraper.run_extended_scraping()
        
    except Exception as e:
        logger.error(f"Error in extended scraping: {e}")
    finally:
        extended_scraper.close()
