#!/usr/bin/env python3
"""Test the admin dashboard startup"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

try:
    print("Importing admin_dashboard...")
    from admin_dashboard import app
    
    print("✅ Successfully imported admin_dashboard")
    
    # Test creating a test client
    with app.test_client() as client:
        print("✅ Flask test client created successfully")
        
        # Check if the route exists
        with app.app_context():
            print("\nChecking routes...")
            routes = []
            for rule in app.url_map.iter_rules():
                routes.append(f"{rule.endpoint}: {rule.rule}")
            
            print(f"Total routes found: {len(routes)}")
            
            # Look for API scraping routes
            api_routes = [r for r in routes if 'api_scraping' in r]
            print(f"\nAPI scraping routes found: {len(api_routes)}")
            for route in api_routes:
                print(f"  {route}")
            
            # Check specifically for admin_api_scraping
            if any('admin_api_scraping:' in route for route in routes):
                print("✅ admin_api_scraping route found!")
            else:
                print("❌ admin_api_scraping route NOT found!")
                print("\nAll routes:")
                for route in sorted(routes):
                    print(f"  {route}")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
