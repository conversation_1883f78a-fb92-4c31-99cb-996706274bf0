{"ai_tools": [{"id": 1, "name": "431 Best AI Tools", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "", "pricing_model": "premium, free", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 1.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "bestaito.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 2, "name": "ChatGPT", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://bestaito.com/tool/chatgpt/", "pricing_model": "pro, free", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 1.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "bestaito.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 3, "name": "Google Bard", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://bestaito.com/tool/google-bard/", "pricing_model": "free", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 1.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "bestaito.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 4, "name": "Bing Image Creator AI", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://bestaito.com/tool/bing-image-creator-ai/", "pricing_model": "free", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 1.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "bestaito.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 5, "name": "Ideogram", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://bestaito.com/tool/ideogram/", "pricing_model": "free", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 1.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "bestaito.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 6, "name": "ImgLarger AI Image Enlarger", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://bestaito.com/tool/imglarger-ai-image-enlarger/", "pricing_model": "pro, free", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 1.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "bestaito.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 7, "name": "Neural Love", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://bestaito.com/tool/neural-love-2/", "pricing_model": "free", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 1.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "bestaito.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 8, "name": "IMGCreator AI", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://bestaito.com/tool/imgcreator-ai/", "pricing_model": "free", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 1.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "bestaito.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 9, "name": "Google Translate", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://bestaito.com/tool/google-translate/", "pricing_model": "free", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 1.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "bestaito.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 10, "name": "Lumiere Video by Google", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://bestaito.com/tool/lumiere-video-by-google/", "pricing_model": "free", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 1.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "bestaito.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 11, "name": "CapCut AI Video Generator", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://bestaito.com/tool/capcut-ai-video-generator/", "pricing_model": "pro, free", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 1.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "bestaito.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 12, "name": "Bing Microsoft Translator", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://bestaito.com/tool/bing-microsoft-translator/", "pricing_model": "pro, free", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 1.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "bestaito.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 13, "name": "Personal AI", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://bestaito.com/tool/personal-ai/", "pricing_model": "pro, free", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 1.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "bestaito.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 14, "name": "10 Best AI Tools for UK Freelancers: Invoicing  Tax Compliance 2025", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://10bestaitools.com/ai-tools-for-uk-freelancers/", "pricing_model": "", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 0.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "10bestaitools.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 15, "name": "10 Best AI Tools for Literature Reviews : Academic Researchers  Data Collection", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://10bestaitools.com/best-ai-tools-for-literature-reviews-academia/", "pricing_model": "", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 0.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "10bestaitools.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 16, "name": "10 AI Tools to Create Viral LinkedIn Carousels in 2025", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://10bestaitools.com/ai-tools-to-create-viral-linkedin-carousels/", "pricing_model": "", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 0.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "10bestaitools.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 17, "name": "10 Best AI Tools for Managing ADHD: Focus Apps  Time Management 2025", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://10bestaitools.com/ai-tools-for-managing-adhd/", "pricing_model": "", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 0.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "10bestaitools.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 18, "name": "10 Best AI Tools for US Insurance Agents: Claims Processing  Customer Queries", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://10bestaitools.com/best-ai-tools-for-us-insurance-agents/", "pricing_model": "", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 0.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "10bestaitools.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 19, "name": "10 Best AI Tools for Recruiters: Candidate Sourcing  Interview Scheduling", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://10bestaitools.com/best-ai-tools-for-recruiters/", "pricing_model": "", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 0.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "10bestaitools.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 20, "name": "10 Best AI Tools for Nonprofit Organizations: Donor Management  Grant Writing", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://10bestaitools.com/best-ai-tools-for-nonprofit-organizations/", "pricing_model": "", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 0.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "10bestaitools.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 21, "name": "Best AI Art Generator: MidJourney vs DALL-E 3 vs Stable Diffusion", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://10bestaitools.com/best-ai-art-generator/", "pricing_model": "", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 0.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "10bestaitools.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 22, "name": "10 Best AI Tools for Small Businesses 2025: Automation Under 50Month", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://10bestaitools.com/best-ai-tools-for-small-businesses/", "pricing_model": "", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 0.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "10bestaitools.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 23, "name": "10 Best AI Tools for US Real Estate Investors: Flip Houses  Analyze Markets", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "https://10bestaitools.com/best-ai-tools-for-us-real-estate-investors/", "pricing_model": "", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 0.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "10bestaitools.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 24, "name": "All The Best AI Tools List In A Single Place!", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "", "pricing_model": "", "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": 0.0, "trial_available": null, "image_url": null, "video_url": null, "source_website": "aisitelist.com", "scraped_date": "2025-07-13 17:32:23", "last_updated": "2025-07-13 17:32:23"}, {"id": 25, "name": "🌟 Editor's Choice", "description": "", "category": "Contents", "subcategory": null, "website_url": "#editors-choice", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 26, "name": "📝 AI Text", "description": "", "category": "Contents", "subcategory": null, "website_url": "#text", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 27, "name": "👩‍💻 Code with AI", "description": "", "category": "Contents", "subcategory": null, "website_url": "#code", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 28, "name": "🖼️ Generative AI Images", "description": "", "category": "Contents", "subcategory": null, "website_url": "#image", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 29, "name": "📽️ Generative AI Video", "description": "", "category": "Contents", "subcategory": null, "website_url": "#video", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 30, "name": "🎶 Generative AI Audio", "description": "", "category": "Contents", "subcategory": null, "website_url": "#audio", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 31, "name": "Voice Cloning", "description": "", "category": "Contents", "subcategory": null, "website_url": "#ai-voice-cloning", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 32, "name": "Music Generation", "description": "", "category": "Contents", "subcategory": null, "website_url": "#ai-music-generation", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 33, "name": "🎯 AI Tools for Marketing", "description": "", "category": "Contents", "subcategory": null, "website_url": "#marketing-ai-tools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 34, "name": "📞 AI Phone Call Agents", "description": "", "category": "Contents", "subcategory": null, "website_url": "#phone-calls", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 35, "name": "🎒 Other AI Tools", "description": "", "category": "Contents", "subcategory": null, "website_url": "#other", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 36, "name": "👩‍🏫 Learning resources", "description": "", "category": "Contents", "subcategory": null, "website_url": "#learning-resources", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 37, "name": "There's an AI", "description": "List of best AI Tools", "category": "Editor's Choice", "subcategory": null, "website_url": "https://theresanai.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 38, "name": "Notion AI", "description": "Write better, more efficient notes and docs", "category": "Text", "subcategory": null, "website_url": "https://www.notion.so/product/ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 39, "name": "Murf AI", "description": "[Review](https://theresanai.com/murf) - User-friendly platform for quick, high-quality voiceovers, favored for commercial and marketing applications", "category": "Audio", "subcategory": null, "website_url": "https://murf.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 40, "name": "SaneBox", "description": "an email management software as a service that integrates with IMAP and Exchange Web Services email accounts", "category": "Editor's Choice", "subcategory": null, "website_url": "https://try.sanebox.com/yzkpe5s68xk2", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 41, "name": "MeetGeek", "description": "an AI meeting assistant that automatically video records, transcribes, summarizes, and provides the key points from every meeting", "category": "Editor's Choice", "subcategory": null, "website_url": "https://get.meetgeek.ai/zmrnb5xlyfs9", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 42, "name": "OpenAI API", "description": "OpenAI's API provides access to GPT-3 and GPT-4 models, which performs a wide variety of natural language tasks, and Codex, which translates natural language to code", "category": "Text", "subcategory": null, "website_url": "https://openai.com/api/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 43, "name": "Gopher", "description": "Gopher by DeepMind is a 280 billion parameter language model", "category": "Text", "subcategory": null, "website_url": "https://www.deepmind.com/blog/language-modelling-at-scale-gopher-ethical-considerations-and-retrieval", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 44, "name": "OPT", "description": "Open Pretrained Transformers (OPT) by Facebook is a suite of decoder-only pre-trained transformers. [Announcement](https://ai.facebook.com/blog/democratizing-access-to-large-scale-language-models-with-opt-175b/). [OPT-175B text generation](https://opt.alpa.ai/) hosted by <PERSON><PERSON>", "category": "Text", "subcategory": null, "website_url": "https://huggingface.co/facebook/opt-350m", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 45, "name": "Bloom", "description": "BLOOM by Hugging Face is a model similar to GPT-3 that has been trained on 46 different languages and 13 programming languages. #opensource", "category": "Text", "subcategory": null, "website_url": "https://huggingface.co/docs/transformers/model_doc/bloom", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 46, "name": "LLaMA", "description": "A foundational, 65-billion-parameter large language model by Meta. #opensource", "category": "Text", "subcategory": null, "website_url": "https://ai.facebook.com/blog/large-language-model-llama-meta-ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 47, "name": "Llama 2", "description": "The next generation of Meta's open source large language model. #opensource", "category": "Text", "subcategory": null, "website_url": "https://ai.meta.com/llama/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 48, "name": "Claude 3", "description": "Talk to <PERSON>, an AI assistant from Anthropic", "category": "Text", "subcategory": null, "website_url": "https://claude.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 49, "name": "Vicuna-13B", "description": "An open-source chatbot trained by fine-tuning LLaMA on user-shared conversations collected from ShareGPT", "category": "Text", "subcategory": null, "website_url": "https://lmsys.org/blog/2023-03-30-vicuna/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 50, "name": "Stable Beluga", "description": "A finetuned LLamma 65B model", "category": "Text", "subcategory": null, "website_url": "https://huggingface.co/stabilityai/StableBeluga1-Delta", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 51, "name": "Stable Beluga 2", "description": "A finetuned LLamma2 70B model", "category": "Text", "subcategory": null, "website_url": "https://huggingface.co/stabilityai/StableBeluga2", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 52, "name": "GPT-4o Mini", "description": "*[Review on Altern](https://altern.ai/ai/gpt-4o-mini)* - Advancing cost-efficient intelligence", "category": "Text", "subcategory": null, "website_url": "https://altern.ai/ai/gpt-4o-mini", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 53, "name": "ChatGPT", "description": "*[reviews](https://theresanai.com/chatgpt)* - ChatGPT by OpenAI is a large language model that interacts in a conversational way", "category": "Text", "subcategory": null, "website_url": "https://chatgpt.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 54, "name": "<PERSON>", "description": "*[reviews](https://altern.ai/product/bing_chat)* - A conversational AI language model powered by Microsoft Bing", "category": "Text", "subcategory": null, "website_url": "https://www.bing.com/chat", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 55, "name": "Gemini", "description": "*[reviews](https://altern.ai/product/gemini)* - An experimental AI chatbot by Google, powered by the LaMDA model", "category": "Text", "subcategory": null, "website_url": "https://gemini.google.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 56, "name": "Character.AI", "description": "*[reviews](https://altern.ai/product/character-ai)* - Character.AI lets you create characters and chat to them", "category": "Text", "subcategory": null, "website_url": "https://character.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 57, "name": "ChatPDF", "description": "*[reviews](https://altern.ai/product/chatpdf)* - Chat with any PDF", "category": "Text", "subcategory": null, "website_url": "https://www.chatpdf.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 58, "name": "ChatSonic", "description": "*[reviews](https://altern.ai/product/chatsonic)* - An AI-powered assistant that enables text and image creation", "category": "Text", "subcategory": null, "website_url": "https://writesonic.com/chat", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 59, "name": "Phind", "description": "AI-based search engine", "category": "Text", "subcategory": null, "website_url": "https://phind.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 60, "name": "Tiledesk", "description": "*[reviews](https://altern.ai/product/tiledesk)* - Open-source LLM-enabled no-code chatbot development framework. Design, test and launch your flows on all your channels in minutes", "category": "Text", "subcategory": null, "website_url": "https://tiledesk.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 61, "name": "AICamp", "description": "*[reviews](#)* - ChatGPT for Teams", "category": "Text", "subcategory": null, "website_url": "https://aicamp.so/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 62, "name": "Kazimir.ai", "description": "A search engine designed to search AI-generated images", "category": "Text", "subcategory": null, "website_url": "https://kazimir.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 63, "name": "Perplexity AI", "description": "AI powered search tools", "category": "Text", "subcategory": null, "website_url": "https://www.perplexity.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 64, "name": "<PERSON><PERSON><PERSON>", "description": "Language model powered search", "category": "Text", "subcategory": null, "website_url": "https://metaphor.systems/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 65, "name": "You.com", "description": "A search engine built on AI that provides users with a customized search experience while keeping their data 100% private", "category": "Text", "subcategory": null, "website_url": "https://you.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 66, "name": "Komo AI", "description": "An AI based Search engine which responses quick and short answers", "category": "Text", "subcategory": null, "website_url": "https://komo.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 67, "name": "Telborg", "description": "Write a high-quality first draft on any Climate topic in minutes", "category": "Text", "subcategory": null, "website_url": "https://telborg.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 68, "name": "MemFree", "description": "Open Source Hybrid AI Search Engine, Instantly Get Accurate Answers from the Internet, Bookmarks, Notes, and Docs", "category": "Text", "subcategory": null, "website_url": "https://github.com/memfreeme/memfree", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 69, "name": "privateGPT", "description": "Ask questions to your documents without an internet connection, using the power of LLMs", "category": "Text", "subcategory": null, "website_url": "https://github.com/imartinez/privateGPT", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 70, "name": "quivr", "description": "Dump all your files and chat with it using your generative AI second brain using LLMs & embeddings", "category": "Text", "subcategory": null, "website_url": "https://github.com/StanGirard/quivr", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 71, "name": "Awesome AI Writing", "description": "", "category": "Related Awesome Lists", "subcategory": null, "website_url": "https://github.com/xaramore/awesome-ai-writing", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 72, "name": "<PERSON>", "description": "Create content faster with artificial intelligence", "category": "Text", "subcategory": null, "website_url": "https://www.jasper.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 73, "name": "Compose AI", "description": "Compose AI is a free Chrome extension that cuts your writing time by 40% with AI-powered autocompletion", "category": "Text", "subcategory": null, "website_url": "https://www.compose.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 74, "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> is an AI writing assistant that helps you create high-quality content", "category": "Text", "subcategory": null, "website_url": "https://rytr.me/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 75, "name": "wordtune", "description": "Personal writing assistant", "category": "Text", "subcategory": null, "website_url": "https://www.wordtune.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 76, "name": "HyperWrite", "description": "HyperWrite helps you write with confidence and get your work done faster from idea to final draft", "category": "Text", "subcategory": null, "website_url": "https://hyperwriteai.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 77, "name": "Nexus AI", "description": "Nexus AI is a generative cutting-edge AI Platform for writing, coding, voiceovers, research, image creation and beyond", "category": "Text", "subcategory": null, "website_url": "https://mynexusai.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 78, "name": "Moonbeam", "description": "Better blogs in a fraction of the time", "category": "Text", "subcategory": null, "website_url": "https://www.gomoonbeam.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 79, "name": "copy.ai", "description": "Write better marketing copy and content with AI", "category": "Text", "subcategory": null, "website_url": "https://www.copy.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 80, "name": "Anyword", "description": "Anyword's AI writing assistant generates effective copy for anyone", "category": "Text", "subcategory": null, "website_url": "https://anyword.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 81, "name": "<PERSON><PERSON>a", "description": "Create the content your audience wants, from content you've already made", "category": "Text", "subcategory": null, "website_url": "https://contenda.co/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 82, "name": "Hypotenuse AI", "description": "Turn a few keywords into original, insightful articles, product descriptions and social media copy", "category": "Text", "subcategory": null, "website_url": "https://www.hypotenuse.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 83, "name": "Lavender", "description": "Lavender email assistant helps you get more replies in less time", "category": "Text", "subcategory": null, "website_url": "https://www.lavender.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 84, "name": "Lex", "description": "A word processor with artificial intelligence baked in, so you can write faster", "category": "Text", "subcategory": null, "website_url": "https://lex.page/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 85, "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> is the ultimate writing assistant that saves you hours of ideation and writing time", "category": "Text", "subcategory": null, "website_url": "https://jenni.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 86, "name": "LAIKA", "description": "LAIKA trains an artificial intelligence on your own writing to create a personalised creative partner-in-crime", "category": "Text", "subcategory": null, "website_url": "https://www.writewithlaika.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 87, "name": "QuillBot", "description": "AI-powered paraphrasing tool", "category": "Text", "subcategory": null, "website_url": "https://quillbot.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 88, "name": "Postwise", "description": "Write tweets, schedule posts and grow your following using AI", "category": "Text", "subcategory": null, "website_url": "https://postwise.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 89, "name": "Co<PERSON><PERSON>", "description": "AI content creation solution for Enterprise & eCommerce", "category": "Text", "subcategory": null, "website_url": "https://copysmith.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 90, "name": "<PERSON><PERSON>", "description": "AI writing assistant for students and academics", "category": "Text", "subcategory": null, "website_url": "https://www.yomu.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 91, "name": "Listomatic", "description": "Free and fully configurable real estate listing description generator", "category": "Text", "subcategory": null, "website_url": "https://listomatic.app", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 92, "name": "Quick Creator", "description": "SEO-Optimized Blog platform powered by AI", "category": "Text", "subcategory": null, "website_url": "https://quickcreator.io", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 93, "name": "Dittto.ai", "description": "Fix your hero copy with an AI trained on top SaaS websites", "category": "Text", "subcategory": null, "website_url": "https://dittto.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 94, "name": "PulsePost", "description": "AI writer that Auto Publishes to your own website", "category": "Text", "subcategory": null, "website_url": "https://pulsepost.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 95, "name": "Shy <PERSON>", "description": "A modern AI-assisted writing environment for all types of prose", "category": "Text", "subcategory": null, "website_url": "https://www.shyeditor.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 96, "name": "DeepL Write", "description": "AI writing tool that improves written communication", "category": "Text", "subcategory": null, "website_url": "https://www.deepl.com/write", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 97, "name": "Gist AI", "description": "ChatGPT-powered free Summarizer for Websites, YouTube and PDF", "category": "Text", "subcategory": null, "website_url": "https://www.gistai.tech?utm_source=tool_directory&utm_medium=post&utm_campaign=launch", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 98, "name": "WebChatGPT", "description": "Augment your ChatGPT prompts with relevant results from the web", "category": "Text", "subcategory": null, "website_url": "https://chrome.google.com/webstore/detail/webchatgpt-chatgpt-with-i/lpfemeioodjbpieminkklglpmhlngfcn", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 99, "name": "GPT for Sheets and Docs", "description": "ChatGPT extension for Google Sheets and Google Docs", "category": "Text", "subcategory": null, "website_url": "https://workspace.google.com/marketplace/app/gpt_for_sheets_and_docs/677318054654", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 100, "name": "YouTube Summary with ChatGPT", "description": "Use ChatGPT to summarize YouTube videos", "category": "Text", "subcategory": null, "website_url": "https://chrome.google.com/webstore/detail/youtube-summary-with-chat/nmmicjeknamkfloonkhhcjmomieiodli", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 101, "name": "ChatGPT Prompt Genius", "description": "Discover, share, import, and use the best prompts for ChatGPT & save your chat history locally", "category": "Text", "subcategory": null, "website_url": "https://chrome.google.com/webstore/detail/chatgpt-prompt-genius/jjdnakkfjnnbbckhifcfchagnpofjffo", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 102, "name": "ChatGPT for Search Engines", "description": "Display ChatGPT response alongside Google, Bing, and DuckDuckGo search results", "category": "Text", "subcategory": null, "website_url": "https://chrome.google.com/webstore/detail/chatgpt-for-search-engine/feeonheemodpkdckaljcjogdncpiiban", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 103, "name": "ShareGPT", "description": "Share your ChatGPT conversations and explore conversations shared by others", "category": "Text", "subcategory": null, "website_url": "https://sharegpt.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 104, "name": "Merlin", "description": "ChatGPT Plus extension on all websites", "category": "Text", "subcategory": null, "website_url": "https://merlin.foyer.work/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 105, "name": "ChatGPT Writer", "description": "Generate entire emails and messages using ChatGPT AI", "category": "Text", "subcategory": null, "website_url": "https://chatgptwriter.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 106, "name": "ChatGPT for Jupyter", "description": "Add various helper functions in Jupyter Notebooks and Jupyter Lab, powered by ChatGPT", "category": "Text", "subcategory": null, "website_url": "https://github.com/TiesdeKok/chat-gpt-jupyter-extension", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 107, "name": "editGPT", "description": "Easily proofread, edit, and track changes to your content in chatGPT", "category": "Text", "subcategory": null, "website_url": "https://www.editgpt.app/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 108, "name": "Chatbot UI", "description": "An open source ChatGPT UI. [Source code](https://github.com/mckaywrigley/chatbot-ui)", "category": "Text", "subcategory": null, "website_url": "https://www.chatbotui.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 109, "name": "Forefront", "description": "A Better ChatGPT Experience", "category": "Text", "subcategory": null, "website_url": "https://www.forefront.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 110, "name": "AI Character for GPT", "description": "One click to curate AI chatbot, including ChatGPT, Google Bard to improve AI responses", "category": "Text", "subcategory": null, "website_url": "https://chromewebstore.google.com/detail/ai-character-for-gpt/daoeioifimkjegafelcaljboknjkkohh", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 111, "name": "<PERSON><PERSON>", "description": "Mem is the world's first AI-powered workspace that's personalized to you. Amplify your creativity, automate the mundane, and stay organized automatically", "category": "Text", "subcategory": null, "website_url": "https://mem.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 112, "name": "Taskade", "description": "Build, train, and deploy autonomous AI agents for task management, team collaboration, and workflow automation—all within a unified workspace", "category": "Text", "subcategory": null, "website_url": "https://www.taskade.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 113, "name": "Nekton AI", "description": "Automate your workflows with AI. Describe your workflows step by step in plain language", "category": "Text", "subcategory": null, "website_url": "https://nekton.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 114, "name": "Elephas", "description": "Personal AI writing assistant for the Mac", "category": "Text", "subcategory": null, "website_url": "https://elephas.app/?ref=mahseema-awesome-ai-tools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 115, "name": "<PERSON><PERSON>", "description": "Autonomous AI Assistant for Work", "category": "Text", "subcategory": null, "website_url": "https://lemmy.co/?ref=mahseema-awesome-ai-tools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 116, "name": "Google Sheets Formula Generator", "description": "Forget about frustrating formulas in Google Sheets", "category": "Text", "subcategory": null, "website_url": "https://bettersheets.co/google-sheets-formula-generator?ref=mahseema-awesome-ai-tools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 117, "name": "CreateEasily", "description": "Free speech-to-text tool for content creators that accurately transcribes audio & video files up to 2GB", "category": "Text", "subcategory": null, "website_url": "https://createeasily.com/?ref=mahseema-awesome-ai-tools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 118, "name": "aiPDF", "description": "The most advanced AI document assistant", "category": "Text", "subcategory": null, "website_url": "https://aipdf.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 119, "name": "Summary With AI", "description": "Summarize any long PDF with AI. Comprehensive summaries using information from all pages of a document", "category": "Text", "subcategory": null, "website_url": "https://www.summarywithai.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 120, "name": "<PERSON>", "description": "Stop drowning in emails - <PERSON> prioritizes and automates your email, saving 60% of your time", "category": "Text", "subcategory": null, "website_url": "https://getemil.io?ref=mahseema-awesome-ai-tools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 121, "name": "Pieces", "description": "AI-enabled productivity tool designed to supercharge developer efficiency,with an on-device copilot that helps capture, enrich, and reuse useful materials, streamline collaboration, and solve complex problems through a contextual understanding of dev workflow", "category": "Text", "subcategory": null, "website_url": "https://pieces.app/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 122, "name": "Huntr AI Resume Builder", "description": "Craft the perfect resume, with a little help from AI. <PERSON><PERSON>’s customizable AI Resume Builder will help you craft a well-written, ATS-friendly resume to help you land more interviews", "category": "Text", "subcategory": null, "website_url": "https://huntr.co/product/ai-resume-builder/?ref=mahseema-awesome-ai-tools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 123, "name": "Chat With PDF by Copilot.us", "description": "An AI app that enables dialogue with PDF documents, supporting interactions with multiple files simultaneously through language models", "category": "Text", "subcategory": null, "website_url": "https://copilot.us/apps/chat-with-pdf", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 124, "name": "Recall", "description": "Summarize Anything, Forget Nothing", "category": "Text", "subcategory": null, "website_url": "https://www.getrecall.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 125, "name": "Talently AI", "description": "An Al interviewer that conducts live, conversational interviews and gives real-time evaluations to effortlessly identify top performers and scale your recruitment process", "category": "Text", "subcategory": null, "website_url": "https://interview.talently.ai/?utm_source=mahseema-awesome-ai-tool&utm_medium=c_and_p&utm_campaign=tool-listing", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 126, "name": "TailorTask", "description": "Automate any boring and repetitive task, without having to learn a new tool", "category": "Text", "subcategory": null, "website_url": "https://wwww.tailortask.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 127, "name": "AnkiDecks AI", "description": "Create Flashcards 10x faster. Generate Anki Flashcards from any File or Text with AI", "category": "Text", "subcategory": null, "website_url": "https://anki-decks.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 128, "name": "AI for Google Slides", "description": "AI presentation maker for Google Slides", "category": "Text", "subcategory": null, "website_url": "https://www.aiforgoogleslides.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 129, "name": "FARSITE", "description": "AI-powered Compliance Software for U.S. Government Contractors", "category": "Text", "subcategory": null, "website_url": "https://far.site/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 130, "name": "GOSH", "description": "Free AI Price Tracker - Track any price of any product at any store using AI", "category": "Text", "subcategory": null, "website_url": "https://gosh.app", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 131, "name": "Pomodoro Timer Tools", "description": "Minimal AI-Driven Pomodoro Timer App", "category": "Text", "subcategory": null, "website_url": "https://pomodoro.tools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 132, "name": "BrainSoup", "description": "Multi-agent & multi-LLM native client where AIs can remember, react to events, use tools, leverage local and external resources, and work together autonomously", "category": "Text", "subcategory": null, "website_url": "https://www.nurgo-software.com/products/brainsoup", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 133, "name": "MindPal", "description": "Build your AI Second Brain with a team of AI agents and multi-agent workflow", "category": "Text", "subcategory": null, "website_url": "https://mindpal.space/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 134, "name": "Otter.ai", "description": "A meeting assistant that records audio, writes notes, automatically captures slides, and generates summaries", "category": "Text", "subcategory": null, "website_url": "https://otter.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 135, "name": "Cogram", "description": "Cogram takes automatic notes in virtual meetings and identifies action items", "category": "Text", "subcategory": null, "website_url": "https://www.cogram.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 136, "name": "Sybill", "description": "Sybill generates summaries of sales calls, including next steps, pain points and areas of interest, by combining transcript and emotion-based insights", "category": "Text", "subcategory": null, "website_url": "https://www.sybill.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 137, "name": "Loopin AI", "description": "Loopin is a collaborative meeting workspace that not only enables you to record, transcribe & summaries meetings using AI, but also enables you to auto-organise meeting notes on top of your calendar", "category": "Text", "subcategory": null, "website_url": "https://www.loopinhq.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 138, "name": "Elicit", "description": "Elicit uses language models to help you automate research workflows, like parts of literature review", "category": "Text", "subcategory": null, "website_url": "https://elicit.org/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 139, "name": "genei", "description": "Summarise academic articles in seconds and save 80% on your research times", "category": "Text", "subcategory": null, "website_url": "https://www.genei.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 140, "name": "Explainpaper", "description": "A better way to read academic papers. Upload a paper, highlight confusing text, get an explanation", "category": "Text", "subcategory": null, "website_url": "https://www.explainpaper.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 141, "name": "Galactica", "description": "A large language model for science. Can summarize academic literature, solve math problems, generate Wiki articles, write scientific code, annotate molecules and proteins, and more. [Model API](https://github.com/paperswithcode/galai)", "category": "Text", "subcategory": null, "website_url": "https://galactica.org/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 142, "name": "Consensus", "description": "Consensus is a search engine that uses AI to find answers in scientific research", "category": "Text", "subcategory": null, "website_url": "https://consensus.app/search/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 143, "name": "Sourcely", "description": "Academic Citation Finding Tool with AI", "category": "Text", "subcategory": null, "website_url": "https://www.sourcely.net/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 144, "name": "SciSpace", "description": "AI Chat for scientific PDFs", "category": "Text", "subcategory": null, "website_url": "https://scispace.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 145, "name": "SiteGPT", "description": "Make AI your expert customer support agent", "category": "Text", "subcategory": null, "website_url": "https://sitegpt.ai/?ref=mahseema-awesome-ai-tools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 146, "name": "GPTHelp.ai", "description": "ChatGPT for your website / AI customer support chatbot", "category": "Text", "subcategory": null, "website_url": "https://gpthelp.ai/?ref=mahseema-awesome-ai-tools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 147, "name": "SiteSpeakAI", "description": "Automate your customer support with AI", "category": "Text", "subcategory": null, "website_url": "https://sitespeak.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 148, "name": "Dear AI", "description": "Supercharge Customer Services and boost sales with AI Chatbot", "category": "Text", "subcategory": null, "website_url": "https://www.dearai.online", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 149, "name": "Inline Help", "description": "Answer customer questions before they ask", "category": "Text", "subcategory": null, "website_url": "https://inlinehelp.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 150, "name": "Aidbase", "description": "AI-Powered Support for your SaaS startup", "category": "Text", "subcategory": null, "website_url": "https://www.aidbase.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 151, "name": "EmailTriager", "description": "Use AI to automatically draft email replies in the background", "category": "Text", "subcategory": null, "website_url": "https://www.emailtriager.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 152, "name": "AI Poem Generator", "description": "AI Poem Generator writes a beautiful rhyming poem for you on any subject, given a text prompt", "category": "Text", "subcategory": null, "website_url": "https://www.aipoemgenerator.org", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 153, "name": "Never Jobless LinkedIn Message Generator", "description": "Maximize Your Interview Chances with AI-Powered LinkedIn Messaging", "category": "Text", "subcategory": null, "website_url": "https://neverjobless.com/?ref=mahseema-awesome-ai-tools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 154, "name": "Ollama", "description": "Load and run large LLMs locally to use in your terminal or build your apps", "category": "Text", "subcategory": null, "website_url": "https://ollama.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 155, "name": "co:here", "description": "Cohere provides access to advanced Large Language Models and NLP tools", "category": "Text", "subcategory": null, "website_url": "https://cohere.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 156, "name": "Haystack", "description": "A framework for building NLP applications (e.g. agents, semantic search, question-answering) with language models", "category": "Text", "subcategory": null, "website_url": "https://haystack.deepset.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 157, "name": "<PERSON><PERSON><PERSON>", "description": "Open source Tool for converting user traffic to Test Cases and Data Stubs", "category": "Text", "subcategory": null, "website_url": "https://keploy.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 158, "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "A framework for developing applications powered by language models", "category": "Text", "subcategory": null, "website_url": "https://langchain.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 159, "name": "gpt4all", "description": "A chatbot trained on a massive collection of clean assistant data including code, stories, and dialogue", "category": "Text", "subcategory": null, "website_url": "https://github.com/nomic-ai/gpt4all", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 160, "name": "LMQL", "description": "LMQL is a query language for large language models", "category": "Text", "subcategory": null, "website_url": "https://lmql.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 161, "name": "LlamaIndex", "description": "A data framework for building LLM applications over external data", "category": "Text", "subcategory": null, "website_url": "https://www.llamaindex.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 162, "name": "<PERSON><PERSON>", "description": "Open-source LLM engineering platform that helps teams collaboratively debug, analyze, and iterate on their LLM applications. [#opensource](https://github.com/langfuse/langfuse)", "category": "Text", "subcategory": null, "website_url": "https://langfuse.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 163, "name": "Phoenix", "description": "Open-source tool for ML observability that runs in your notebook environment, by Arize. Monitor and fine-tune LLM, CV, and tabular models", "category": "Text", "subcategory": null, "website_url": "https://phoenix.arize.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 164, "name": "Prediction Guard", "description": "Seamlessly integrate private, controlled, and compliant Large Language Models (LLM) functionality", "category": "Text", "subcategory": null, "website_url": "https://www.predictionguard.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 165, "name": "<PERSON><PERSON>", "description": "Full-stack LLMOps platform to monitor, manage, and improve LLM-based apps", "category": "Text", "subcategory": null, "website_url": "https://portkey.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 166, "name": "OpenAI Downtime Monitor", "description": "Free tool that tracks API uptime and latencies for various OpenAI models and other LLM providers", "category": "Text", "subcategory": null, "website_url": "https://status.portkey.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 167, "name": "ChatWithCloud", "description": "CLI allowing you to interact with AWS Cloud using human language inside your Terminal", "category": "Text", "subcategory": null, "website_url": "https://chatwithcloud.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 168, "name": "SinglebaseCloud", "description": "AI-powered backend platform with Vector DB, DocumentDB, Auth, and more to speed up app development", "category": "Text", "subcategory": null, "website_url": "https://singlebase.cloud", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 169, "name": "Maxim AI", "description": "A generative AI evaluation and observability platform, empowering modern AI teams to ship products with quality, reliability, and speed", "category": "Text", "subcategory": null, "website_url": "https://www.getmaxim.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 170, "name": "Wordware", "description": "A web-hosted IDE where non-technical domain experts work with AI Engineers to build task-specific AI agents. It approaches prompting as a new programming language rather than low/no-code blocks", "category": "Text", "subcategory": null, "website_url": "https://www.wordware.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 171, "name": "CodeRabbit", "description": "An AI-powered code review tool that helps developers improve code quality and productivity", "category": "Text", "subcategory": null, "website_url": "https://coderabbit.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 172, "name": "<PERSON><PERSON>", "description": "Your Operations Co-pilot on Slack/Teams. It assists and prompts oncall with relevant information to debug issues", "category": "Text", "subcategory": null, "website_url": "https://www.pagerly.io", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 173, "name": "He<PERSON><PERSON>", "description": "A Open-source No-Code tool to build your AI Chatbot / Agent (multi-lingual, multi-channel, LLM, NLU, + ability to develop custom extensions)", "category": "Text", "subcategory": null, "website_url": "https://hexabot.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 174, "name": "Plandex", "description": "Open source, terminal-based AI programming engine for complex tasks", "category": "Text", "subcategory": null, "website_url": "https://github.com/plandex-ai/plandex", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 175, "name": "AI/ML API", "description": "AI/ML API gives developers access to 100+ AI models with one API", "category": "Text", "subcategory": null, "website_url": "https://aimlapi.com/?utm_source=github+of+altern.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 176, "name": "GitHub Copilot", "description": "GitHub Copilot uses the OpenAI Codex to suggest code and entire functions in real-time, right from your editor", "category": "Code", "subcategory": null, "website_url": "https://github.com/features/copilot", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 177, "name": "OpenAI Codex", "description": "An AI system by OpenAI that translates natural language to code", "category": "Code", "subcategory": null, "website_url": "https://platform.openai.com/docs/guides/code/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 178, "name": "Ghostwriter", "description": "An AI-powered pair programmer by <PERSON><PERSON>", "category": "Code", "subcategory": null, "website_url": "https://blog.replit.com/ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 179, "name": "Amazon CodeWhisperer", "description": "Build applications faster with the ML-powered coding companion", "category": "Code", "subcategory": null, "website_url": "https://aws.amazon.com/codewhisperer/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 180, "name": "tabnine", "description": "Code faster with whole-line & full-function code completions", "category": "Code", "subcategory": null, "website_url": "https://www.tabnine.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 181, "name": "Stenography", "description": "Automatic code documentation", "category": "Code", "subcategory": null, "website_url": "https://stenography.dev/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 182, "name": "Mintlify", "description": "AI powered documentation writer", "category": "Code", "subcategory": null, "website_url": "https://mintlify.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 183, "name": "Debuild", "description": "AI-powered low-code tool for web apps", "category": "Code", "subcategory": null, "website_url": "https://debuild.app/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 184, "name": "AI2sql", "description": "With AI2sql, engineers and non-engineers can easily write efficient, error-free SQL queries without knowing SQL", "category": "Code", "subcategory": null, "website_url": "https://www.ai2sql.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 185, "name": "CodiumAI", "description": "With CodiumAI, you get non-trivial tests suggested right inside your IDE, so you stay confident when you push", "category": "Code", "subcategory": null, "website_url": "https://www.codium.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 186, "name": "PR-Agent", "description": "AI-powered tool for automated PR analysis, feedback, suggestions, and more", "category": "Code", "subcategory": null, "website_url": "https://github.com/Codium-ai/pr-agent", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 187, "name": "MutableAI", "description": "AI Accelerated Software Development", "category": "Code", "subcategory": null, "website_url": "https://mutable.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 188, "name": "TurboPilot", "description": "A self-hosted copilot clone that uses the library behind llama.cpp to run the 6 billion parameter Salesforce Codegen model in 4 GB of RAM", "category": "Code", "subcategory": null, "website_url": "https://github.com/ravenscroftj/turbopilot", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 189, "name": "GPT-Code UI", "description": "An open-source implementation of OpenAI's ChatGPT Code interpreter", "category": "Code", "subcategory": null, "website_url": "https://github.com/ricklamers/gpt-code-ui", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 190, "name": "MetaGPT", "description": "The Multi-Agent Framework: Given one line Requirement, return PRD, Design, Tasks, Repo", "category": "Code", "subcategory": null, "website_url": "https://github.com/geekan/MetaGPT", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 191, "name": "Marblism", "description": "Generate a SaaS boilerplate from a prompt", "category": "Code", "subcategory": null, "website_url": "https://marblism.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 192, "name": "MutahunterAI", "description": "Accelerate developer productivity and code security with our open-source AI", "category": "Code", "subcategory": null, "website_url": "https://github.com/codeintegrity-ai/mutahunter", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 193, "name": "AI Kernel Explorer", "description": "Explore the Linux kernel source code with AI-generated summaries", "category": "Code", "subcategory": null, "website_url": "https://github.com/mathiscode/ai-kernel-explorer", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 194, "name": "WhoDB", "description": "SQL/NoSQL/Graph/Cache/Object data explorer with AI-powered chat + other useful features", "category": "Code", "subcategory": null, "website_url": "https://github.com/clidey/whodb", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 195, "name": "Best Image AI Tools", "description": "or [Awesome AI Image](https://github.com/xaramore/awesome-ai-image)*", "category": "Image", "subcategory": null, "website_url": "https://github.com/mahseema/awesome-ai-tools/blob/main/IMAGE.md", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 196, "name": "DALL·E 2", "description": "DALL·E 2 by OpenAI is a new AI system that can create realistic images and art from a description in natural language", "category": "Image", "subcategory": null, "website_url": "https://openai.com/dall-e-2/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 197, "name": "Stable Diffusion", "description": "Stable Diffusion by Stability AI is a state-of-the-art text-to-image model that generates images from text. #opensource", "category": "Image", "subcategory": null, "website_url": "https://huggingface.co/CompVis/stable-diffusion-v1-4", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 198, "name": "Midjourney", "description": "Midjourney is an independent research lab exploring new mediums of thought and expanding the imaginative powers of the human species", "category": "Image", "subcategory": null, "website_url": "https://www.midjourney.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 199, "name": "Imagen", "description": "Imagen by Google is a text-to-image diffusion model with an unprecedented degree of photorealism and a deep level of language understanding", "category": "Image", "subcategory": null, "website_url": "https://imagen.research.google/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 200, "name": "Make-A-Scene", "description": "Make-A-Scene by <PERSON><PERSON> is a multimodal generative AI method puts creative control in the hands of people who use it by allowing them to describe and illustrate their vision through both text descriptions and freeform sketches", "category": "Image", "subcategory": null, "website_url": "https://ai.facebook.com/blog/greater-creative-control-for-ai-image-generation/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 201, "name": "DragGAN", "description": "Drag Your GAN: Interactive Point-based Manipulation on the Generative Image Manifold", "category": "Image", "subcategory": null, "website_url": "https://github.com/XingangPan/DragGAN", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 202, "name": "<PERSON><PERSON>", "description": "Generating AI Images", "category": "Image", "subcategory": null, "website_url": "https://www.canva.com/ai-image-generator/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 203, "name": "<PERSON><PERSON><PERSON>", "description": "Craiyon, formerly DALL-E mini, is an AI model that can draw images from any text prompt", "category": "Image", "subcategory": null, "website_url": "https://www.craiyon.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 204, "name": "DreamStudio", "description": "DreamStudio is an easy-to-use interface for creating images using the Stable Diffusion image generation model", "category": "Image", "subcategory": null, "website_url": "https://beta.dreamstudio.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 205, "name": "Artbreeder", "description": "Artbreeder is a new type of creative tool that empowers users creativity by making it easier to collaborate and explore", "category": "Image", "subcategory": null, "website_url": "https://www.artbreeder.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 206, "name": "GauGAN2", "description": "GauGAN2 is a robust tool for creating photorealistic art using a combination of words and drawings since it integrates segmentation mapping, inpainting, and text-to-image production in a single model", "category": "Image", "subcategory": null, "website_url": "http://gaugan.org/gaugan2/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 207, "name": "Magic Eraser", "description": "Remove unwanted things from images in seconds", "category": "Image", "subcategory": null, "website_url": "https://www.magiceraser.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 208, "name": "Imagine by Magic Studio", "description": "A tool by Magic Studio that let's you express yourself by just describing what's on your mind", "category": "Image", "subcategory": null, "website_url": "https://magicstudio.com/imagine", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 209, "name": "Alpaca", "description": "Stable Diffusion Photoshop plugin", "category": "Image", "subcategory": null, "website_url": "https://www.getalpaca.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 210, "name": "Patience.ai", "description": "Patience.ai is an app for creating images with Stable Diffusion, a cutting-edge AI developed by Stability.AI", "category": "Image", "subcategory": null, "website_url": "https://www.patience.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 211, "name": "GenShare", "description": "Generate art in seconds for free. Own and share what you create. A multimedia generative studio, democratizing design and creativity", "category": "Image", "subcategory": null, "website_url": "https://www.genshare.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 212, "name": "Playground AI", "description": "Playground AI is a free-to-use online AI image creator. Use it to create art, social media posts, presentations, posters, videos, logos and more", "category": "Image", "subcategory": null, "website_url": "https://playgroundai.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 213, "name": "Pixelz AI Art Generator", "description": "Pixelz AI Art Generator enables you to create incredible art from text. Stable Diffusion, CLIP Guided Diffusion & PXL·E realistic algorithms available", "category": "Image", "subcategory": null, "website_url": "https://pixelz.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 214, "name": "modyfi", "description": "The image editor you've always wanted. AI-powered creative tools in your browser. Real-time collaboration", "category": "Image", "subcategory": null, "website_url": "https://www.modyfi.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 215, "name": "Ponzu", "description": "Ponzu is your free AI logo generator. Build your brand with creatively designed logos in seconds, using only your imagination", "category": "Image", "subcategory": null, "website_url": "https://www.ponzu.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 216, "name": "PhotoRoom", "description": "Create product and portrait pictures using only your phone. Remove background, change background and showcase products", "category": "Image", "subcategory": null, "website_url": "https://www.photoroom.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 217, "name": "Avatar AI", "description": "Create your own AI-generated avatars", "category": "Image", "subcategory": null, "website_url": "https://avatarai.me/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 218, "name": "ClipDrop", "description": "Create professional visuals without a photo studio, powered by [stability.ai](https://stability.ai/)", "category": "Image", "subcategory": null, "website_url": "https://clipdrop.co/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 219, "name": "Lensa", "description": "An all-in-one image editing app that includes the generation of personalized avatars using Stable Diffusion", "category": "Image", "subcategory": null, "website_url": "https://prisma-ai.com/lensa", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 220, "name": "RunDiffusion", "description": "Cloud-based workspace for creating AI-generated art", "category": "Image", "subcategory": null, "website_url": "https://rundiffusion.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 221, "name": "Human Generator", "description": "AI generator or realistic looking photos of humans", "category": "Image", "subcategory": null, "website_url": "https://generated.photos/human-generator", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 222, "name": "VectorArt.ai", "description": "Create vector images with AI", "category": "Image", "subcategory": null, "website_url": "https://vectorart.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 223, "name": "StockPhotoAI.net", "description": "Great stock photos, made for you", "category": "Image", "subcategory": null, "website_url": "https://www.stockphotoai.net/?ref=mahseema-awesome-ai-tools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 224, "name": "Room Reinvented", "description": "Transform your room effortlessly with Room Reinvented! Upload a photo and let AI create over 30 stunning interior styles. Elevate your space today", "category": "Image", "subcategory": null, "website_url": "https://roomreinvented.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 225, "name": "Gensbot", "description": "Gensbot uses AI to craft personalised printed merchandise. One prompt creates one unique product to fit your needs", "category": "Image", "subcategory": null, "website_url": "https://gensbot.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 226, "name": "PlantPhotoAI", "description": "free AI-generated plant images", "category": "Image", "subcategory": null, "website_url": "https://www.plantphotoai.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 227, "name": "RepublicLabs.AI", "description": "multi-model simultaneous generation from a single prompt, fully unrestricted and packed with the latest greatest AI models", "category": "Image", "subcategory": null, "website_url": "https://republiclabs.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 228, "name": "Black Headshots", "description": "AI headshots generator for black professionals", "category": "Image", "subcategory": null, "website_url": "https://www.blackheadshots.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 229, "name": "Pixvify AI", "description": "Free realistic AI photo generator platform", "category": "Image", "subcategory": null, "website_url": "https://pixvify.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 230, "name": "Pawtrait", "description": "AI Pet Portraits", "category": "Image", "subcategory": null, "website_url": "https://www.pawtrait.art/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 231, "name": "iColoring", "description": "Free AI Coloring Pages Generator", "category": "Image", "subcategory": null, "website_url": "https://icoloring.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 232, "name": "Suit me Up", "description": "Generate pictures of you wearing a suit with AI", "category": "Image", "subcategory": null, "website_url": "https://suitmeup.pictures/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 233, "name": "AI Photo Forge", "description": "A Telegram bot to generate AI pictures of you", "category": "Image", "subcategory": null, "website_url": "https://aiphotoforge.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 234, "name": "Brandmark", "description": "AI-based logo design tool", "category": "Image", "subcategory": null, "website_url": "https://brandmark.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 235, "name": "Gamma", "description": "Create beautiful presentations and webpages with none of the formatting and design work", "category": "Image", "subcategory": null, "website_url": "https://gamma.app/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 236, "name": "Microsoft Designer", "description": "Stunning designs in a flash", "category": "Image", "subcategory": null, "website_url": "https://designer.microsoft.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 237, "name": "SVGStud.io", "description": "AI-based SVG Generation and Semantic Seach", "category": "Image", "subcategory": null, "website_url": "https://svgstud.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 238, "name": "Lexica", "description": "Stable Diffusion search engine", "category": "Image", "subcategory": null, "website_url": "https://lexica.art/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 239, "name": "Libraire", "description": "The largest library of AI-generated images", "category": "Image", "subcategory": null, "website_url": "https://libraire.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 240, "name": "KREA", "description": "Explore millions of AI-generated images and create collections of prompts. Featuring Stable Diffusion generations", "category": "Image", "subcategory": null, "website_url": "https://www.krea.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 241, "name": "OpenArt", "description": "Search 10M+ of prompts, and generate AI art via Stable Diffusion, DALL·E 2", "category": "Image", "subcategory": null, "website_url": "https://openart.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 242, "name": "Phygital", "description": "Built-in templates for generating or editing any pictures. Moreover, you can create your own design", "category": "Image", "subcategory": null, "website_url": "https://app.phygital.plus/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 243, "name": "Civitai", "description": "Community-driven AI model sharing tool", "category": "Image", "subcategory": null, "website_url": "https://civitai.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 244, "name": "Stable Diffusion Models", "description": "A comprehensive list of Stable Diffusion checkpoints on rentry.org", "category": "Image", "subcategory": null, "website_url": "https://rentry.org/sdmodels", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 245, "name": "Stable Horde", "description": "A crowdsourced distributed cluster of Stable Diffusion workers", "category": "Image", "subcategory": null, "website_url": "https://stablehorde.net/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 246, "name": "DiffusionDB", "description": "A list of all public apps, developer tools, guides and plugins for Stable Diffusion. [Airtable version](https://airtable.com/shr0HlBwbw3nZ8Ht3/tblxOCylXV8ynh7ti)", "category": "Image", "subcategory": null, "website_url": "https://diffusiondb.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 247, "name": "PublicPrompts", "description": "A collection of free prompts for Stable Diffusion", "category": "Image", "subcategory": null, "website_url": "https://publicprompts.art/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 248, "name": "Stableboost", "description": "Stableboost is a Stable Diffusion WebUI that lets you quickly generate a lot of images so you can find the perfect ones", "category": "Image", "subcategory": null, "website_url": "https://stableboost.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 249, "name": "Hugging Face Diffusion Models Course", "description": "Python materials for the online course on diffusion models by [@huggingface](https://github.com/huggingface)", "category": "Image", "subcategory": null, "website_url": "https://github.com/huggingface/diffusion-models-class", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 250, "name": "RunwayML", "description": "Magical AI tools, realtime collaboration, precision editing, and more. Your next-generation content creation suite", "category": "Video", "subcategory": null, "website_url": "https://runwayml.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 251, "name": "Synthesia", "description": "Create videos from plain text in minutes", "category": "Video", "subcategory": null, "website_url": "https://www.synthesia.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 252, "name": "Rephrase AI", "description": "Rephrase's technology enables hyper-personalized video creation at scale that drive engagement and business efficiencies", "category": "Video", "subcategory": null, "website_url": "https://www.rephrase.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 253, "name": "Hour One", "description": "Turn text into video, featuring virtual presenters, automatically", "category": "Video", "subcategory": null, "website_url": "https://hourone.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 254, "name": "D-ID", "description": "Create and interact with talking avatars at the touch of a button", "category": "Video", "subcategory": null, "website_url": "https://www.d-id.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 255, "name": "ShortVideoGen", "description": "Create short videos with audio using text prompts", "category": "Video", "subcategory": null, "website_url": "https://shortgen.video/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 256, "name": "Clipwing", "description": "A tool for cutting long videos into dozens of short clips", "category": "Video", "subcategory": null, "website_url": "https://clipwing.pro/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 257, "name": "Recast Studio", "description": "AI powered podcast marketing assistant", "category": "Video", "subcategory": null, "website_url": "https://recast.studio", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 258, "name": "Based AI", "description": "AI Intuitive Interface for Video creating", "category": "Video", "subcategory": null, "website_url": "https://www.basedlabs.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 259, "name": "Awesome AI Music", "description": "", "category": "Related Awesome Lists", "subcategory": null, "website_url": "https://github.com/xaramore/awesome-ai-music", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 260, "name": "Descript Overdub", "description": "[Review](https://theresanai.com/descript-overdub) - Seamlessly integrates with Descript’s transcription and editing tools, ideal for content creators needing quick voiceovers", "category": "Audio", "subcategory": null, "website_url": "https://www.descript.com/overdub", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 261, "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "[Review](https://theresanai.com/respeecher) -  A professional tool widely used in the entertainment industry to create emotion-rich, realistic voice clones", "category": "Audio", "subcategory": null, "website_url": "https://www.respeecher.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 262, "name": "ElevenLabs", "description": "[Review](https://theresanai.com/elevenlabs) - Known for ultra-realistic voice cloning and emotion modeling, setting a new standard in AI-driven voice synthesis", "category": "Audio", "subcategory": null, "website_url": "https://elevenlabs.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 263, "name": "Resemble AI", "description": "AI voice generator and voice cloning for text to speech", "category": "Audio", "subcategory": null, "website_url": "https://www.resemble.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 264, "name": "iSpeech", "description": "[Review](https://theresanai.com/ispeech) - A versatile solution for corporate applications with support for a wide array of languages and voices", "category": "Audio", "subcategory": null, "website_url": "https://www.ispeech.org/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 265, "name": "Veritone Voice", "description": "[Review](https://theresanai.com/veritone-voice) - Focuses on maintaining brand consistency with highly customizable voice cloning used in media and entertainment", "category": "Audio", "subcategory": null, "website_url": "https://www.veritone.com/solutions/voice/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 266, "name": "Microsoft Azure Neural TTS", "description": "Review - Scalable and highly customizable, ideal for integration into enterprise applications", "category": "Audio", "subcategory": null, "website_url": "https://azure.microsoft.com/en-us/services/cognitive-services/text-to-speech/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 267, "name": "WellSaid Labs", "description": "[Review](https://theresanai.com/wellsaid-labs) - Gaining traction for its natural-sounding voiceovers, particularly in corporate training and e-learning", "category": "Audio", "subcategory": null, "website_url": "https://www.wellsaidlabs.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 268, "name": "Lovo.ai", "description": "[Review](https://theresanai.com/lovo-ai) - A compelling choice for creative professionals, especially useful in ads and explainer videos", "category": "Audio", "subcategory": null, "website_url": "https://www.lovo.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 269, "name": "Zenmic.com", "description": "An app to generate podcast eposode ( script + Audio ) using AI", "category": "Audio", "subcategory": null, "website_url": "https://zenmic.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 270, "name": "There's an AI AI Music Generation Tools list", "description": "*", "category": "Audio", "subcategory": null, "website_url": "https://theresanai.com/category/music-generation", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 271, "name": "Splash Pro", "description": "[Review](https://theresanai.com/splash-pro) - A versatile platform offering intuitive music creation tools for all skill levels", "category": "Audio", "subcategory": null, "website_url": "https://www.splashpro.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 272, "name": "AIVA", "description": "[Review](https://theresanai.com/aiva) - AI composer specializing in classical and cinematic music creation", "category": "Audio", "subcategory": null, "website_url": "https://www.aiva.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 273, "name": "<PERSON><PERSON>", "description": "A royalty-free music ecosystem for content creators, brands and developers", "category": "Audio", "subcategory": null, "website_url": "https://mubert.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 274, "name": "Soundraw", "description": "[Review](https://theresanai.com/soundraw) - Allows users to customize music compositions based on mood and style", "category": "Audio", "subcategory": null, "website_url": "https://soundraw.io", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 275, "name": "Beatoven.ai", "description": "[Review](https://theresanai.com/beatoven-ai) - AI-driven music generation focused on evoking specific emotions", "category": "Audio", "subcategory": null, "website_url": "https://www.beatoven.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 276, "name": "<PERSON><PERSON>", "description": "[Review](https://theresanai.com/boomy) - Democratizes music creation with quick track generation and monetization", "category": "Audio", "subcategory": null, "website_url": "https://www.boomy.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 277, "name": "Ecrett Music", "description": "[Review](https://theresanai.com/ecrett-music) - Designed for video creators, offering royalty-free music", "category": "Audio", "subcategory": null, "website_url": "https://www.ecrettmusic.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 278, "name": "Loudly", "description": "[Review](https://theresanai.com/loudly) - Combines AI music generation with a social platform for collaboration", "category": "Audio", "subcategory": null, "website_url": "https://www.loudly.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 279, "name": "Soundful", "description": "[Review](https://theresanai.com/soundful) - High-quality, royalty-free music for content creators", "category": "Audio", "subcategory": null, "website_url": "https://www.soundful.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 280, "name": "Marketing List", "description": "*", "category": "Audio", "subcategory": null, "website_url": "https://github.com/mahseema/awesome-ai-tools/blob/main/marketing.md", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 281, "name": "Jasper AI", "description": "** - AI-powered tool for generating marketing content like blogs, emails, and ad copy", "category": "Audio", "subcategory": null, "website_url": "https://www.jasper.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 282, "name": "Mutiny", "description": "** - Personalization platform to improve website conversions using AI", "category": "Audio", "subcategory": null, "website_url": "https://www.mutinyhq.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 283, "name": "Clearbit", "description": "** - Lead enrichment and data intelligence platform", "category": "Audio", "subcategory": null, "website_url": "https://clearbit.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 284, "name": "Seventh Sense", "description": "** - AI tool for email send time optimization", "category": "Audio", "subcategory": null, "website_url": "https://www.theseventhsense.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 285, "name": "Smartly.io", "description": "** - Automates social media ad creation and optimization", "category": "Audio", "subcategory": null, "website_url": "https://www.smartly.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 286, "name": "Adzooma", "description": "** - AI-powered PPC campaign management platform", "category": "Audio", "subcategory": null, "website_url": "https://www.adzooma.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 287, "name": "<PERSON><PERSON><PERSON>", "description": "** - AI tool that generates optimized marketing copy", "category": "Audio", "subcategory": null, "website_url": "https://www.phrasee.co/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 288, "name": "Crimson Hexagon", "description": "** - AI-based social media sentiment analysis platform", "category": "Audio", "subcategory": null, "website_url": "https://www.crimsonhexagon.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 289, "name": "MarketMuse", "description": "** - SEO content optimization platform using AI", "category": "Audio", "subcategory": null, "website_url": "https://www.marketmuse.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 290, "name": "Chatfuel", "description": "** - AI-driven chatbot for automating customer engagement on Messenger", "category": "Audio", "subcategory": null, "website_url": "https://www.chatfuel.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 291, "name": "LogicBalls", "description": "** - An AI-powered writing tool to create any type of content and supercharge your productivity", "category": "Audio", "subcategory": null, "website_url": "https://logicballs.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 292, "name": "<PERSON>", "description": "** - AI tools for designers and marketers", "category": "Audio", "subcategory": null, "website_url": "https://www.getrupert.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 293, "name": "PersonaForce", "description": "** - Create and chat with AI buyer personas for smarter marketing", "category": "Audio", "subcategory": null, "website_url": "https://personaforce.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 294, "name": "AICaller.io", "description": "AICaller is a simple-to-use automated bulk calling solution that uses the latest Generative AI technology to trigger phone calls for you and get things done. It can do things like lead qualification, data gathering over phone calls, and much more. It comes with a powerful API, low cost pricing and free trial", "category": "Audio", "subcategory": null, "website_url": "https://aicaller.io/?ref=v", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 295, "name": "Cald.ai", "description": "AI based calling agents for outbound and inbound phone calls", "category": "Audio", "subcategory": null, "website_url": "https://cald.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 296, "name": "<PERSON>", "description": "AI Phone Answering Service", "category": "Audio", "subcategory": null, "website_url": "https://heyrosie.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 297, "name": "Eleven Labs", "description": "AI voice generator", "category": "Audio", "subcategory": null, "website_url": "https://beta.elevenlabs.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 298, "name": "WellSaid", "description": "Convert text to voice in real time", "category": "Audio", "subcategory": null, "website_url": "https://wellsaidlabs.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 299, "name": "Play.ht", "description": "AI Voice Generator. Generate realistic Text to Speech voice over online with AI. Convert text to audio", "category": "Audio", "subcategory": null, "website_url": "https://play.ht/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 300, "name": "<PERSON><PERSON>", "description": "Generative AI for Voice", "category": "Audio", "subcategory": null, "website_url": "https://coqui.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 301, "name": "podcast.ai", "description": "A podcast that is entirely generated by artificial intelligence, powered by Play.ht text-to-voice AI", "category": "Audio", "subcategory": null, "website_url": "https://podcast.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 302, "name": "VALL-E X", "description": "A cross-lingual neural codec language model for cross-lingual speech synthesis", "category": "Audio", "subcategory": null, "website_url": "https://vallex-demo.github.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 303, "name": "TorToiSe", "description": "A multi-voice text-to-speech system trained with an emphasis on quality. #opensource", "category": "Audio", "subcategory": null, "website_url": "https://github.com/neonbjb/tortoise-tts", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 304, "name": "Bark", "description": "A transformer-based text-to-audio model. #opensource", "category": "Audio", "subcategory": null, "website_url": "https://github.com/suno-ai/bark", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 305, "name": "CustomPod.io", "description": "Generate daily news podcasts only on the topics you care about", "category": "Audio", "subcategory": null, "website_url": "https://custompod.io", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 306, "name": "Harmonai", "description": "We are a community-driven organization releasing open-source generative audio tools to make music production more accessible and fun for everyone", "category": "Audio", "subcategory": null, "website_url": "https://www.harmonai.org/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 307, "name": "MusicLM", "description": "A model by Google Research for generating high-fidelity music from text descriptions", "category": "Audio", "subcategory": null, "website_url": "https://google-research.github.io/seanet/musiclm/examples/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 308, "name": "Taranify", "description": "Using AI, Taranify finds you Spotify playlists, Netflix shows, Books & Foods you'd enjoy when you don't exactly know what you want", "category": "Other", "subcategory": null, "website_url": "https://www.taranify.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 309, "name": "Diagram", "description": "Magical new ways to design products", "category": "Other", "subcategory": null, "website_url": "https://diagram.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 310, "name": "PromptBase", "description": "A marketplace for buying and selling quality prompts for DALL·E, GPT-3, Midjourney, Stable Diffusion", "category": "Other", "subcategory": null, "website_url": "https://promptbase.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 311, "name": "This Image Does Not Exist", "description": "Test your ability to tell if an image is human or computer generated", "category": "Other", "subcategory": null, "website_url": "https://thisimagedoesnotexist.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 312, "name": "Have I Been Trained?", "description": "Check if your image has been used to train popular AI art models", "category": "Other", "subcategory": null, "website_url": "https://haveibeentrained.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 313, "name": "AI Dungeon", "description": "A text-based adventure-story game you direct (and star in) while the AI brings it to life", "category": "Other", "subcategory": null, "website_url": "https://aidungeon.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 314, "name": "Clickable", "description": "Generate ads in seconds with AI. Beautiful, brand-consistent, and highly converting ads for all marketing channels", "category": "Other", "subcategory": null, "website_url": "https://www.clickable.so/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 315, "name": "Scale Spellbook", "description": "Build, compare, and deploy large language model apps with Scale Spellbook", "category": "Other", "subcategory": null, "website_url": "https://scale.com/spellbook", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 316, "name": "<PERSON><PERSON><PERSON>", "description": "AI-generated gaming assets", "category": "Other", "subcategory": null, "website_url": "https://www.scenario.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 317, "name": "Teleprompter", "description": "An on-device AI for your meetings that listens to you and makes charismatic quote suggestions", "category": "Other", "subcategory": null, "website_url": "https://github.com/danielgross/teleprompter", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 318, "name": "FinChat", "description": "Using AI, FinChat generates answers to questions about public companies and investors", "category": "Other", "subcategory": null, "website_url": "https://finchat.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 319, "name": "Petals", "description": "BitTorrent style platform for running AI models in a distributed way", "category": "Other", "subcategory": null, "website_url": "https://github.com/bigscience-workshop/petals", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 320, "name": "Shotstack Workflows", "description": "No-code, automation workflow tool for building Generative AI media applications", "category": "Other", "subcategory": null, "website_url": "https://shotstack.io/product/workflows/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 321, "name": "Aispect", "description": "New way to experience events", "category": "Other", "subcategory": null, "website_url": "https://aispect.io/?ref=mahseema-awesome-ai-tools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 322, "name": "PressPulse AI", "description": "Get personalized media coverage leads every morning", "category": "Other", "subcategory": null, "website_url": "https://www.presspulse.ai/?ref=mahseema-awesome-ai-tools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 323, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "AI-based customer research via Reddit. Discover problems to solve, sentiment on current solutions, and people who want to buy your product", "category": "Other", "subcategory": null, "website_url": "https://gummysearch.com/?ref=mahseema-awesome-ai-tools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 324, "name": "<PERSON><PERSON><PERSON>", "description": "The all-in-one, AI-powered LinkedIn tool", "category": "Other", "subcategory": null, "website_url": "https://taplio.com/?ref=mahseema-awesome-ai-tools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 325, "name": "PromptPal", "description": "Search for prompts and bots, then use them with your favorite AI. All in one place", "category": "Other", "subcategory": null, "website_url": "https://promptpal.net", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 326, "name": "FairyTailAI", "description": "Personalized bedtime story generator", "category": "Other", "subcategory": null, "website_url": "https://fairytailai.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 327, "name": "Myriad", "description": "Scale your content creation and get the best writing from ChatGPT, Copilot, and other AIs. Build and fine-tune prompts for any kind of content, from long-form to ads and email", "category": "Other", "subcategory": null, "website_url": "https://www.namepepper.com/free-tools/ai-content-prompt-tool", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 328, "name": "GradGPT", "description": "AI tools to simplify college applications. Review applications, draft essays, find universities and requirements and more", "category": "Other", "subcategory": null, "website_url": "https://www.gradgpt.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 329, "name": "Code to Flow", "description": "Visualize, Analyze, and Understand Your Code flow. Turn Code into Interactive Flowcharts with AI. Simplify Complex Logic Instantly", "category": "Other", "subcategory": null, "website_url": "https://codetoflow.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 330, "name": "AI-Flow", "description": "Connect multiple AI models easily", "category": "Other", "subcategory": null, "website_url": "https://ai-flow.net/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 331, "name": "Architecture Helper", "description": "Analyze any building architecture, and generate your own custom styles, in seconds", "category": "Other", "subcategory": null, "website_url": "https://architecturehelper.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 332, "name": "VocalReplica", "description": "AI-Powered Vocal and Instrumental Isolation for Your Favorite Tracks", "category": "Other", "subcategory": null, "website_url": "https://vocalreplica.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 333, "name": "AI Wedding Toast", "description": "Generate a personalized wedding speech with <PERSON>", "category": "Other", "subcategory": null, "website_url": "https://aiweddingtoast.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 334, "name": "Interviews <PERSON><PERSON>", "description": "Your Personal Interview Prep & Copilot", "category": "Other", "subcategory": null, "website_url": "https://www.interviews.chat/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 335, "name": "Context Data", "description": "Data Processing & ETL infrastructure for Generative AI applications", "category": "Other", "subcategory": null, "website_url": "https://contextdata.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 336, "name": "ezJobs", "description": "Automated job search and applications", "category": "Other", "subcategory": null, "website_url": "https://www.getezjobs.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 337, "name": "<PERSON>mp<PERSON>", "description": "AI driven answers to SaaS research questions", "category": "Other", "subcategory": null, "website_url": "https://www.getwhys.io/compass", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 338, "name": "Adon AI", "description": "CV screening automation and blind CV generator, AI backed ATS", "category": "Other", "subcategory": null, "website_url": "https://adon-web.awakast.com/en/recruiter/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 339, "name": "<PERSON><PERSON><PERSON>", "description": "Persuva is the AI-driven platform to create persuasive, high-converting ad copy at scale", "category": "Other", "subcategory": null, "website_url": "https://persuva.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 340, "name": "Interview Solver", "description": "Ace your live coding interviews with our AI Copilot", "category": "Other", "subcategory": null, "website_url": "https://interviewsolver.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 341, "name": "Socialsonic", "description": "AI LinkedIn Coach: Personalized content, trends & scheduling", "category": "Other", "subcategory": null, "website_url": "https://socialsonic.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 342, "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> turns your text into visuals so sharing your ideas is quick and effective", "category": "Other", "subcategory": null, "website_url": "https://www.napkin.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 343, "name": "<PERSON><PERSON>", "description": "AI Exam Generator", "category": "Other", "subcategory": null, "website_url": "https://www.examsamur.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 344, "name": "AI Watermark Remover", "description": "Remove watermarks from images and videos", "category": "Other", "subcategory": null, "website_url": "https://aiwatermarkremover.io/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 345, "name": "AISaver", "description": "Collection of AI Powered Video and Photo Tools", "category": "Other", "subcategory": null, "website_url": "https://aisaver.io", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 346, "name": "Harbor", "description": "run LLM backends, APIs, frontends, and services with one command", "category": "Other", "subcategory": null, "website_url": "https://github.com/av/harbor", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 347, "name": "LangMagic", "description": "Learn languages from native content", "category": "Other", "subcategory": null, "website_url": "https://easytolearn.io", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 348, "name": "fynk", "description": "AI powered contract management software", "category": "Other", "subcategory": null, "website_url": "https://fynk.com/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 349, "name": "LooksMax AI", "description": "Find out how hot you are using AI", "category": "Other", "subcategory": null, "website_url": "https://looksmax.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 350, "name": "Learn Prompting", "description": "A free, open-source course on communicating with artificial intelligence", "category": "Learning resources", "subcategory": null, "website_url": "https://learnprompting.org/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 351, "name": "Prompt Engineering Guide", "description": "Guide and resources for prompt engineering", "category": "Learning resources", "subcategory": null, "website_url": "https://github.com/dair-ai/Prompt-Engineering-Guide", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 352, "name": "ChatGPT prompt engineering for developers", "description": "A short course by <PERSON> (OpenAI) and <PERSON> (DeepLearning.AI)", "category": "Learning resources", "subcategory": null, "website_url": "https://www.deeplearning.ai/short-courses/chatgpt-prompt-engineering-for-developers/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 353, "name": "OpenAI Cookbook", "description": "Examples and guides for using the OpenAI API", "category": "Learning resources", "subcategory": null, "website_url": "https://github.com/openai/openai-cookbook", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 354, "name": "<PERSON> AI Safety", "description": "Youtube channel about AI safety", "category": "Learning resources", "subcategory": null, "website_url": "https://www.youtube.com/@RobertMilesAI", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 355, "name": "Roadmap", "description": "A roadmap connecting many of the most important concepts in machine learning, how to learn them, and what tools to use to perform them", "category": "Learn AI free", "subcategory": null, "website_url": "https://github.com/mrdbourke/machine-learning-roadmap", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 356, "name": "<PERSON>’s Machine Learning at Stanford University", "description": "<PERSON><PERSON>s gentle introduction to machine learning course is perfect for engineers who want a foundational overview of key concepts in the field", "category": "Learn AI free", "subcategory": null, "website_url": "https://www.coursera.org/learn/machine-learning", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 357, "name": "<PERSON>’s Introduction To Machine Learning", "description": "robust introduction to the subject and also the foundation for a Data Analyst “nanodegree” certification sponsored by Facebook and MongoDB", "category": "Learn AI free", "subcategory": null, "website_url": "https://www.udacity.com/course/intro-to-machine-learning--ud120", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 358, "name": "AI and Machine Learning Roadmaps", "description": "Roadmaps featuring essential concepts, learning methods, and the tools to put them into practice", "category": "Learn AI free", "subcategory": null, "website_url": "https://www.scaler.com/blog/category/artificial-intelligence-machine-learning/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 359, "name": "How To Learn Artificial Intelligence (AI)?", "description": "provides a step-by-step guide for beginners to understand and develop AI skills. It covers foundational topics like programming (Python), mathematics, and machine learning, progressing to advanced concepts such as deep learning and neural networks", "category": "Learn AI free", "subcategory": null, "website_url": "https://www.appliedaicourse.com/blog/how-to-learn-artificial-intelligence-ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 360, "name": "<PERSON>’s Neural Networks For Machine Learning", "description": "it is now removed from cousrea but still check these list", "category": "Learn AI free", "subcategory": null, "website_url": "https://medium.com/kaggle-blog", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 361, "name": "<PERSON>’s Fast.ai & Data Institute Certificates", "description": "The in-person certificate courses are not free, but all of the content is available on Fast.ai as MOOCs", "category": "Learn AI free", "subcategory": null, "website_url": "https://www.fast.ai/", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 362, "name": "coursera-deep-learning-specialization", "description": "Notes, programming assignments and quizzes from all courses within the Coursera Deep Learning specialization offered by deeplearning.ai", "category": "Learn AI free", "subcategory": null, "website_url": "https://github.com/pratham5368/coursera-deep-learning-specialization", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 363, "name": "tensorflow", "description": "all important notes to learn pytorch with all the examples in google colab", "category": "Learn AI free", "subcategory": null, "website_url": "https://github.com/pratham5368/Tecnologies-I-Learn/tree/main/31-pytorch", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 364, "name": "NVIDIA Omniverse AI Animal Explorer Extension", "description": "AI Animal Explorer is an Omniverse extension that enables creators to quickly prototype unique 3D animal meshes", "category": "NVIDIA Platform Extensions", "subcategory": null, "website_url": "https://docs.omniverse.nvidia.com/extensions/latest/ext_animal-explorer.html#installation", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 365, "name": "Altern", "description": "Find Best AI Tools", "category": "Related Awesome Lists", "subcategory": null, "website_url": "https://altern.ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 366, "name": "Awesome AI Models", "description": "A curated list of top AI models and LLMs", "category": "Related Awesome Lists", "subcategory": null, "website_url": "https://github.com/dariubs/awesome-ai-models", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 367, "name": "There's An AI", "description": "Frontpage of AI", "category": "Related Awesome Lists", "subcategory": null, "website_url": "https://theresanai.com", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 368, "name": "Awesome AI Books", "description": "Curated List of Top AI and ML Books", "category": "Related Awesome Lists", "subcategory": null, "website_url": "https://github.com/mahseema/aibooks", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 369, "name": "AI for Productivity", "description": "Curated List of AI Apps for productivity", "category": "Related Awesome Lists", "subcategory": null, "website_url": "https://productivity.directory/category/ai", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 370, "name": "Workflow Automation Softwares", "description": "Curated List of Workflow Automation Apps And Tools", "category": "Related Awesome Lists", "subcategory": null, "website_url": "https://productivity.directory/category/workflow-automation", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": NaN, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 371, "name": "Awesome Workflow Automation", "description": "Curated List of Workflow Automation Apps And Tools", "category": "Related Awesome Lists", "subcategory": null, "website_url": "https://github.com/dariubs/awesome-workflow-automation", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 372, "name": "Awesome Marketing", "description": "", "category": "Related Awesome Lists", "subcategory": null, "website_url": "https://github.com/marketingtoolslist/awesome-marketing", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 373, "name": "Awesome AI SEO", "description": "", "category": "Related Awesome Lists", "subcategory": null, "website_url": "https://github.com/xaramore/awesome-ai-seo", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 374, "name": "Awesome AI Marketing", "description": "", "category": "Related Awesome Lists", "subcategory": null, "website_url": "https://github.com/sarahdanesh/awesome-ai-marketing", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 375, "name": "Awesome AI Image", "description": "", "category": "Related Awesome Lists", "subcategory": null, "website_url": "https://github.com/xaramore/awesome-ai-image", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 376, "name": "Awesome AI Video", "description": "", "category": "Related Awesome Lists", "subcategory": null, "website_url": "https://github.com/xaramore/awesome-ai-video", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 377, "name": "Awesome AI-Powered Developer Tools", "description": "Curated list of AI-powered developer tools", "category": "Related Awesome Lists", "subcategory": null, "website_url": "https://github.com/jamesmurdza/awesome-ai-devtools", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 1.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}, {"id": 378, "name": "Learn to use AI like a Pro", "description": "", "category": "AI Tools", "subcategory": null, "website_url": "", "pricing_model": null, "pricing_details": null, "features": null, "rating": null, "reviews_count": null, "tags": null, "use_cases": null, "api_available": null, "open_source": 0.0, "free_tier": NaN, "trial_available": null, "image_url": null, "video_url": null, "source_website": "opentools.ai", "scraped_date": "2025-07-13 17:32:38", "last_updated": "2025-07-13 17:32:38"}], "github_repos": [{"id": 1, "name": "awesome-ai-tools", "full_name": "mahseema/awesome-ai-tools", "description": "A curated list of Artificial Intelligence Top Tools", "url": "https://api.github.com/repos/mahseema/awesome-ai-tools", "homepage": "", "language": null, "stars": 2746, "forks": 551, "open_issues": 181, "license": "MIT License", "topics": "ai,ai-agent,ai-agents,ai-assistant,ai-tools,ai-tools-list,ai-top-tools,awesome,awesome-ai,awesome-ai-tools,awesome-list,bestofai,easywithai,futurepedia,futuretools,machine-learning,ml,mlops,theresanaiforthat,top-ai-tools", "created_at": "2023-08-25T21:51:55Z", "updated_at": "2025-07-13T15:19:16Z", "size": 332, "watchers": 2746, "has_wiki": 1, "has_pages": 0, "archived": 0, "disabled": 0, "pushed_at": "2025-04-03T16:46:36Z", "clone_url": "https://github.com/mahseema/awesome-ai-tools.git", "ssh_url": "**************:mahseema/awesome-ai-tools.git", "html_url": "https://github.com/mahseema/awesome-ai-tools", "scraped_date": "2025-07-13 17:32:26"}], "huggingface_models": [{"id": 1, "model_id": "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B", "model_name": "DeepSeek-R1-<PERSON><PERSON><PERSON>-<PERSON><PERSON>-32B", "author": "deepseek-ai", "description": "", "pipeline_tag": "text-generation", "task": null, "library_name": "transformers", "language": "", "license": "", "tags": "transformers,safetensors,qwen2,text-generation,conversational,arxiv:2501.12948,license:mit,autotrain_compatible,text-generation-inference,endpoints_compatible,region:us", "downloads": 415253, "likes": 1415, "created_at": "2025-01-20T09:19:00.000Z", "last_modified": "2025-02-24T03:31:29.000Z", "private": 0, "gated": 0, "inference": null, "model_size": null, "parameters": "32B parameters", "model_url": "https://huggingface.co/deepseek-ai/DeepSeek-R1-Dist<PERSON>-Qwen-32B", "scraped_date": "2025-07-13 17:32:28"}, {"id": 2, "model_id": "meta-llama/Llama-3.1-8B-Instruct", "model_name": "Llama-3.1-8B-Instruct", "author": "meta-llama", "description": "", "pipeline_tag": "text-generation", "task": null, "library_name": "transformers", "language": "", "license": "", "tags": "transformers,safetensors,llama,text-generation,facebook,meta,pytorch,llama-3,conversational,en,de,fr,it,pt,hi,es,th,arxiv:2204.05149,base_model:meta-llama/Llama-3.1-8B,base_model:finetune:meta-llama/Llama-3.1-8B,license:llama3.1,autotrain_compatible,text-generation-inference,endpoints_compatible,region:us", "downloads": 5444987, "likes": 4286, "created_at": "2024-07-18T08:56:00.000Z", "last_modified": "2024-09-25T17:00:57.000Z", "private": 0, "gated": "manual", "inference": null, "model_size": null, "parameters": "8B parameters", "model_url": "https://huggingface.co/meta-llama/Llama-3.1-8B-Instruct", "scraped_date": "2025-07-13 17:32:29"}, {"id": 3, "model_id": "black-forest-labs/FLUX.1-dev", "model_name": "FLUX.1-dev", "author": "black-forest-labs", "description": "", "pipeline_tag": "text-to-image", "task": null, "library_name": "diffusers", "language": "", "license": "", "tags": "diffusers,safetensors,text-to-image,image-generation,flux,en,license:other,endpoints_compatible,diffusers:FluxPipeline,region:us", "downloads": 1483001, "likes": 10862, "created_at": "2024-07-31T21:13:44.000Z", "last_modified": "2025-06-27T16:22:19.000Z", "private": 0, "gated": "auto", "inference": null, "model_size": null, "parameters": null, "model_url": "https://huggingface.co/black-forest-labs/FLUX.1-dev", "scraped_date": "2025-07-13 17:32:30"}, {"id": 4, "model_id": "THUDM/glm-4-9b-chat", "model_name": "glm-4-9b-chat", "author": "THUDM", "description": "", "pipeline_tag": "", "task": null, "library_name": "transformers", "language": "", "license": "", "tags": "transformers,safetensors,chatglm,glm,thudm,custom_code,zh,en,arxiv:2406.12793,license:other,region:us", "downloads": 96399, "likes": 683, "created_at": "2024-06-04T09:01:42.000Z", "last_modified": "2025-03-13T15:48:14.000Z", "private": 0, "gated": 0, "inference": null, "model_size": null, "parameters": "9B parameters", "model_url": "https://huggingface.co/THUDM/glm-4-9b-chat", "scraped_date": "2025-07-13 17:32:31"}, {"id": 5, "model_id": "HuggingFaceTB/SmolLM2-1.7B-Instruct", "model_name": "SmolLM2-1.7B-Instruct", "author": "HuggingFaceTB", "description": "", "pipeline_tag": "text-generation", "task": null, "library_name": "transformers", "language": "", "license": "", "tags": "transformers,tensorboard,onnx,safetensors,llama,text-generation,transformers.js,conversational,en,arxiv:2502.02737,base_model:HuggingFaceTB/SmolLM2-1.7B,base_model:quantized:HuggingFaceTB/SmolLM2-1.7B,license:apache-2.0,autotrain_compatible,text-generation-inference,endpoints_compatible,region:us", "downloads": 61092, "likes": 656, "created_at": "2024-10-31T13:42:06.000Z", "last_modified": "2025-04-21T20:51:14.000Z", "private": 0, "gated": 0, "inference": null, "model_size": null, "parameters": "1.7B parameters", "model_url": "https://huggingface.co/HuggingFaceTB/SmolLM2-1.7B-Instruct", "scraped_date": "2025-07-13 17:32:32"}, {"id": 6, "model_id": "01-ai/Yi-Coder-9B-<PERSON><PERSON>", "model_name": "Yi-Coder-9B-<PERSON><PERSON>", "author": "01-ai", "description": "", "pipeline_tag": "text-generation", "task": null, "library_name": "transformers", "language": "", "license": "", "tags": "transformers,safetensors,llama,text-generation,conversational,arxiv:2403.04652,base_model:01-ai/Yi-Coder-9B,base_model:finetune:01-ai/Yi-Coder-9B,license:apache-2.0,autotrain_compatible,text-generation-inference,endpoints_compatible,region:us", "downloads": 10377, "likes": 203, "created_at": "2024-08-21T02:11:52.000Z", "last_modified": "2024-09-12T06:51:12.000Z", "private": 0, "gated": 0, "inference": null, "model_size": null, "parameters": "9B parameters", "model_url": "https://huggingface.co/01-ai/Yi-Coder-9B-Chat", "scraped_date": "2025-07-13 17:32:33"}, {"id": 7, "model_id": "jinaai/jina-embeddings-v3", "model_name": "jina-embeddings-v3", "author": "ji<PERSON><PERSON>", "description": "", "pipeline_tag": "feature-extraction", "task": null, "library_name": "transformers", "language": "", "license": "", "tags": "transformers,pytorch,onnx,safetensors,feature-extraction,sentence-similarity,mteb,sentence-transformers,custom_code,multilingual,af,am,ar,as,az,be,bg,bn,br,bs,ca,cs,cy,da,de,el,en,eo,es,et,eu,fa,fi,fr,fy,ga,gd,gl,gu,ha,he,hi,hr,hu,hy,id,is,it,ja,jv,ka,kk,km,kn,ko,ku,ky,la,lo,lt,lv,mg,mk,ml,mn,mr,ms,my,ne,nl,no,om,or,pa,pl,ps,pt,ro,ru,sa,sd,si,sk,sl,so,sq,sr,su,sv,sw,ta,te,th,tl,tr,ug,uk,ur,uz,vi,xh,yi,zh,arxiv:2409.10173,license:cc-by-nc-4.0,model-index,region:eu", "downloads": 3485147, "likes": 1026, "created_at": "2024-09-05T11:56:46.000Z", "last_modified": "2025-02-24T07:06:37.000Z", "private": 0, "gated": 0, "inference": null, "model_size": null, "parameters": null, "model_url": "https://huggingface.co/jinaai/jina-embeddings-v3", "scraped_date": "2025-07-13 17:32:35"}]}