<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Tools by Category</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f7fa;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .back-btn {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            margin-bottom: 20px;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
        }
        
        .category-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #667eea;
        }
        
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .category-name {
            font-size: 1.4rem;
            font-weight: bold;
            color: #333;
        }
        
        .category-count {
            background: #667eea;
            color: white;
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 1rem;
            font-weight: 500;
        }
        
        .category-stats {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9rem;
            color: #666;
        }
        
        .stat-number {
            font-weight: bold;
            color: #333;
        }
        
        .category-sources {
            font-size: 0.8rem;
            color: #999;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #f0f0f0;
        }
        
        .category-actions {
            margin-top: 15px;
        }
        
        .btn-view {
            background: #667eea;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: background 0.3s;
        }
        
        .btn-view:hover {
            background: #5a6fd8;
        }
        
        .badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-right: 5px;
        }
        
        .badge-free {
            background: #4CAF50;
            color: white;
        }
        
        .badge-open-source {
            background: #FF9800;
            color: white;
        }
        
        @media (max-width: 768px) {
            .categories-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .category-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .category-stats {
                flex-direction: column;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📂 AI Tools by Category</h1>
        <p>Browse tools organized by their functionality and use case</p>
    </div>
    
    <div class="container">
        <a href="/" class="back-btn">← Back to Dashboard</a>
        
        <div class="categories-grid">
            {% for category in categories %}
            <div class="category-card">
                <div class="category-header">
                    <div class="category-name">{{ category.category }}</div>
                    <div class="category-count">{{ category.total_tools }}</div>
                </div>
                
                <div class="category-stats">
                    {% if category.free_tools > 0 %}
                    <div class="stat-item">
                        <span>💚</span>
                        <span class="stat-number">{{ category.free_tools }}</span>
                        <span>free tools</span>
                    </div>
                    {% endif %}
                    
                    {% if category.open_source_tools > 0 %}
                    <div class="stat-item">
                        <span>⭐</span>
                        <span class="stat-number">{{ category.open_source_tools }}</span>
                        <span>open source</span>
                    </div>
                    {% endif %}
                </div>
                
                <div class="category-actions">
                    <a href="/tools?category={{ category.category }}" class="btn-view">
                        View {{ category.total_tools }} tools →
                    </a>
                </div>
                
                {% if category.sources %}
                <div class="category-sources">
                    <strong>Sources:</strong> {{ category.sources }}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
</body>
</html>
