"""
Massive AI Tools Scraper - Fixed Version
This version focuses on what works and scales it up to reach 5000+ tools
"""

import requests
from bs4 import BeautifulSoup
import sqlite3
import time
import logging
from fake_useragent import UserAgent
import pandas as pd
from datetime import datetime
import re
import json
from urllib.parse import urljoin, urlparse
from concurrent.futures import ThreadPoolExecutor
import threading

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MassiveAIToolsScraperFixed:
    def __init__(self):
        self.ua = UserAgent()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache'
        })
        
        # Initialize database with correct schema
        self.conn = sqlite3.connect('ai_tools_database_fixed.db', check_same_thread=False)
        self.cursor = self.conn.cursor()
        self.init_database()
        
        self.all_tools = []
        self.seen_names = set()
        self.lock = threading.Lock()
    
    def init_database(self):
        """Initialize database with correct schema"""
        try:
            self.cursor.execute('DROP TABLE IF EXISTS ai_tools_temp')
            self.cursor.execute('''
                CREATE TABLE ai_tools_temp (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    website_url TEXT,
                    pricing_model TEXT,
                    category TEXT,
                    source_website TEXT,
                    source_page_url TEXT,
                    free_tier BOOLEAN,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(name, website_url)
                )
            ''')
            
            # Copy existing data
            try:
                self.cursor.execute('''
                    INSERT OR IGNORE INTO ai_tools_temp 
                    (name, description, website_url, pricing_model, category, source_website, free_tier)
                    SELECT name, description, website_url, pricing_model, category, source_website, free_tier
                    FROM ai_tools
                ''')
                logger.info("Copied existing tools to new schema")
            except:
                logger.info("Starting with fresh database")
            
            # Drop old table and rename new one
            self.cursor.execute('DROP TABLE IF EXISTS ai_tools')
            self.cursor.execute('ALTER TABLE ai_tools_temp RENAME TO ai_tools')
            self.conn.commit()
            
            # Check current count
            self.cursor.execute('SELECT COUNT(*) FROM ai_tools')
            count = self.cursor.fetchone()[0]
            logger.info(f"Database initialized with {count} existing tools")
            
        except Exception as e:
            logger.error(f"Database initialization error: {e}")
    
    def clean_text(self, text):
        """Clean and normalize text"""
        if not text:
            return ""
        text = re.sub(r'\\s+', ' ', text.strip())
        text = re.sub(r'[^\\w\\s\\-\\.\\,\\!\\?\\:\\;\\(\\)\\[\\]\\/\\&\\%\\$\\@\\#\\+\\=]', '', text)
        return text[:500]
    
    def add_tool_to_collection(self, tool):
        """Add tool to collection with duplicate checking"""
        name_key = tool['name'].lower().strip()
        if name_key and len(name_key) > 2:
            # Quality checks
            if not any(skip in name_key for skip in ['read more', 'click here', 'learn more', 'view all', 'see more', 'load more', 'show more']):
                with self.lock:
                    if name_key not in self.seen_names:
                        self.all_tools.append(tool)
                        self.seen_names.add(name_key)
                        return True
        return False
    
    def scrape_paginated_site(self, base_url, site_name, selectors, max_pages=50):
        """Scrape a site with pagination"""
        logger.info(f"🚀 SCRAPING: {site_name} with pagination (max {max_pages} pages)")
        tools = []
        
        for page in range(1, max_pages + 1):
            try:
                # Try different pagination patterns
                page_urls = [
                    f"{base_url}?page={page}",
                    f"{base_url}?p={page}",
                    f"{base_url}/page/{page}",
                    f"{base_url}/page/{page}/",
                    f"{base_url}?paged={page}",
                    f"{base_url}?sf_paged={page}"
                ]
                
                tools_found_this_page = 0
                
                for page_url in page_urls:
                    try:
                        response = self.session.get(page_url, timeout=15)
                        if response.status_code == 200:
                            soup = BeautifulSoup(response.content, 'html.parser')
                            
                            for selector in selectors:
                                try:
                                    elements = soup.select(selector)
                                    if elements:
                                        for elem in elements:
                                            tool = self.extract_tool_data(elem, site_name, page_url)
                                            if tool and self.add_tool_to_collection(tool):
                                                tools.append(tool)
                                                tools_found_this_page += 1
                                        
                                        if tools_found_this_page > 0:
                                            logger.info(f"📄 {site_name} Page {page}: {tools_found_this_page} tools found")
                                            break
                                except Exception as e:
                                    continue
                            
                            if tools_found_this_page > 0:
                                break  # Found tools with this URL pattern
                            
                    except Exception as e:
                        continue
                
                if tools_found_this_page == 0:
                    logger.info(f"📄 {site_name} Page {page}: No tools found, stopping pagination")
                    break
                
                time.sleep(1)  # Rate limiting
                
            except Exception as e:
                logger.error(f"Error on page {page} of {site_name}: {e}")
                break
        
        logger.info(f"✅ {site_name}: {len(tools)} total tools collected")
        return tools
    
    def extract_tool_data(self, element, source_name, source_url):
        """Extract tool data from HTML element"""
        try:
            # Extract name
            name = ""
            name_selectors = ['h1', 'h2', 'h3', 'h4', 'h5', '.title', '.name', '.tool-name', 'a[title]', 'strong']
            
            for sel in name_selectors:
                name_elem = element.select_one(sel)
                if name_elem:
                    name = self.clean_text(name_elem.get_text())
                    if name and len(name) > 2:
                        break
            
            # If no name found, try link text or title attribute
            if not name:
                link = element.find('a')
                if link:
                    if link.get('title'):
                        name = self.clean_text(link.get('title'))
                    elif link.get_text().strip():
                        name = self.clean_text(link.get_text())
            
            if not name or len(name) < 3:
                return None
            
            # Extract description
            description = ""
            desc_selectors = ['p', '.description', '.excerpt', '.summary', '.content', '.desc']
            
            for sel in desc_selectors:
                desc_elem = element.select_one(sel)
                if desc_elem:
                    desc_text = desc_elem.get_text()
                    if desc_text and len(desc_text) > 10:
                        description = self.clean_text(desc_text)
                        break
            
            # Extract URL
            website_url = ""
            link = element.find('a', href=True)
            if link:
                href = link['href']
                if href.startswith('http'):
                    website_url = href
                elif href.startswith('/'):
                    parsed_source = urlparse(source_url)
                    website_url = f"{parsed_source.scheme}://{parsed_source.netloc}{href}"
            
            # Extract category and pricing
            element_text = element.get_text().lower()
            
            # Category detection
            category = "AI Tools"
            category_keywords = {
                'writing': 'Writing',
                'design': 'Design', 
                'code': 'Development',
                'video': 'Video',
                'image': 'Image',
                'audio': 'Audio',
                'marketing': 'Marketing',
                'business': 'Business',
                'productivity': 'Productivity',
                'education': 'Education'
            }
            
            for keyword, cat_name in category_keywords.items():
                if keyword in element_text:
                    category = cat_name
                    break
            
            # Pricing detection
            pricing = "Unknown"
            if any(word in element_text for word in ['free', 'gratis', '$0', 'no cost']):
                pricing = "Free"
            elif any(word in element_text for word in ['premium', 'paid', '$', 'subscription', 'pro']):
                pricing = "Premium"
            elif 'freemium' in element_text:
                pricing = "Freemium"
            
            return {
                'name': name,
                'description': description,
                'website_url': website_url,
                'pricing_model': pricing,
                'category': category,
                'source_website': source_name.lower().replace(' ', ''),
                'source_page_url': source_url,
                'free_tier': pricing in ["Free", "Freemium"]
            }
            
        except Exception as e:
            return None
    
    def scrape_specific_sites(self):
        """Scrape specific high-volume sites with known working approaches"""
        logger.info("🎯 SCRAPING SPECIFIC HIGH-VOLUME SITES")
        
        sites_config = [
            {
                'name': 'Best AI Tools',
                'base_url': 'https://www.bestaitools.com',
                'selectors': ['.tool-card', '.ai-tool', '.post', 'article', '.grid-item'],
                'max_pages': 25
            },
            {
                'name': 'Insidr AI',
                'base_url': 'https://insidr.ai/ai-tools',
                'selectors': ['.elementor-widget-aitools-listing', '.aitools-item', '.tool-item'],
                'max_pages': 15
            },
            {
                'name': 'Future Tools',
                'base_url': 'https://www.futuretools.io/tools',
                'selectors': ['.tool', '.tool-home', '.w-dyn-item'],
                'max_pages': 30
            },
            {
                'name': 'AI Tools List',
                'base_url': 'https://aitoolslist.io',
                'selectors': ['.tool-card', '.ai-tool', '.directory-item'],
                'max_pages': 20
            },
            {
                'name': 'There\'s An AI For That',
                'base_url': 'https://theresanaiforthat.com/tools',
                'selectors': ['.tool-card', '.ai-tool-item', '.grid-item'],
                'max_pages': 40
            }
        ]
        
        all_tools = []
        
        for site in sites_config:
            try:
                tools = self.scrape_paginated_site(
                    site['base_url'], 
                    site['name'], 
                    site['selectors'], 
                    site['max_pages']
                )
                all_tools.extend(tools)
                
                # Also try the main page without pagination
                try:
                    response = self.session.get(site['base_url'], timeout=15)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        for selector in site['selectors']:
                            elements = soup.select(selector)
                            if elements:
                                page_tools = 0
                                for elem in elements:
                                    tool = self.extract_tool_data(elem, site['name'], site['base_url'])
                                    if tool and self.add_tool_to_collection(tool):
                                        all_tools.append(tool)
                                        page_tools += 1
                                
                                if page_tools > 0:
                                    logger.info(f"📄 {site['name']} main page: {page_tools} additional tools")
                                    break
                                    
                except Exception as e:
                    logger.error(f"Error scraping main page of {site['name']}: {e}")
                
                time.sleep(2)  # Rate limiting between sites
                
            except Exception as e:
                logger.error(f"Error scraping {site['name']}: {e}")
                continue
        
        return all_tools
    
    def scrape_directory_sites(self):
        """Scrape AI tool directory sites"""
        logger.info("🎯 SCRAPING AI TOOL DIRECTORIES")
        
        directories = [
            'https://www.aihub.org/tools',
            'https://www.toolsai.org/',
            'https://www.ai-finder.net/',
            'https://www.aitoolnet.com/',
            'https://www.aitoolguru.com/',
            'https://www.topai.tools/',
            'https://www.aitools.fyi/',
            'https://allthingsai.com/tools'
        ]
        
        all_tools = []
        
        for directory_url in directories:
            try:
                logger.info(f"🔍 Scraping directory: {directory_url}")
                
                response = self.session.get(directory_url, timeout=15)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Generic selectors for directory sites
                    selectors = [
                        '[class*="tool"]', '[class*="ai"]', '[class*="item"]',
                        '[class*="card"]', '[class*="product"]', '[class*="listing"]',
                        'article', '.grid > div', '.row > div'
                    ]
                    
                    tools_found = 0
                    for selector in selectors:
                        elements = soup.select(selector)
                        if len(elements) > 5:  # Likely contains tools
                            for elem in elements[:50]:  # Limit per selector
                                tool = self.extract_tool_data(elem, directory_url.split('/')[2], directory_url)
                                if tool and self.add_tool_to_collection(tool):
                                    all_tools.append(tool)
                                    tools_found += 1
                            
                            if tools_found > 0:
                                logger.info(f"📄 {directory_url}: {tools_found} tools found")
                                break
                
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error scraping directory {directory_url}: {e}")
                continue
        
        return all_tools
    
    def scrape_github_repositories(self):
        """Scrape GitHub repositories for AI tools"""
        logger.info("🎯 SCRAPING GITHUB REPOSITORIES")
        
        github_sources = [
            'https://api.github.com/repos/mahseema/awesome-ai-tools/contents/README.md',
            'https://api.github.com/repos/steven2358/awesome-generative-ai/contents/README.md',
        ]
        
        all_tools = []
        
        for api_url in github_sources:
            try:
                logger.info(f"🔍 Scraping GitHub: {api_url}")
                
                response = self.session.get(api_url, timeout=15)
                if response.status_code == 200:
                    data = response.json()
                    
                    # Decode base64 content
                    import base64
                    content = base64.b64decode(data['content']).decode('utf-8')
                    
                    # Extract markdown links
                    link_pattern = r'\\[([^\\]]+)\\]\\(([^\\)]+)\\)'
                    matches = re.findall(link_pattern, content)
                    
                    tools_found = 0
                    for name, url in matches:
                        if any(keyword in name.lower() for keyword in ['ai', 'tool', 'generator', 'assistant', 'model']):
                            if url.startswith('http'):
                                tool = {
                                    'name': self.clean_text(name),
                                    'description': f"AI tool from GitHub awesome list: {name}",
                                    'website_url': url,
                                    'pricing_model': 'Unknown',
                                    'category': 'AI Tools',
                                    'source_website': 'github-awesome',
                                    'source_page_url': api_url,
                                    'free_tier': False
                                }
                                
                                if self.add_tool_to_collection(tool):
                                    all_tools.append(tool)
                                    tools_found += 1
                    
                    logger.info(f"📄 GitHub repository: {tools_found} tools extracted")
                
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error scraping GitHub {api_url}: {e}")
                continue
        
        return all_tools
    
    def save_tools_to_database(self, tools):
        """Save tools to database with proper error handling"""
        saved_count = 0
        
        with self.lock:
            for tool in tools:
                try:
                    self.cursor.execute('''
                        INSERT OR IGNORE INTO ai_tools 
                        (name, description, website_url, pricing_model, category, 
                         source_website, source_page_url, free_tier)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        tool['name'],
                        tool['description'],
                        tool['website_url'],
                        tool['pricing_model'],
                        tool['category'],
                        tool['source_website'],
                        tool['source_page_url'],
                        tool['free_tier']
                    ))
                    
                    if self.cursor.rowcount > 0:
                        saved_count += 1
                        
                except Exception as e:
                    logger.error(f"Error saving tool {tool.get('name', 'Unknown')}: {e}")
                    continue
            
            self.conn.commit()
        
        logger.info(f"💾 Saved {saved_count} new tools to database")
        return saved_count
    
    def run_massive_scraping(self):
        """Run massive scraping operation"""
        logger.info("🚀🚀🚀 STARTING MASSIVE AI TOOLS SCRAPING - TARGET: 5000+ TOOLS 🚀🚀🚀")
        
        start_time = time.time()
        
        # Get initial count
        self.cursor.execute('SELECT COUNT(*) FROM ai_tools')
        initial_count = self.cursor.fetchone()[0]
        logger.info(f"Starting with {initial_count} tools in database")
        
        # Run all scraping strategies
        all_collected_tools = []
        
        # Strategy 1: Specific high-volume sites
        logger.info("\\n" + "="*60)
        logger.info("STRATEGY 1: SPECIFIC HIGH-VOLUME SITES")
        logger.info("="*60)
        specific_tools = self.scrape_specific_sites()
        all_collected_tools.extend(specific_tools)
        
        # Strategy 2: Directory sites
        logger.info("\\n" + "="*60)
        logger.info("STRATEGY 2: AI TOOL DIRECTORIES")
        logger.info("="*60)
        directory_tools = self.scrape_directory_sites()
        all_collected_tools.extend(directory_tools)
        
        # Strategy 3: GitHub repositories
        logger.info("\\n" + "="*60)
        logger.info("STRATEGY 3: GITHUB REPOSITORIES")
        logger.info("="*60)
        github_tools = self.scrape_github_repositories()
        all_collected_tools.extend(github_tools)
        
        # Remove duplicates
        unique_tools = []
        seen_names = set()
        for tool in all_collected_tools:
            name_key = tool['name'].lower().strip()
            if name_key not in seen_names:
                unique_tools.append(tool)
                seen_names.add(name_key)
        
        # Save all tools
        saved_count = self.save_tools_to_database(unique_tools)
        
        # Final summary
        self.cursor.execute('SELECT COUNT(*) FROM ai_tools')
        final_count = self.cursor.fetchone()[0]
        
        elapsed_time = time.time() - start_time
        
        logger.info(f"\\n{'='*80}")
        logger.info(f"🏁 MASSIVE SCRAPING COMPLETED 🏁")
        logger.info(f"{'='*80}")
        logger.info(f"⏱️  Time elapsed: {elapsed_time/60:.2f} minutes")
        logger.info(f"📊 Initial tools: {initial_count}")
        logger.info(f"📊 Final tools: {final_count}")
        logger.info(f"📊 New tools added: {final_count - initial_count}")
        logger.info(f"📊 Tools collected this session: {len(unique_tools)}")
        logger.info(f"📊 Tools saved: {saved_count}")
        logger.info(f"🎯 Target achieved: {'✅ YES' if final_count >= 5000 else '❌ NO - Need ' + str(5000 - final_count) + ' more'}")
        
        # Export results
        self.export_results()
        
        return final_count
    
    def export_results(self):
        """Export results to CSV"""
        try:
            df = pd.read_sql_query('SELECT * FROM ai_tools ORDER BY created_at DESC', self.conn)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"massive_scrape_fixed_results_{timestamp}.csv"
            df.to_csv(filename, index=False)
            
            logger.info(f"📁 Results exported to: {filename}")
            logger.info(f"📊 Total rows exported: {len(df)}")
            
            # Show summary by source
            if 'source_website' in df.columns:
                source_summary = df['source_website'].value_counts()
                logger.info(f"\\n📈 Tools by source:")
                for source, count in source_summary.items():
                    logger.info(f"  {source}: {count} tools")
                    
        except Exception as e:
            logger.error(f"Error exporting results: {e}")

def main():
    scraper = MassiveAIToolsScraperFixed()
    final_count = scraper.run_massive_scraping()
    
    if final_count >= 5000:
        print(f"\\n🎉🎉🎉 SUCCESS! Collected {final_count} tools - TARGET ACHIEVED! 🎉🎉🎉")
    elif final_count >= 1000:
        print(f"\\n🎉 Great progress! Collected {final_count} tools. Need {5000 - final_count} more to reach 5000.")
    else:
        print(f"\\n⚠️ Collected {final_count} tools. Need {5000 - final_count} more to reach target.")
    
    scraper.conn.close()

if __name__ == "__main__":
    main()
