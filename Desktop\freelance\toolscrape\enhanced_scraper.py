import sqlite3
import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
import json
from fake_useragent import UserAgent
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
from tqdm import tqdm
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor
import urllib.parse
from urllib.parse import urljoin, urlparse
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedAIToolsScraper:
    def __init__(self):
        self.ua = UserAgent()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # Initialize database
        self.init_database()
        
        # Chrome options for Selenium
        self.chrome_options = Options()
        self.chrome_options.add_argument('--headless')
        self.chrome_options.add_argument('--no-sandbox')
        self.chrome_options.add_argument('--disable-dev-shm-usage')
        self.chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        self.chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        self.chrome_options.add_experimental_option('useAutomationExtension', False)
        self.chrome_options.add_argument(f'--user-agent={self.ua.random}')
        
    def init_database(self):
        """Initialize SQLite database with comprehensive schema"""
        self.conn = sqlite3.connect('ai_tools_database.db')
        self.cursor = self.conn.cursor()
        
        # Create main tools table with additional fields
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_tools (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                category TEXT,
                subcategory TEXT,
                website_url TEXT,
                pricing_model TEXT,
                pricing_details TEXT,
                features TEXT,
                rating REAL,
                reviews_count INTEGER,
                tags TEXT,
                use_cases TEXT,
                api_available BOOLEAN,
                open_source BOOLEAN,
                free_tier BOOLEAN,
                trial_available BOOLEAN,
                image_url TEXT,
                video_url TEXT,
                source_website TEXT,
                source_page_url TEXT,
                scraped_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(name, source_website)
            )
        ''')
        
        self.conn.commit()
        
    def get_selenium_driver(self):
        """Get Selenium WebDriver instance with better stealth"""
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=self.chrome_options)
            # Execute script to remove webdriver property
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            logger.error(f"Error creating WebDriver: {e}")
            return None
    
    def clean_text(self, text):
        """Clean and normalize text"""
        if not text:
            return ""
        # Remove extra whitespace and newlines
        text = re.sub(r'\s+', ' ', text.strip())
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s\-\.\,\!\?\:\;\(\)\[\]\/]', '', text)
        return text[:1000]  # Limit length
    
    def extract_pricing_info(self, text):
        """Extract pricing information from text"""
        if not text:
            return ""
        
        pricing_patterns = [
            r'(\$[\d,]+(?:\.\d{2})?(?:/month|/mo|/year|/yr|/week)?)',
            r'(free|premium|pro|enterprise|starter|basic|plus)',
            r'(\d+(?:\.\d{2})?\s*(?:usd|eur|gbp|dollars?))',
            r'(freemium|subscription|one-time|lifetime)',
        ]
        
        pricing_info = []
        text_lower = text.lower()
        
        for pattern in pricing_patterns:
            matches = re.findall(pattern, text_lower)
            pricing_info.extend(matches)
        
        return ', '.join(set(pricing_info)) if pricing_info else ""
    
    def scrape_bestaito_com_comprehensive(self):
        """Enhanced scraping of bestaito.com with pagination"""
        logger.info("Enhanced scraping of bestaito.com...")
        tools = []
        
        base_urls = [
            "https://bestaito.com/tool/?pricing=free",
            "https://bestaito.com/tool/?pricing=premium", 
            "https://bestaito.com/tool/?pricing=freemium",
            "https://bestaito.com/tools/",
            "https://bestaito.com/category/ai-writing-tools/",
            "https://bestaito.com/category/ai-image-generators/",
            "https://bestaito.com/category/ai-video-tools/",
            "https://bestaito.com/category/ai-audio-tools/",
            "https://bestaito.com/category/ai-code-tools/",
        ]
        
        for base_url in base_urls:
            try:
                # Try multiple pages
                for page in range(1, 6):  # Check first 5 pages
                    if page > 1:
                        if '?' in base_url:
                            url = f"{base_url}&page={page}"
                        else:
                            url = f"{base_url}?page={page}"
                    else:
                        url = base_url
                    
                    logger.info(f"Scraping {url}")
                    response = self.session.get(url, timeout=30)
                    
                    if response.status_code != 200:
                        continue
                        
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Multiple selectors for tool cards
                    selectors = [
                        'div[class*="tool"]',
                        'div[class*="card"]',
                        'div[class*="item"]',
                        'article[class*="post"]',
                        'div[class*="listing"]',
                        '.tool-card',
                        '.ai-tool',
                        '.product-item'
                    ]
                    
                    found_tools = False
                    for selector in selectors:
                        tool_cards = soup.select(selector)
                        if tool_cards:
                            found_tools = True
                            break
                    
                    if not found_tools:
                        # Fallback: find any div with links
                        tool_cards = soup.find_all('div', recursive=True)
                        tool_cards = [card for card in tool_cards if card.find('a', href=True)][:50]
                    
                    page_tools = 0
                    for card in tool_cards[:100]:  # Increased limit
                        try:
                            # Extract name
                            name = ""
                            title_selectors = ['h1', 'h2', 'h3', 'h4', 'h5', '.title', '.name', '.tool-name']
                            for sel in title_selectors:
                                title_elem = card.find(sel) if isinstance(sel, str) and sel.startswith('.') else card.find(sel)
                                if title_elem:
                                    name = self.clean_text(title_elem.get_text())
                                    break
                            
                            # Extract description
                            description = ""
                            desc_selectors = ['p', '.description', '.excerpt', '.summary', '.content']
                            for sel in desc_selectors:
                                desc_elem = card.find(sel) if isinstance(sel, str) and sel.startswith('.') else card.find(sel)
                                if desc_elem:
                                    description = self.clean_text(desc_elem.get_text())
                                    break
                            
                            # Extract URL
                            website_url = ""
                            link_elem = card.find('a', href=True)
                            if link_elem:
                                href = link_elem['href']
                                if href.startswith('http'):
                                    website_url = href
                                elif href.startswith('/'):
                                    website_url = urljoin("https://bestaito.com", href)
                            
                            # Extract category
                            category = "AI Tools"
                            cat_selectors = ['.category', '.tag', '.badge', '[class*="cat"]']
                            for sel in cat_selectors:
                                cat_elem = card.find(sel)
                                if cat_elem:
                                    category = self.clean_text(cat_elem.get_text())
                                    break
                            
                            # Extract pricing
                            card_text = card.get_text()
                            pricing = self.extract_pricing_info(card_text)
                            
                            if name and len(name) > 2:
                                tools.append({
                                    'name': name,
                                    'description': description,
                                    'website_url': website_url,
                                    'pricing_model': pricing or "Unknown",
                                    'category': category,
                                    'source_website': 'bestaito.com',
                                    'source_page_url': url,
                                    'free_tier': 'free' in pricing.lower() if pricing else False
                                })
                                page_tools += 1
                        except Exception as e:
                            continue
                    
                    logger.info(f"Found {page_tools} tools on page {page}")
                    if page_tools == 0:
                        break  # No more tools found, stop pagination
                    
                    time.sleep(2)  # Rate limiting
                    
            except Exception as e:
                logger.error(f"Error scraping {base_url}: {e}")
                continue
        
        logger.info(f"Total tools from bestaito.com: {len(tools)}")
        return tools
    
    def scrape_bestaitools_com_enhanced(self):
        """Enhanced scraping of bestaitools.com with multiple strategies"""
        logger.info("Enhanced scraping of bestaitools.com...")
        tools = []
        
        driver = self.get_selenium_driver()
        if not driver:
            return tools
        
        try:
            base_urls = [
                "https://www.bestaitools.com/",
                "https://www.bestaitools.com/tools/",
                "https://www.bestaitools.com/?sf_paged=1",
                "https://www.bestaitools.com/?sf_paged=2",
                "https://www.bestaitools.com/?sf_paged=3",
                "https://www.bestaitools.com/?sf_paged=4",
                "https://www.bestaitools.com/?sf_paged=5"
            ]
            
            for url in base_urls:
                try:
                    logger.info(f"Loading {url}")
                    driver.get(url)
                    time.sleep(5)  # Wait for page load
                    
                    # Scroll to load more content
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(3)
                    
                    # Multiple selectors to find tools
                    selectors = [
                        '[class*="tool"]',
                        '[class*="card"]', 
                        '[class*="item"]',
                        '[class*="post"]',
                        '.entry',
                        '.product',
                        'article'
                    ]
                    
                    found_elements = []
                    for selector in selectors:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            found_elements = elements
                            logger.info(f"Found {len(elements)} elements with selector: {selector}")
                            break
                    
                    if not found_elements:
                        # Fallback: find any element with text and links
                        found_elements = driver.find_elements(By.XPATH, "//div[contains(@class, 'post') or contains(@class, 'entry') or .//a[@href]]")
                    
                    page_tools = 0
                    for element in found_elements[:100]:
                        try:
                            # Get text content
                            element_text = element.text.strip()
                            if len(element_text) < 10:
                                continue
                            
                            # Extract name from various elements
                            name = ""
                            name_selectors = ['h1', 'h2', 'h3', 'h4', 'h5', '.title', '.name']
                            for sel in name_selectors:
                                try:
                                    name_elem = element.find_element(By.CSS_SELECTOR, sel)
                                    if name_elem:
                                        name = name_elem.text.strip()
                                        if name:
                                            break
                                except:
                                    continue
                            
                            # If no name found, try to extract from link text
                            if not name:
                                try:
                                    link_elem = element.find_element(By.CSS_SELECTOR, 'a')
                                    if link_elem:
                                        name = link_elem.text.strip()
                                except:
                                    pass
                            
                            # Extract description
                            description = ""
                            desc_selectors = ['p', '.description', '.excerpt', '.summary']
                            for sel in desc_selectors:
                                try:
                                    desc_elem = element.find_element(By.CSS_SELECTOR, sel)
                                    if desc_elem:
                                        description = desc_elem.text.strip()
                                        break
                                except:
                                    continue
                            
                            # Extract URL
                            website_url = ""
                            try:
                                link_elem = element.find_element(By.CSS_SELECTOR, 'a')
                                if link_elem:
                                    website_url = link_elem.get_attribute('href')
                            except:
                                pass
                            
                            # Extract category
                            category = "AI Tools"
                            try:
                                cat_elem = element.find_element(By.CSS_SELECTOR, '[class*="cat"], [class*="tag"], .badge')
                                if cat_elem:
                                    category = cat_elem.text.strip()
                            except:
                                pass
                            
                            if name and len(name) > 2:
                                tools.append({
                                    'name': self.clean_text(name),
                                    'description': self.clean_text(description),
                                    'website_url': website_url,
                                    'category': category,
                                    'source_website': 'bestaitools.com',
                                    'source_page_url': url
                                })
                                page_tools += 1
                                
                        except Exception as e:
                            continue
                    
                    logger.info(f"Found {page_tools} tools on {url}")
                    time.sleep(3)
                    
                except Exception as e:
                    logger.error(f"Error scraping {url}: {e}")
                    continue
                    
        finally:
            driver.quit()
        
        logger.info(f"Total tools from bestaitools.com: {len(tools)}")
        return tools
    
    def scrape_futuretools_io_comprehensive(self):
        """Enhanced scraping of futuretools.io"""
        logger.info("Enhanced scraping of futuretools.io...")
        tools = []
        
        driver = self.get_selenium_driver()
        if not driver:
            return tools
        
        try:
            # Multiple entry points
            urls = [
                "https://www.futuretools.io/",
                "https://www.futuretools.io/tools",
                "https://www.futuretools.io/tools?page=1",
                "https://www.futuretools.io/tools?page=2", 
                "https://www.futuretools.io/tools?page=3",
                "https://www.futuretools.io/tools?page=4",
                "https://www.futuretools.io/tools?page=5"
            ]
            
            for url in urls:
                try:
                    logger.info(f"Scraping {url}")
                    driver.get(url)
                    time.sleep(5)
                    
                    # Scroll to trigger lazy loading
                    for i in range(3):
                        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                        time.sleep(2)
                    
                    # Find tool cards
                    selectors = [
                        '[data-testid*="tool"]',
                        '.tool-card',
                        '[class*="card"]',
                        '[class*="tool"]',
                        '[class*="item"]'
                    ]
                    
                    elements = []
                    for selector in selectors:
                        found = driver.find_elements(By.CSS_SELECTOR, selector)
                        if found:
                            elements = found
                            break
                    
                    page_tools = 0
                    for element in elements[:100]:
                        try:
                            # Extract tool information
                            name = ""
                            try:
                                name_elem = element.find_element(By.CSS_SELECTOR, 'h1, h2, h3, h4, .title, [class*="name"]')
                                name = name_elem.text.strip()
                            except:
                                pass
                            
                            description = ""
                            try:
                                desc_elem = element.find_element(By.CSS_SELECTOR, 'p, .description, [class*="desc"]')
                                description = desc_elem.text.strip()
                            except:
                                pass
                            
                            website_url = ""
                            try:
                                link_elem = element.find_element(By.CSS_SELECTOR, 'a')
                                website_url = link_elem.get_attribute('href')
                            except:
                                pass
                            
                            category = "AI Tools"
                            try:
                                cat_elem = element.find_element(By.CSS_SELECTOR, '[class*="category"], [class*="tag"], .badge')
                                category = cat_elem.text.strip()
                            except:
                                pass
                            
                            if name and len(name) > 2:
                                tools.append({
                                    'name': self.clean_text(name),
                                    'description': self.clean_text(description),
                                    'website_url': website_url,
                                    'category': category,
                                    'source_website': 'futuretools.io',
                                    'source_page_url': url
                                })
                                page_tools += 1
                                
                        except Exception as e:
                            continue
                    
                    logger.info(f"Found {page_tools} tools on {url}")
                    time.sleep(3)
                    
                except Exception as e:
                    logger.error(f"Error scraping {url}: {e}")
                    continue
                    
        finally:
            driver.quit()
        
        logger.info(f"Total tools from futuretools.io: {len(tools)}")
        return tools
    
    def scrape_site_with_requests(self, base_url, site_name, max_pages=10):
        """Generic scraper for sites using requests"""
        logger.info(f"Scraping {site_name}...")
        tools = []
        
        for page in range(1, max_pages + 1):
            try:
                # Try different URL patterns
                url_patterns = [
                    f"{base_url}?page={page}",
                    f"{base_url}/page/{page}",
                    f"{base_url}?p={page}",
                    f"{base_url}?offset={page*20}",
                    base_url if page == 1 else None
                ]
                
                url = None
                for pattern in url_patterns:
                    if pattern:
                        url = pattern
                        break
                
                if not url:
                    continue
                
                logger.info(f"Trying {url}")
                response = self.session.get(url, timeout=30)
                
                if response.status_code != 200:
                    continue
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Generic selectors for tools
                selectors = [
                    'div[class*="tool"]',
                    'div[class*="card"]',
                    'div[class*="item"]',
                    'article',
                    'div[class*="post"]',
                    'div[class*="listing"]',
                    'div[class*="product"]'
                ]
                
                found_elements = []
                for selector in selectors:
                    elements = soup.select(selector)
                    if elements:
                        found_elements = elements
                        break
                
                if not found_elements and page == 1:
                    # Fallback: any div with links
                    found_elements = soup.find_all('div', recursive=True)
                    found_elements = [el for el in found_elements if el.find('a', href=True)][:50]
                
                page_tools = 0
                for element in found_elements[:100]:
                    try:
                        # Extract tool data
                        name = ""
                        title_tags = element.find(['h1', 'h2', 'h3', 'h4', 'h5'])
                        if title_tags:
                            name = self.clean_text(title_tags.get_text())
                        
                        description = ""
                        desc_elem = element.find(['p', 'div'], class_=re.compile(r'desc|excerpt|summary'))
                        if desc_elem:
                            description = self.clean_text(desc_elem.get_text())
                        
                        website_url = ""
                        link_elem = element.find('a', href=True)
                        if link_elem:
                            href = link_elem['href']
                            if href.startswith('http'):
                                website_url = href
                            elif href.startswith('/'):
                                website_url = urljoin(base_url, href)
                        
                        category = "AI Tools"
                        cat_elem = element.find(['span', 'div'], class_=re.compile(r'cat|tag|badge'))
                        if cat_elem:
                            category = self.clean_text(cat_elem.get_text())
                        
                        if name and len(name) > 2:
                            tools.append({
                                'name': name,
                                'description': description,
                                'website_url': website_url,
                                'category': category,
                                'source_website': site_name,
                                'source_page_url': url
                            })
                            page_tools += 1
                            
                    except Exception as e:
                        continue
                
                logger.info(f"Found {page_tools} tools on page {page}")
                if page_tools == 0:
                    break
                
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"Error scraping page {page}: {e}")
                continue
        
        logger.info(f"Total tools from {site_name}: {len(tools)}")
        return tools
    
    def save_tools_to_database(self, tools):
        """Save tools to database with enhanced deduplication"""
        logger.info(f"Saving {len(tools)} tools to database...")
        
        saved_count = 0
        updated_count = 0
        
        for tool in tools:
            try:
                # Check if tool already exists
                self.cursor.execute(
                    "SELECT id FROM ai_tools WHERE name = ? AND source_website = ?",
                    (tool['name'], tool['source_website'])
                )
                
                existing = self.cursor.fetchone()
                
                if existing:
                    # Update existing tool with new info
                    self.cursor.execute('''
                        UPDATE ai_tools SET 
                            description = COALESCE(NULLIF(?, ''), description),
                            category = COALESCE(NULLIF(?, ''), category),
                            website_url = COALESCE(NULLIF(?, ''), website_url),
                            pricing_model = COALESCE(NULLIF(?, ''), pricing_model),
                            free_tier = COALESCE(?, free_tier),
                            source_page_url = COALESCE(NULLIF(?, ''), source_page_url),
                            last_updated = CURRENT_TIMESTAMP
                        WHERE id = ?
                    ''', (
                        tool.get('description', ''),
                        tool.get('category', ''),
                        tool.get('website_url', ''),
                        tool.get('pricing_model', ''),
                        tool.get('free_tier', False),
                        tool.get('source_page_url', ''),
                        existing[0]
                    ))
                    updated_count += 1
                else:
                    # Insert new tool
                    self.cursor.execute('''
                        INSERT OR IGNORE INTO ai_tools (
                            name, description, category, website_url, pricing_model,
                            free_tier, source_website, source_page_url
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        tool['name'],
                        tool.get('description', ''),
                        tool.get('category', ''),
                        tool.get('website_url', ''),
                        tool.get('pricing_model', ''),
                        tool.get('free_tier', False),
                        tool['source_website'],
                        tool.get('source_page_url', '')
                    ))
                    saved_count += 1
                    
            except Exception as e:
                logger.error(f"Error saving tool {tool.get('name', 'Unknown')}: {e}")
                continue
        
        self.conn.commit()
        logger.info(f"Database updated: {saved_count} new tools, {updated_count} updated")
        return saved_count + updated_count
    
    def scrape_all_sources_enhanced(self):
        """Enhanced scraping of all sources"""
        logger.info("Starting enhanced comprehensive scraping...")
        
        all_tools = []
        
        # 1. Enhanced bestaito.com scraping
        bestaito_tools = self.scrape_bestaito_com_comprehensive()
        all_tools.extend(bestaito_tools)
        self.save_tools_to_database(bestaito_tools)
        
        # 2. Enhanced bestaitools.com scraping
        bestaitools_tools = self.scrape_bestaitools_com_enhanced()
        all_tools.extend(bestaitools_tools)
        self.save_tools_to_database(bestaitools_tools)
        
        # 3. Enhanced futuretools.io scraping
        futuretools_tools = self.scrape_futuretools_io_comprehensive()
        all_tools.extend(futuretools_tools)
        self.save_tools_to_database(futuretools_tools)
        
        # 4. Scrape other sites with generic scraper
        sites_to_scrape = [
            ("https://10bestaitools.com", "10bestaitools.com"),
            ("https://bestfreeaitools.io", "bestfreeaitools.io"),
            ("https://best-ai-tools.org", "best-ai-tools.org"),
            ("https://aisitelist.com", "aisitelist.com"),
            ("https://opentools.ai", "opentools.ai"),
        ]
        
        for base_url, site_name in sites_to_scrape:
            try:
                site_tools = self.scrape_site_with_requests(base_url, site_name, max_pages=5)
                all_tools.extend(site_tools)
                self.save_tools_to_database(site_tools)
                time.sleep(3)  # Rate limiting between sites
            except Exception as e:
                logger.error(f"Error scraping {site_name}: {e}")
                continue
        
        logger.info(f"Enhanced scraping completed! Total tools collected: {len(all_tools)}")
        return all_tools
    
    def close(self):
        """Close database connection"""
        self.conn.close()

if __name__ == "__main__":
    scraper = EnhancedAIToolsScraper()
    
    try:
        # Run enhanced scraping
        all_tools = scraper.scrape_all_sources_enhanced()
        
        # Get final count
        scraper.cursor.execute("SELECT COUNT(*) FROM ai_tools")
        total_count = scraper.cursor.fetchone()[0]
        
        print(f"\n🎉 ENHANCED SCRAPING COMPLETED!")
        print(f"Total tools in database: {total_count}")
        print(f"Tools collected in this session: {len(all_tools)}")
        
    except Exception as e:
        logger.error(f"Error in enhanced scraping: {e}")
    finally:
        scraper.close()
