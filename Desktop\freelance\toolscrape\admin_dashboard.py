"""
Advanced Admin Dashboard for AI Tools Database Management
Includes scraping, CRUD operations, filtering, sorting, publishing, bulk operations,
analytics, verification system, and real-time notifications
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, session, send_file
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
import os
import requests
from bs4 import BeautifulSoup
import json
import re
from urllib.parse import urljoin, urlparse
import time
import io
import csv
from werkzeug.utils import secure_filename
import hashlib

app = Flask(__name__)
app.secret_key = 'admin_dashboard_secret_key_2025'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

def get_db_connection():
    conn = sqlite3.connect('ai_tools_master.db')
    conn.row_factory = sqlite3.Row
    return conn

def init_admin_tables():
    """Initialize admin-specific tables"""
    conn = get_db_connection()
    
    # Create admin users table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS admin_users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT DEFAULT 'admin',
            last_login TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create scraping jobs table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS scraping_jobs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            job_name TEXT NOT NULL,
            target_url TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            tools_found INTEGER DEFAULT 0,
            started_at TIMESTAMP,
            completed_at TIMESTAMP,
            error_message TEXT,
            created_by TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create tool modifications log
    conn.execute('''
        CREATE TABLE IF NOT EXISTS tool_modifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            tool_id INTEGER,
            action TEXT NOT NULL,
            old_data TEXT,
            new_data TEXT,
            modified_by TEXT,
            modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create publishing queue
    conn.execute('''
        CREATE TABLE IF NOT EXISTS publishing_queue (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            tool_id INTEGER,
            status TEXT DEFAULT 'pending',
            requested_by TEXT,
            approved_by TEXT,
            published_at TIMESTAMP,
            rejection_reason TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create analytics table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS analytics_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            metric_name TEXT NOT NULL,
            metric_value REAL NOT NULL,
            metric_date DATE NOT NULL,
            category TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create notifications table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS admin_notifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            message TEXT NOT NULL,
            type TEXT DEFAULT 'info',
            is_read BOOLEAN DEFAULT 0,
            target_user TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create API scraping configurations table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS api_scraping_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            config_name TEXT UNIQUE NOT NULL,
            api_type TEXT NOT NULL,
            api_url TEXT NOT NULL,
            api_key TEXT,
            headers TEXT,
            parameters TEXT,
            data_mapping TEXT,
            is_active BOOLEAN DEFAULT 1,
            rate_limit INTEGER DEFAULT 60,
            last_run TIMESTAMP,
            total_runs INTEGER DEFAULT 0,
            success_rate REAL DEFAULT 0.0,
            created_by TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create API scraping results table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS api_scraping_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            config_id INTEGER,
            job_id INTEGER,
            status TEXT DEFAULT 'pending',
            tools_found INTEGER DEFAULT 0,
            response_data TEXT,
            error_message TEXT,
            execution_time REAL,
            started_at TIMESTAMP,
            completed_at TIMESTAMP,
            FOREIGN KEY (config_id) REFERENCES api_scraping_configs (id),
            FOREIGN KEY (job_id) REFERENCES scraping_jobs (id)
        )
    ''')
    
    # Insert default admin user if not exists
    try:
        conn.execute('INSERT INTO admin_users (username, password, role) VALUES (?, ?, ?)',
                    ('admin', 'admin123', 'super_admin'))
    except:
        pass  # User already exists
    
    # Insert default API configurations
    try:
        default_configs = [
            {
                'config_name': 'GitHub AI Tools API',
                'api_type': 'github',
                'api_url': 'https://api.github.com/search/repositories',
                'parameters': '{"q": "ai tools machine learning", "sort": "stars", "order": "desc", "per_page": 100}',
                'data_mapping': '{"name": "full_name", "description": "description", "url": "html_url", "category": "AI Tools"}',
                'rate_limit': 30
            },
            {
                'config_name': 'Product Hunt API',
                'api_type': 'producthunt',
                'api_url': 'https://api.producthunt.com/v2/api/graphql',
                'headers': '{"Authorization": "Bearer YOUR_TOKEN", "Content-Type": "application/json"}',
                'parameters': '{"query": "query { posts(first: 50, topic: \\"artificial-intelligence\\") { nodes { name description url } } }"}',
                'data_mapping': '{"name": "name", "description": "description", "url": "url", "category": "AI Tools"}',
                'rate_limit': 60
            },
            {
                'config_name': 'Hugging Face Models API',
                'api_type': 'huggingface',
                'api_url': 'https://huggingface.co/api/models',
                'parameters': '{"limit": 100, "filter": "ai", "sort": "downloads"}',
                'data_mapping': '{"name": "id", "description": "description", "url": "html_url", "category": "AI Models"}',
                'rate_limit': 60
            }
        ]
        
        for config in default_configs:
            try:
                conn.execute('''
                    INSERT INTO api_scraping_configs 
                    (config_name, api_type, api_url, parameters, data_mapping, rate_limit, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (config['config_name'], config['api_type'], config['api_url'], 
                     config['parameters'], config['data_mapping'], config['rate_limit'], 'system'))
            except:
                pass  # Config already exists
    except Exception as e:
        print(f"Error inserting default API configs: {e}")
    
    conn.commit()
    conn.close()

# Initialize on app startup with error handling
try:
    with app.app_context():
        init_admin_tables()
        print("✅ Admin dashboard initialized successfully")
except Exception as e:
    print(f"⚠️ Warning: Admin table initialization failed: {e}")
    print("The dashboard will still work, but some features may be limited")

# Authentication decorator
def require_auth(f):
    def wrapper(*args, **kwargs):
        if 'admin_logged_in' not in session:
            return redirect(url_for('admin_login'))
        return f(*args, **kwargs)
    wrapper.__name__ = f.__name__
    return wrapper

def create_notification(title, message, type='info', target_user=None):
    """Create admin notification"""
    conn = get_db_connection()
    conn.execute('''
        INSERT INTO admin_notifications (title, message, type, target_user)
        VALUES (?, ?, ?, ?)
    ''', (title, message, type, target_user))
    conn.commit()
    conn.close()

def log_tool_modification(tool_id, action, old_data=None, new_data=None):
    """Log tool modification"""
    conn = get_db_connection()
    conn.execute('''
        INSERT INTO tool_modifications (tool_id, action, old_data, new_data, modified_by)
        VALUES (?, ?, ?, ?, ?)
    ''', (tool_id, action, old_data, new_data, session.get('admin_username', 'system')))
    conn.commit()
    conn.close()

def update_analytics(metric_name, metric_value, category=None):
    """Update analytics data"""
    conn = get_db_connection()
    today = datetime.now().date()
    
    # Check if metric exists for today
    existing = conn.execute('''
        SELECT id FROM analytics_data 
        WHERE metric_name = ? AND metric_date = ? AND category = ?
    ''', (metric_name, today, category)).fetchone()
    
    if existing:
        conn.execute('''
            UPDATE analytics_data 
            SET metric_value = metric_value + ?
            WHERE id = ?
        ''', (metric_value, existing['id']))
    else:
        conn.execute('''
            INSERT INTO analytics_data (metric_name, metric_value, metric_date, category)
            VALUES (?, ?, ?, ?)
        ''', (metric_name, metric_value, today, category))
    
    conn.commit()
    conn.close()

@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        conn = get_db_connection()
        user = conn.execute(
            'SELECT * FROM admin_users WHERE username = ? AND password = ?',
            (username, password)
        ).fetchone()
        
        if user:
            # Update last login
            conn.execute(
                'UPDATE admin_users SET last_login = ? WHERE id = ?',
                (datetime.now(), user['id'])
            )
            conn.commit()
            
            session['admin_logged_in'] = True
            session['admin_username'] = username
            session['admin_role'] = user['role']
            session['admin_id'] = user['id']
            
            create_notification(
                'Admin Login', 
                f'User {username} logged in', 
                'info', 
                username
            )
            
            flash('Successfully logged in!', 'success')
            return redirect(url_for('admin_dashboard'))
        else:
            flash('Invalid credentials!', 'error')
        
        conn.close()
    
    return render_template('admin/login.html')

@app.route('/admin/logout')
def admin_logout():
    session.clear()
    flash('Successfully logged out!', 'success')
    return redirect(url_for('admin_login'))

@app.route('/')
def index():
    """Root route - redirect to admin login"""
    return redirect(url_for('admin_login'))

@app.route('/admin/dashboard')
@require_auth
def admin_dashboard_redirect():
    """Alternative dashboard route"""
    return redirect(url_for('admin_dashboard'))

@app.route('/admin')
@app.route('/admin/')
@require_auth
def admin_dashboard():
    """Main admin dashboard with overview statistics and analytics"""
    conn = get_db_connection()
    
    # Get comprehensive statistics
    stats = {}
    stats['total_tools'] = conn.execute('SELECT COUNT(*) FROM ai_tools').fetchone()[0]
    stats['free_tools'] = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE free_tier = 1').fetchone()[0]
    stats['verified_tools'] = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE is_verified = 1').fetchone()[0]
    stats['tools_with_urls'] = conn.execute('SELECT COUNT(*) FROM ai_tools WHERE website_url IS NOT NULL AND website_url != ""').fetchone()[0]
    stats['categories_count'] = conn.execute('SELECT COUNT(DISTINCT category) FROM ai_tools WHERE category IS NOT NULL').fetchone()[0]
    stats['pending_publications'] = conn.execute('SELECT COUNT(*) FROM publishing_queue WHERE status = "pending"').fetchone()[0]
    stats['active_scraping_jobs'] = conn.execute('SELECT COUNT(*) FROM scraping_jobs WHERE status = "running"').fetchone()[0]
    
    # Recent activity analytics
    week_ago = datetime.now() - timedelta(days=7)
    stats['tools_added_week'] = conn.execute(
        'SELECT COUNT(*) FROM ai_tools WHERE created_at > ?', 
        (week_ago,)
    ).fetchone()[0]
    
    # Recent scraping jobs
    recent_jobs = conn.execute('''
        SELECT * FROM scraping_jobs 
        ORDER BY created_at DESC 
        LIMIT 5
    ''').fetchall()
    
    # Recent modifications
    recent_modifications = conn.execute('''
        SELECT tm.*, at.name as tool_name 
        FROM tool_modifications tm
        LEFT JOIN ai_tools at ON tm.tool_id = at.id
        ORDER BY tm.modified_at DESC 
        LIMIT 10
    ''').fetchall()
    
    # Category breakdown with growth
    categories = conn.execute('''
        SELECT 
            category, 
            COUNT(*) as count,
            AVG(CASE WHEN rating IS NOT NULL THEN rating ELSE 0 END) as avg_rating
        FROM ai_tools 
        WHERE category IS NOT NULL AND category != ''
        GROUP BY category 
        ORDER BY count DESC 
        LIMIT 10
    ''').fetchall()
    
    # Recent notifications
    notifications = conn.execute('''
        SELECT * FROM admin_notifications 
        WHERE target_user IS NULL OR target_user = ?
        ORDER BY created_at DESC 
        LIMIT 10
    ''', (session['admin_username'],)).fetchall()
    
    # Analytics data for charts
    analytics_data = conn.execute('''
        SELECT metric_name, metric_value, metric_date
        FROM analytics_data 
        WHERE metric_date >= date('now', '-30 days')
        ORDER BY metric_date DESC
    ''').fetchall()
    
    # Pending publications
    pending_publications = conn.execute('''
        SELECT pq.*, at.name as tool_name, at.category
        FROM publishing_queue pq
        JOIN ai_tools at ON pq.tool_id = at.id
        WHERE pq.status = 'pending'
        ORDER BY pq.created_at DESC
        LIMIT 5
    ''').fetchall()
    
    conn.close()
    
    return render_template('admin/dashboard.html',
                         stats=stats,
                         recent_jobs=recent_jobs,
                         recent_modifications=recent_modifications,
                         categories=categories,
                         notifications=notifications,
                         analytics_data=analytics_data,
                         pending_publications=pending_publications)

@app.route('/admin/tools')
@require_auth
def admin_tools():
    """Tools management page with advanced filtering, sorting, and bulk operations"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category = request.args.get('category', '')
    free_only = request.args.get('free_only', '')
    verified_only = request.args.get('verified_only', '')
    has_url = request.args.get('has_url', '')
    sort_by = request.args.get('sort_by', 'name')
    sort_order = request.args.get('sort_order', 'asc')
    per_page = 20
    
    conn = get_db_connection()
    
    # Build query
    query = 'SELECT * FROM ai_tools WHERE 1=1'
    params = []
    
    if search:
        query += ' AND (name LIKE ? OR description LIKE ? OR category LIKE ?)'
        params.extend([f'%{search}%', f'%{search}%', f'%{search}%'])
    
    if category:
        query += ' AND category = ?'
        params.append(category)
    
    if free_only:
        query += ' AND free_tier = 1'
    
    if verified_only:
        query += ' AND is_verified = 1'
    
    if has_url:
        query += ' AND website_url IS NOT NULL AND website_url != ""'
    
    # Add sorting
    valid_sorts = ['name', 'category', 'created_at', 'free_tier', 'is_verified', 'rating', 'popularity_score']
    if sort_by in valid_sorts:
        order = 'ASC' if sort_order == 'asc' else 'DESC'
        query += f' ORDER BY {sort_by} {order}'
    
    # Get total count
    count_query = query.replace('SELECT *', 'SELECT COUNT(*)')
    total = conn.execute(count_query, params).fetchone()[0]
    
    # Add pagination
    query += ' LIMIT ? OFFSET ?'
    params.extend([per_page, (page - 1) * per_page])
    
    tools = conn.execute(query, params).fetchall()
    
    # Get all categories for filter
    all_categories = conn.execute('''
        SELECT DISTINCT category 
        FROM ai_tools 
        WHERE category IS NOT NULL AND category != ''
        ORDER BY category
    ''').fetchall()
    
    # Get filter statistics
    filter_stats = {
        'total': total,
        'free': conn.execute('SELECT COUNT(*) FROM ai_tools WHERE free_tier = 1').fetchone()[0],
        'verified': conn.execute('SELECT COUNT(*) FROM ai_tools WHERE is_verified = 1').fetchone()[0],
        'with_url': conn.execute('SELECT COUNT(*) FROM ai_tools WHERE website_url IS NOT NULL AND website_url != ""').fetchone()[0]
    }
    
    conn.close()
    
    total_pages = (total + per_page - 1) // per_page
    
    return render_template('admin/tools.html',
                         tools=tools,
                         page=page,
                         total_pages=total_pages,
                         total=total,
                         search=search,
                         category=category,
                         free_only=free_only,
                         verified_only=verified_only,
                         has_url=has_url,
                         sort_by=sort_by,
                         sort_order=sort_order,
                         categories=all_categories,
                         filter_stats=filter_stats)

@app.route('/admin/tools/add', methods=['GET', 'POST'])
@require_auth
def admin_add_tool():
    """Add new tool manually"""
    if request.method == 'POST':
        conn = get_db_connection()
        
        # Insert new tool
        conn.execute('''
            INSERT INTO ai_tools (name, description, category, website_url, 
                                source_website, free_tier, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            request.form['name'],
            request.form['description'],
            request.form['category'],
            request.form['website_url'],
            request.form.get('source_website', 'Manual Entry'),
            1 if request.form.get('free_tier') else 0,
            datetime.now()
        ))
        
        tool_id = conn.lastrowid
        
        # Log the addition
        conn.execute('''
            INSERT INTO tool_modifications (tool_id, action, new_data, modified_by)
            VALUES (?, ?, ?, ?)
        ''', (
            tool_id,
            'CREATE',
            json.dumps(dict(request.form)),
            session['admin_username']
        ))
        
        conn.commit()
        conn.close()
        
        flash('Tool added successfully!', 'success')
        return redirect(url_for('admin_tools'))
    
    return render_template('admin/add_tool.html')

@app.route('/admin/tools/edit/<int:tool_id>', methods=['GET', 'POST'])
@require_auth
def admin_edit_tool(tool_id):
    """Edit existing tool"""
    conn = get_db_connection()
    
    if request.method == 'POST':
        # Get old data for logging
        old_tool = conn.execute('SELECT * FROM ai_tools WHERE id = ?', (tool_id,)).fetchone()
        
        # Update tool
        conn.execute('''
            UPDATE ai_tools 
            SET name = ?, description = ?, category = ?, website_url = ?, 
                source_website = ?, free_tier = ?
            WHERE id = ?
        ''', (
            request.form['name'],
            request.form['description'],
            request.form['category'],
            request.form['website_url'],
            request.form.get('source_website', old_tool['source_website']),
            1 if request.form.get('free_tier') else 0,
            tool_id
        ))
        
        # Log the modification
        conn.execute('''
            INSERT INTO tool_modifications (tool_id, action, old_data, new_data, modified_by)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            tool_id,
            'UPDATE',
            json.dumps(dict(old_tool)),
            json.dumps(dict(request.form)),
            session['admin_username']
        ))
        
        conn.commit()
        conn.close()
        
        flash('Tool updated successfully!', 'success')
        return redirect(url_for('admin_tools'))
    
    tool = conn.execute('SELECT * FROM ai_tools WHERE id = ?', (tool_id,)).fetchone()
    conn.close()
    
    if not tool:
        flash('Tool not found!', 'error')
        return redirect(url_for('admin_tools'))
    
    return render_template('admin/edit_tool.html', tool=tool)

@app.route('/admin/tools/delete/<int:tool_id>', methods=['POST'])
@require_auth
def admin_delete_tool(tool_id):
    """Delete tool"""
    conn = get_db_connection()
    
    # Get tool data for logging
    tool = conn.execute('SELECT * FROM ai_tools WHERE id = ?', (tool_id,)).fetchone()
    
    if tool:
        # Delete tool
        conn.execute('DELETE FROM ai_tools WHERE id = ?', (tool_id,))
        
        # Log the deletion
        conn.execute('''
            INSERT INTO tool_modifications (tool_id, action, old_data, modified_by)
            VALUES (?, ?, ?, ?)
        ''', (
            tool_id,
            'DELETE',
            json.dumps(dict(tool)),
            session['admin_username']
        ))
        
        conn.commit()
        flash('Tool deleted successfully!', 'success')
    else:
        flash('Tool not found!', 'error')
    
    conn.close()
    return redirect(url_for('admin_tools'))

@app.route('/admin/scraping')
@require_auth
def admin_scraping():
    """Scraping management page"""
    conn = get_db_connection()
    
    jobs = conn.execute('''
        SELECT * FROM scraping_jobs 
        ORDER BY created_at DESC
    ''').fetchall()
    
    conn.close()
    
    return render_template('admin/scraping.html', jobs=jobs)

@app.route('/admin/scraping/new', methods=['GET', 'POST'])
@require_auth
def admin_new_scraping_job():
    """Create new scraping job with advanced options"""
    if request.method == 'POST':
        conn = get_db_connection()
        
        job_id = conn.execute('''
            INSERT INTO scraping_jobs (job_name, target_url, status, created_by)
            VALUES (?, ?, ?, ?)
        ''', (
            request.form['job_name'],
            request.form['target_url'],
            'pending',
            session['admin_username']
        )).lastrowid
        
        conn.commit()
        conn.close()
        
        create_notification(
            'Scraping Job Created', 
            f'New scraping job "{request.form["job_name"]}" created for {request.form["target_url"]}', 
            'info'
        )
        
        flash('Scraping job created!', 'success')
        
        # Auto-run job if requested
        if request.form.get('auto_run'):
            return redirect(url_for('admin_run_scraping_job', job_id=job_id))
        
        return redirect(url_for('admin_scraping'))
    
    return render_template('admin/new_scraping_job.html')

@app.route('/admin/scraping/run/<int:job_id>')
@require_auth
def admin_run_scraping_job(job_id):
    """Run scraping job"""
    conn = get_db_connection()
    
    job = conn.execute('SELECT * FROM scraping_jobs WHERE id = ?', (job_id,)).fetchone()
    
    if not job:
        flash('Job not found!', 'error')
        return redirect(url_for('admin_scraping'))
    
    # Update job status
    conn.execute('''
        UPDATE scraping_jobs 
        SET status = ?, started_at = ?
        WHERE id = ?
    ''', ('running', datetime.now(), job_id))
    conn.commit()
    
    try:
        # Simple scraping logic (can be enhanced)
        tools_found = scrape_website(job['target_url'])
        
        # Update job completion
        conn.execute('''
            UPDATE scraping_jobs 
            SET status = ?, completed_at = ?, tools_found = ?
            WHERE id = ?
        ''', ('completed', datetime.now(), len(tools_found), job_id))
        
        # Insert found tools
        for tool in tools_found:
            conn.execute('''
                INSERT INTO ai_tools (name, description, category, website_url, 
                                    source_website, free_tier, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                tool.get('name', 'Unknown'),
                tool.get('description', ''),
                tool.get('category', 'AI Tools'),
                tool.get('url', ''),
                job['target_url'],
                tool.get('free_tier', 0),
                datetime.now()
            ))
        
        conn.commit()
        flash(f'Scraping completed! Found {len(tools_found)} tools.', 'success')
        
    except Exception as e:
        # Update job with error
        conn.execute('''
            UPDATE scraping_jobs 
            SET status = ?, error_message = ?
            WHERE id = ?
        ''', ('failed', str(e), job_id))
        conn.commit()
        flash(f'Scraping failed: {str(e)}', 'error')
    
    conn.close()
    return redirect(url_for('admin_scraping'))

def scrape_website(url):
    """Simple website scraper - can be enhanced"""
    tools = []
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(url, headers=headers, timeout=30)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Basic scraping logic - look for tool-like content
        # This is a simplified version, can be made more sophisticated
        tools_elements = soup.find_all(['div', 'article', 'section'], class_=re.compile(r'tool|product|item|card'))
        
        for element in tools_elements[:10]:  # Limit to 10 for demo
            name = element.find(['h1', 'h2', 'h3', 'h4'])
            description = element.find(['p', 'div'], class_=re.compile(r'desc|summary|content'))
            
            if name:
                tool = {
                    'name': name.get_text().strip()[:100],
                    'description': description.get_text().strip()[:500] if description else '',
                    'category': 'AI Tools',
                    'url': url,
                    'free_tier': 1 if 'free' in element.get_text().lower() else 0
                }
                tools.append(tool)
        
    except Exception as e:
        print(f"Scraping error: {e}")
    
    return tools

@app.route('/admin/api/tools')
@require_auth
def admin_api_tools():
    """API endpoint for admin tools data"""
    conn = get_db_connection()
    tools = conn.execute('SELECT * FROM ai_tools ORDER BY created_at DESC').fetchall()
    conn.close()
    
    return jsonify([dict(tool) for tool in tools])

@app.route('/admin/api/stats')
@require_auth
def admin_api_stats():
    """API endpoint for admin statistics"""
    conn = get_db_connection()
    
    stats = {
        'total_tools': conn.execute('SELECT COUNT(*) FROM ai_tools').fetchone()[0],
        'free_tools': conn.execute('SELECT COUNT(*) FROM ai_tools WHERE free_tier = 1').fetchone()[0],
        'categories': conn.execute('SELECT COUNT(DISTINCT category) FROM ai_tools').fetchone()[0],
        'recent_modifications': conn.execute('SELECT COUNT(*) FROM tool_modifications WHERE modified_at > datetime("now", "-7 days")').fetchone()[0]
    }
    
    conn.close()
    return jsonify(stats)

@app.route('/admin/export')
@require_auth
def admin_export():
    """Export admin data"""
    conn = get_db_connection()
    df = pd.read_sql_query('SELECT * FROM ai_tools', conn)
    conn.close()
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'admin_export_{timestamp}.csv'
    df.to_csv(filename, index=False)
    
    flash(f'Data exported to {filename}', 'success')
    return redirect(url_for('admin_dashboard'))

# Bulk Operations
@app.route('/admin/tools/bulk', methods=['POST'])
@require_auth
def admin_bulk_operations():
    """Handle bulk operations on tools"""
    action = request.form.get('bulk_action')
    tool_ids = request.form.getlist('selected_tools')
    
    if not tool_ids:
        flash('No tools selected!', 'error')
        return redirect(url_for('admin_tools'))
    
    conn = get_db_connection()
    
    try:
        if action == 'delete':
            # Bulk delete
            placeholders = ','.join(['?' for _ in tool_ids])
            conn.execute(f'DELETE FROM ai_tools WHERE id IN ({placeholders})', tool_ids)
            
            # Log deletions
            for tool_id in tool_ids:
                log_tool_modification(tool_id, 'BULK_DELETE')
            
            create_notification(
                'Bulk Delete', 
                f'Deleted {len(tool_ids)} tools', 
                'warning'
            )
            flash(f'Deleted {len(tool_ids)} tools successfully!', 'success')
            
        elif action == 'verify':
            # Bulk verify
            placeholders = ','.join(['?' for _ in tool_ids])
            conn.execute(f'UPDATE ai_tools SET is_verified = 1 WHERE id IN ({placeholders})', tool_ids)
            
            # Log verifications
            for tool_id in tool_ids:
                log_tool_modification(tool_id, 'BULK_VERIFY')
            
            flash(f'Verified {len(tool_ids)} tools successfully!', 'success')
            
        elif action == 'publish':
            # Bulk add to publishing queue
            for tool_id in tool_ids:
                try:
                    conn.execute('''
                        INSERT INTO publishing_queue (tool_id, requested_by)
                        VALUES (?, ?)
                    ''', (tool_id, session['admin_username']))
                except:
                    pass  # Already in queue
            
            flash(f'Added {len(tool_ids)} tools to publishing queue!', 'success')
            
        elif action == 'category_update':
            # Bulk category update
            new_category = request.form.get('new_category')
            if new_category:
                placeholders = ','.join(['?' for _ in tool_ids])
                conn.execute(f'UPDATE ai_tools SET category = ? WHERE id IN ({placeholders})', [new_category] + tool_ids)
                flash(f'Updated category for {len(tool_ids)} tools!', 'success')
        
        conn.commit()
        
    except Exception as e:
        conn.rollback()
        flash(f'Bulk operation failed: {str(e)}', 'error')
    
    conn.close()
    return redirect(url_for('admin_tools'))

# Publishing System
@app.route('/admin/publishing')
@require_auth
def admin_publishing():
    """Publishing management page"""
    conn = get_db_connection()
    
    status_filter = request.args.get('status', '')
    
    query = '''
        SELECT pq.*, at.name as tool_name, at.category, at.description, at.website_url
        FROM publishing_queue pq
        JOIN ai_tools at ON pq.tool_id = at.id
    '''
    params = []
    
    if status_filter:
        query += ' WHERE pq.status = ?'
        params.append(status_filter)
    
    query += ' ORDER BY pq.created_at DESC'
    
    publications = conn.execute(query, params).fetchall()
    
    # Get status counts
    status_counts = {}
    for status in ['pending', 'approved', 'published', 'rejected']:
        count = conn.execute(
            'SELECT COUNT(*) FROM publishing_queue WHERE status = ?', 
            (status,)
        ).fetchone()[0]
        status_counts[status] = count
    
    conn.close()
    
    return render_template('admin/publishing.html', 
                         publications=publications,
                         status_counts=status_counts,
                         current_status=status_filter)

@app.route('/admin/publishing/<int:publication_id>/<action>')
@require_auth
def admin_publishing_action(publication_id, action):
    """Handle publishing actions"""
    conn = get_db_connection()
    
    publication = conn.execute(
        'SELECT * FROM publishing_queue WHERE id = ?', 
        (publication_id,)
    ).fetchone()
    
    if not publication:
        flash('Publication not found!', 'error')
        return redirect(url_for('admin_publishing'))
    
    if action == 'approve':
        conn.execute('''
            UPDATE publishing_queue 
            SET status = 'approved', approved_by = ?
            WHERE id = ?
        ''', (session['admin_username'], publication_id))
        
        # Mark tool as verified
        conn.execute(
            'UPDATE ai_tools SET is_verified = 1 WHERE id = ?',
            (publication['tool_id'],)
        )
        
        create_notification(
            'Tool Approved', 
            f'Tool approved for publication by {session["admin_username"]}', 
            'success'
        )
        flash('Tool approved for publication!', 'success')
        
    elif action == 'publish':
        conn.execute('''
            UPDATE publishing_queue 
            SET status = 'published', published_at = ?
            WHERE id = ?
        ''', (datetime.now(), publication_id))
        
        create_notification(
            'Tool Published', 
            f'Tool published successfully', 
            'success'
        )
        flash('Tool published successfully!', 'success')
        
    elif action == 'reject':
        reason = request.args.get('reason', 'No reason provided')
        conn.execute('''
            UPDATE publishing_queue 
            SET status = 'rejected', rejection_reason = ?
            WHERE id = ?
        ''', (reason, publication_id))
        
        flash('Tool rejected!', 'warning')
    
    conn.commit()
    conn.close()
    
    return redirect(url_for('admin_publishing'))

# Analytics and Reporting
@app.route('/admin/analytics')
@require_auth
def admin_analytics():
    """Analytics dashboard"""
    conn = get_db_connection()
    
    # Daily tool additions for the last 30 days
    daily_additions = conn.execute('''
        SELECT DATE(created_at) as date, COUNT(*) as count
        FROM ai_tools 
        WHERE created_at >= date('now', '-30 days')
        GROUP BY DATE(created_at)
        ORDER BY date
    ''').fetchall()
    
    # Category distribution
    category_distribution = conn.execute('''
        SELECT category, COUNT(*) as count
        FROM ai_tools 
        WHERE category IS NOT NULL AND category != ''
        GROUP BY category
        ORDER BY count DESC
    ''').fetchall()
    
    # Pricing model distribution
    pricing_distribution = conn.execute('''
        SELECT 
            CASE 
                WHEN free_tier = 1 THEN 'Free'
                WHEN pricing_model IS NOT NULL THEN pricing_model
                ELSE 'Unknown'
            END as pricing,
            COUNT(*) as count
        FROM ai_tools
        GROUP BY pricing
        ORDER BY count DESC
    ''').fetchall()
    
    # Top sources
    top_sources = conn.execute('''
        SELECT source_website, COUNT(*) as count
        FROM ai_tools 
        WHERE source_website IS NOT NULL
        GROUP BY source_website
        ORDER BY count DESC
        LIMIT 10
    ''').fetchall()
    
    # Monthly growth
    monthly_growth = conn.execute('''
        SELECT 
            strftime('%Y-%m', created_at) as month,
            COUNT(*) as count
        FROM ai_tools
        WHERE created_at >= date('now', '-12 months')
        GROUP BY strftime('%Y-%m', created_at)
        ORDER BY month
    ''').fetchall()
    
    conn.close()
    
    return render_template('admin/analytics.html',
                         daily_additions=daily_additions,
                         category_distribution=category_distribution,
                         pricing_distribution=pricing_distribution,
                         top_sources=top_sources,
                         monthly_growth=monthly_growth)

# Import/Export System
@app.route('/admin/import_export')
@require_auth
def admin_import_export():
    """Import/Export management page"""
    return render_template('admin/import_export.html')

@app.route('/admin/export_data')
@require_auth
def admin_export_data():
    """Export data in various formats"""
    format_type = request.args.get('format', 'csv')
    include_fields = request.args.getlist('fields')
    
    conn = get_db_connection()
    
    # Build query based on selected fields
    if include_fields:
        fields = ', '.join(include_fields)
    else:
        fields = '*'
    
    df = pd.read_sql_query(f'SELECT {fields} FROM ai_tools', conn)
    conn.close()
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    if format_type == 'csv':
        output = io.StringIO()
        df.to_csv(output, index=False)
        output.seek(0)
        
        return send_file(
            io.BytesIO(output.getvalue().encode()),
            mimetype='text/csv',
            as_attachment=True,
            download_name=f'ai_tools_export_{timestamp}.csv'
        )
    
    elif format_type == 'json':
        output = df.to_json(orient='records', indent=2)
        
        return send_file(
            io.BytesIO(output.encode()),
            mimetype='application/json',
            as_attachment=True,
            download_name=f'ai_tools_export_{timestamp}.json'
        )
    
    elif format_type == 'excel':
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='AI Tools', index=False)
        output.seek(0)
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'ai_tools_export_{timestamp}.xlsx'
        )

@app.route('/admin/import_data', methods=['POST'])
@require_auth
def admin_import_data():
    """Import data from CSV/JSON"""
    if 'file' not in request.files:
        flash('No file selected!', 'error')
        return redirect(url_for('admin_import_export'))
    
    file = request.files['file']
    if file.filename == '':
        flash('No file selected!', 'error')
        return redirect(url_for('admin_import_export'))
    
    try:
        filename = secure_filename(file.filename)
        
        if filename.endswith('.csv'):
            df = pd.read_csv(file)
        elif filename.endswith('.json'):
            df = pd.read_json(file)
        else:
            flash('Unsupported file format!', 'error')
            return redirect(url_for('admin_import_export'))
        
        # Validate required columns
        required_columns = ['name']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            flash(f'Missing required columns: {", ".join(missing_columns)}', 'error')
            return redirect(url_for('admin_import_export'))
        
        # Import data
        conn = get_db_connection()
        imported_count = 0
        
        for _, row in df.iterrows():
            try:
                # Check if tool already exists
                existing = conn.execute(
                    'SELECT id FROM ai_tools WHERE name = ?', 
                    (row['name'],)
                ).fetchone()
                
                if not existing:
                    conn.execute('''
                        INSERT INTO ai_tools (name, description, category, website_url, 
                                            source_website, free_tier, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        row.get('name', ''),
                        row.get('description', ''),
                        row.get('category', ''),
                        row.get('website_url', ''),
                        'Import',
                        int(row.get('free_tier', 0)),
                        datetime.now()
                    ))
                    imported_count += 1
            except Exception as e:
                print(f"Error importing row: {e}")
                continue
        
        conn.commit()
        conn.close()
        
        create_notification(
            'Data Import', 
            f'Imported {imported_count} tools from {filename}', 
            'success'
        )
        
        flash(f'Successfully imported {imported_count} tools!', 'success')
        
    except Exception as e:
        flash(f'Import failed: {str(e)}', 'error')
    
    return redirect(url_for('admin_import_export'))

# Notifications Management
@app.route('/admin/notifications')
@require_auth
def admin_notifications():
    """Notifications management"""
    conn = get_db_connection()
    
    notifications = conn.execute('''
        SELECT * FROM admin_notifications 
        ORDER BY created_at DESC
    ''').fetchall()
    
    conn.close()
    
    return render_template('admin/notifications.html', notifications=notifications)

@app.route('/admin/notifications/mark_read/<int:notification_id>')
@require_auth
def admin_mark_notification_read(notification_id):
    """Mark notification as read"""
    conn = get_db_connection()
    conn.execute(
        'UPDATE admin_notifications SET is_read = 1 WHERE id = ?',
        (notification_id,)
    )
    conn.commit()
    conn.close()
    
    return jsonify({'status': 'success'})

@app.route('/admin/notifications/clear_all')
@require_auth
def admin_clear_notifications():
    """Clear all notifications"""
    conn = get_db_connection()
    conn.execute(
        'UPDATE admin_notifications SET is_read = 1 WHERE target_user IS NULL OR target_user = ?',
        (session['admin_username'],)
    )
    conn.commit()
    conn.close()
    
    flash('All notifications cleared!', 'success')
    return redirect(url_for('admin_notifications'))

class APIScrapingManager:
    """Enhanced API-based scraping manager with multiple source support"""
    
    def __init__(self):
        self.supported_apis = {
            'github': self.scrape_github_api,
            'producthunt': self.scrape_producthunt_api,
            'huggingface': self.scrape_huggingface_api,
            'rest': self.scrape_generic_rest_api,
            'graphql': self.scrape_graphql_api,
            'rss': self.scrape_rss_feed
        }
        
    def execute_api_scraping(self, config_id, job_id=None):
        """Execute API scraping based on configuration"""
        conn = get_db_connection()
        
        try:
            # Get configuration
            config = conn.execute(
                'SELECT * FROM api_scraping_configs WHERE id = ? AND is_active = 1',
                (config_id,)
            ).fetchone()
            
            if not config:
                raise Exception("API configuration not found or inactive")
            
            # Check rate limiting
            if config['last_run']:
                last_run = datetime.fromisoformat(config['last_run'])
                if datetime.now() - last_run < timedelta(seconds=config['rate_limit']):
                    raise Exception(f"Rate limit exceeded. Wait {config['rate_limit']} seconds between runs")
            
            # Start scraping
            start_time = time.time()
            
            # Record start in results
            result_id = conn.execute('''
                INSERT INTO api_scraping_results 
                (config_id, job_id, status, started_at)
                VALUES (?, ?, 'running', CURRENT_TIMESTAMP)
            ''', (config_id, job_id)).lastrowid
            
            conn.commit()
            
            # Execute scraping based on API type
            api_type = config['api_type']
            if api_type in self.supported_apis:
                tools = self.supported_apis[api_type](config)
            else:
                raise Exception(f"Unsupported API type: {api_type}")
            
            # Process and save tools
            tools_saved = self.save_scraped_tools(tools, config_id)
            
            # Update results
            execution_time = time.time() - start_time
            conn.execute('''
                UPDATE api_scraping_results 
                SET status = 'completed', tools_found = ?, execution_time = ?, completed_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (tools_saved, execution_time, result_id))
            
            # Update configuration stats
            conn.execute('''
                UPDATE api_scraping_configs 
                SET last_run = CURRENT_TIMESTAMP, total_runs = total_runs + 1,
                    success_rate = (total_runs * success_rate + 1.0) / (total_runs + 1)
                WHERE id = ?
            ''', (config_id,))
            
            conn.commit()
            
            return {
                'success': True,
                'tools_found': tools_saved,
                'execution_time': execution_time
            }
            
        except Exception as e:
            # Record error
            if 'result_id' in locals():
                conn.execute('''
                    UPDATE api_scraping_results 
                    SET status = 'failed', error_message = ?, completed_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (str(e), result_id))
                conn.commit()
            
            return {
                'success': False,
                'error': str(e)
            }
        finally:
            conn.close()
    
    def scrape_github_api(self, config):
        """Scrape GitHub API for repositories"""
        tools = []
        try:
            url = config['api_url']
            headers = json.loads(config['headers'] or '{}')
            params = json.loads(config['parameters'] or '{}')
            mapping = json.loads(config['data_mapping'])
            
            response = requests.get(url, headers=headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            for item in data.get('items', []):
                tool = self.map_api_data(item, mapping)
                if tool['name']:  # Only add if name exists
                    tools.append(tool)
                    
        except Exception as e:
            print(f"GitHub API scraping error: {e}")
            
        return tools
    
    def scrape_huggingface_api(self, config):
        """Scrape Hugging Face API for models"""
        tools = []
        try:
            url = config['api_url']
            headers = json.loads(config['headers'] or '{}')
            params = json.loads(config['parameters'] or '{}')
            mapping = json.loads(config['data_mapping'])
            
            response = requests.get(url, headers=headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            for item in data:
                tool = self.map_api_data(item, mapping)
                if tool['name']:
                    tools.append(tool)
                    
        except Exception as e:
            print(f"Hugging Face API scraping error: {e}")
            
        return tools
    
    def scrape_producthunt_api(self, config):
        """Scrape Product Hunt GraphQL API"""
        tools = []
        try:
            url = config['api_url']
            headers = json.loads(config['headers'] or '{}')
            query_data = json.loads(config['parameters'] or '{}')
            mapping = json.loads(config['data_mapping'])
            
            response = requests.post(url, headers=headers, json=query_data, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # Handle GraphQL response structure
            posts = data.get('data', {}).get('posts', {}).get('nodes', [])
            for item in posts:
                tool = self.map_api_data(item, mapping)
                if tool['name']:
                    tools.append(tool)
                    
        except Exception as e:
            print(f"Product Hunt API scraping error: {e}")
            
        return tools
    
    def scrape_generic_rest_api(self, config):
        """Scrape any generic REST API"""
        tools = []
        try:
            url = config['api_url']
            headers = json.loads(config['headers'] or '{}')
            params = json.loads(config['parameters'] or '{}')
            mapping = json.loads(config['data_mapping'])
            
            response = requests.get(url, headers=headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # Handle different response structures
            if isinstance(data, list):
                items = data
            elif isinstance(data, dict):
                # Try common patterns
                items = data.get('results', data.get('data', data.get('items', [data])))
            else:
                items = [data]
            
            for item in items:
                tool = self.map_api_data(item, mapping)
                if tool['name']:
                    tools.append(tool)
                    
        except Exception as e:
            print(f"Generic REST API scraping error: {e}")
            
        return tools
    
    def scrape_graphql_api(self, config):
        """Scrape GraphQL API"""
        return self.scrape_producthunt_api(config)  # Same logic for now
    
    def scrape_rss_feed(self, config):
        """Scrape RSS/Atom feeds"""
        tools = []
        try:
            try:
                import feedparser
            except ImportError:
                raise Exception("feedparser library not installed. Run: pip install feedparser")
            
            url = config['api_url']
            mapping = json.loads(config['data_mapping'])
            
            feed = feedparser.parse(url)
            
            for entry in feed.entries:
                # Convert feedparser entry to dict-like object for mapping
                entry_dict = {
                    'title': entry.get('title', ''),
                    'summary': entry.get('summary', ''),
                    'link': entry.get('link', ''),
                    'description': entry.get('description', ''),
                    'url': entry.get('link', '')
                }
                tool = self.map_api_data(entry_dict, mapping)
                if tool['name']:
                    tools.append(tool)
                    
        except Exception as e:
            print(f"RSS feed scraping error: {e}")
            
        return tools
    
    def map_api_data(self, item, mapping):
        """Map API response data to tool structure"""
        tool = {
            'name': '',
            'description': '',
            'category': 'AI Tools',
            'url': '',
            'free_tier': 0
        }
        
        for tool_field, api_field in mapping.items():
            if api_field in item:
                value = item[api_field]
                if tool_field == 'name':
                    tool[tool_field] = str(value)[:100] if value else ''
                elif tool_field == 'description':
                    tool[tool_field] = str(value)[:500] if value else ''
                elif tool_field == 'url':
                    tool[tool_field] = str(value) if value else ''
                elif tool_field == 'category':
                    tool[tool_field] = str(value) if value else 'AI Tools'
                elif tool_field == 'free_tier':
                    tool[tool_field] = 1 if 'free' in str(value).lower() else 0
        
        return tool
    
    def save_scraped_tools(self, tools, config_id):
        """Save scraped tools to database"""
        conn = get_db_connection()
        saved_count = 0
        
        for tool in tools:
            try:
                # Check if tool already exists
                existing = conn.execute(
                    'SELECT id FROM ai_tools WHERE name = ? AND url = ?',
                    (tool['name'], tool['url'])
                ).fetchone()
                
                if not existing:
                    conn.execute('''
                        INSERT INTO ai_tools (name, description, category, url, free_tier, source, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    ''', (tool['name'], tool['description'], tool['category'], 
                         tool['url'], tool['free_tier'], f'API-{config_id}'))
                    saved_count += 1
                    
            except Exception as e:
                print(f"Error saving tool {tool['name']}: {e}")
        
        conn.commit()
        conn.close()
        return saved_count

# Initialize API scraping manager
api_scraper = APIScrapingManager()

@app.route('/admin/api-scraping')
@require_auth
def admin_api_scraping():
    """API scraping management page"""
    try:
        conn = get_db_connection()

        # Get API configurations
        configs = conn.execute('''
            SELECT ac.*, COUNT(ar.id) as total_results
            FROM api_scraping_configs ac
            LEFT JOIN api_scraping_results ar ON ac.id = ar.config_id
            GROUP BY ac.id
            ORDER BY ac.created_at DESC
        ''').fetchall()

        # Get recent results
        recent_results = conn.execute('''
            SELECT ar.*, ac.config_name
            FROM api_scraping_results ar
            JOIN api_scraping_configs ac ON ar.config_id = ac.id
            ORDER BY ar.started_at DESC
            LIMIT 10
        ''').fetchall()

        conn.close()

        return render_template('admin/api_scraping.html',
                             configs=configs,
                             recent_results=recent_results)

    except Exception as e:
        # Return a simple working page if template fails
        return f"""
        <h1>🔌 API Scraping Management</h1>
        <p>✅ Route is working correctly!</p>
        <p>🔧 Template integration in progress...</p>
        <p>Error details: {str(e)}</p>
        <p><a href="/admin/dashboard">← Back to Dashboard</a></p>
        """

@app.route('/admin/api-scraping/config/new')
@require_auth
def admin_new_api_config():
    """New API configuration page"""
    return render_template('admin/new_api_config.html')

@app.route('/admin/api-scraping/config/edit/<int:config_id>')
@require_auth
def admin_edit_api_config(config_id):
    """Edit API configuration page"""
    conn = get_db_connection()
    config = conn.execute('SELECT * FROM api_scraping_configs WHERE id = ?', (config_id,)).fetchone()
    conn.close()
    
    if not config:
        flash('API configuration not found', 'error')
        return redirect(url_for('admin_api_scraping'))
    
    return render_template('admin/edit_api_config.html', config=config)

@app.route('/admin/api-scraping/config/save', methods=['POST'])
@require_auth
def admin_save_api_config():
    """Save API configuration"""
    try:
        config_id = request.form.get('config_id')
        config_name = request.form.get('config_name', '').strip()
        api_type = request.form.get('api_type', '').strip()
        api_url = request.form.get('api_url', '').strip()
        api_key = request.form.get('api_key', '').strip()
        headers = request.form.get('headers', '{}').strip()
        parameters = request.form.get('parameters', '{}').strip()
        data_mapping = request.form.get('data_mapping', '{}').strip()
        rate_limit = int(request.form.get('rate_limit', 60))
        is_active = 1 if request.form.get('is_active') else 0
        
        # Validate JSON fields
        try:
            json.loads(headers)
            json.loads(parameters)
            json.loads(data_mapping)
        except json.JSONDecodeError as e:
            flash(f'Invalid JSON format: {e}', 'error')
            return redirect(request.referrer)
        
        conn = get_db_connection()
        
        if config_id:  # Edit existing
            conn.execute('''
                UPDATE api_scraping_configs 
                SET config_name = ?, api_type = ?, api_url = ?, api_key = ?,
                    headers = ?, parameters = ?, data_mapping = ?, 
                    rate_limit = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (config_name, api_type, api_url, api_key, headers, parameters, 
                 data_mapping, rate_limit, is_active, config_id))
            
            log_tool_modification(None, 'api_config_updated', 
                                old_data=f'Config ID: {config_id}',
                                new_data=config_name)
            flash('API configuration updated successfully!', 'success')
        else:  # Create new
            conn.execute('''
                INSERT INTO api_scraping_configs 
                (config_name, api_type, api_url, api_key, headers, parameters, 
                 data_mapping, rate_limit, is_active, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (config_name, api_type, api_url, api_key, headers, parameters, 
                 data_mapping, rate_limit, is_active, session['admin_username']))
            
            log_tool_modification(None, 'api_config_created', 
                                new_data=config_name)
            flash('API configuration created successfully!', 'success')
        
        conn.commit()
        conn.close()
        
        create_notification(
            'API Configuration Saved',
            f'API configuration "{config_name}" has been saved',
            'success'
        )
        
    except Exception as e:
        flash(f'Error saving API configuration: {str(e)}', 'error')
    
    return redirect(url_for('admin_api_scraping'))

@app.route('/admin/api-scraping/config/delete/<int:config_id>', methods=['POST'])
@require_auth
def admin_delete_api_config(config_id):
    """Delete API configuration"""
    try:
        conn = get_db_connection()
        
        # Get config name for logging
        config = conn.execute('SELECT config_name FROM api_scraping_configs WHERE id = ?', (config_id,)).fetchone()
        
        if config:
            # Delete related results first
            conn.execute('DELETE FROM api_scraping_results WHERE config_id = ?', (config_id,))
            # Delete configuration
            conn.execute('DELETE FROM api_scraping_configs WHERE id = ?', (config_id,))
            conn.commit()
            
            log_tool_modification(None, 'api_config_deleted', 
                                old_data=config['config_name'])
            
            flash('API configuration deleted successfully!', 'success')
        else:
            flash('API configuration not found', 'error')
        
        conn.close()
        
    except Exception as e:
        flash(f'Error deleting API configuration: {str(e)}', 'error')
    
    return redirect(url_for('admin_api_scraping'))

@app.route('/admin/api-scraping/run/<int:config_id>', methods=['POST'])
@require_auth
def admin_run_api_scraping(config_id):
    """Run API scraping for specific configuration"""
    try:
        # Create a scraping job first
        conn = get_db_connection()
        config = conn.execute('SELECT config_name FROM api_scraping_configs WHERE id = ?', (config_id,)).fetchone()
        
        if not config:
            return jsonify({'success': False, 'error': 'Configuration not found'})
        
        job_id = conn.execute('''
            INSERT INTO scraping_jobs (job_name, target_url, status, created_by)
            VALUES (?, ?, 'running', ?)
        ''', (f"API Scraping: {config['config_name']}", 
              f"API Config ID: {config_id}", 
              session['admin_username'])).lastrowid
        
        conn.commit()
        conn.close()
        
        # Execute API scraping
        result = api_scraper.execute_api_scraping(config_id, job_id)
        
        # Update job status
        conn = get_db_connection()
        if result['success']:
            conn.execute('''
                UPDATE scraping_jobs 
                SET status = 'completed', tools_found = ?, completed_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (result['tools_found'], job_id))
            
            create_notification(
                'API Scraping Completed',
                f'Found {result["tools_found"]} tools in {result["execution_time"]:.2f} seconds',
                'success'
            )
        else:
            conn.execute('''
                UPDATE scraping_jobs 
                SET status = 'failed', error_message = ?, completed_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (result['error'], job_id))
        
        conn.commit()
        conn.close()
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/api-scraping/test/<int:config_id>', methods=['POST'])
@require_auth
def admin_test_api_config(config_id):
    """Test API configuration without saving results"""
    try:
        conn = get_db_connection()
        config = conn.execute('SELECT * FROM api_scraping_configs WHERE id = ?', (config_id,)).fetchone()
        conn.close()
        
        if not config:
            return jsonify({'success': False, 'error': 'Configuration not found'})
        
        # Test the API call
        api_type = config['api_type']
        if api_type in api_scraper.supported_apis:
            start_time = time.time()
            tools = api_scraper.supported_apis[api_type](config)
            execution_time = time.time() - start_time
            
            return jsonify({
                'success': True,
                'tools_found': len(tools),
                'execution_time': execution_time,
                'sample_tools': tools[:3]  # Return first 3 tools as sample
            })
        else:
            return jsonify({'success': False, 'error': f'Unsupported API type: {api_type}'})
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/api-scraping/toggle/<int:config_id>', methods=['POST'])
@require_auth
def admin_toggle_api_config(config_id):
    """Toggle API configuration active status"""
    try:
        conn = get_db_connection()
        config = conn.execute('SELECT is_active, config_name FROM api_scraping_configs WHERE id = ?', (config_id,)).fetchone()
        
        if config:
            new_status = 0 if config['is_active'] else 1
            conn.execute('UPDATE api_scraping_configs SET is_active = ? WHERE id = ?', (new_status, config_id))
            conn.commit()
            
            status_text = 'activated' if new_status else 'deactivated'
            flash(f'API configuration "{config["config_name"]}" {status_text}', 'success')
        else:
            flash('API configuration not found', 'error')
        
        conn.close()
        
    except Exception as e:
        flash(f'Error toggling configuration: {str(e)}', 'error')
    
    return redirect(url_for('admin_api_scraping'))

@app.route('/admin/api-scraping/bulk-run', methods=['POST'])
@require_auth
def admin_bulk_run_api_scraping():
    """Run multiple API configurations"""
    try:
        config_ids = request.json.get('config_ids', [])
        
        if not config_ids:
            return jsonify({'success': False, 'error': 'No configurations selected'})
        
        results = []
        total_tools = 0
        
        for config_id in config_ids:
            result = api_scraper.execute_api_scraping(config_id)
            results.append({
                'config_id': config_id,
                'result': result
            })
            
            if result['success']:
                total_tools += result['tools_found']
        
        create_notification(
            'Bulk API Scraping Completed',
            f'Processed {len(config_ids)} configurations, found {total_tools} total tools',
            'success'
        )
        
        return jsonify({
            'success': True,
            'results': results,
            'total_tools': total_tools
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/api-scraping/results/<int:config_id>')
@require_auth
def admin_api_scraping_results(config_id):
    """View detailed results for an API configuration"""
    conn = get_db_connection()
    
    config = conn.execute('SELECT * FROM api_scraping_configs WHERE id = ?', (config_id,)).fetchone()
    
    if not config:
        flash('API configuration not found', 'error')
        return redirect(url_for('admin_api_scraping'))
    
    results = conn.execute('''
        SELECT * FROM api_scraping_results 
        WHERE config_id = ? 
        ORDER BY started_at DESC
    ''', (config_id,)).fetchall()
    
    # Get tools found from this config
    tools = conn.execute('''
        SELECT * FROM ai_tools 
        WHERE source = ? 
        ORDER BY created_at DESC
    ''', (f'API-{config_id}',)).fetchall()
    
    conn.close()
    
    return render_template('admin/api_scraping_results.html', 
                         config=config, 
                         results=results, 
                         tools=tools)

# Simple API scraping route for testing (temporary)
@app.route('/admin/api-scraping-test')
@require_auth  
def admin_api_scraping_test():
    """Simple test API scraping page"""
    return "<h1>API Scraping Test</h1><p>This is a test route to verify URL generation works.</p>"
