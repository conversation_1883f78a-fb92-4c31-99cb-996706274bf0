"""
Quick route inspection script
"""
import os
import sys

# Set up the environment
os.chdir(r'c:\Users\<USER>\Desktop\freelance\toolscrape')
sys.path.insert(0, os.getcwd())

try:
    print("Importing admin_dashboard...")
    import admin_dashboard
    
    app = admin_dashboard.app
    print(f"Flask app loaded successfully!")
    
    print("\nAll registered routes:")
    for rule in sorted(app.url_map.iter_rules(), key=lambda x: x.rule):
        print(f"  {rule.endpoint:<30} -> {rule.rule}")
    
    # Check specifically for API scraping routes
    api_routes = [rule for rule in app.url_map.iter_rules() if 'api_scraping' in rule.endpoint]
    print(f"\nAPI Scraping routes ({len(api_routes)}):")
    for route in api_routes:
        print(f"  {route.endpoint} -> {route.rule}")
        
    # Test if the route can be accessed
    with app.test_client() as client:
        print("\nTesting route accessibility...")
        response = client.get('/admin/api-scraping')
        print(f"GET /admin/api-scraping: {response.status_code}")
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
