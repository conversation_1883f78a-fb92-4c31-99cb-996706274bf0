<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Tools - Browse</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .filters {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .filters-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 15px;
            align-items: end;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
        }
        
        .filter-group label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .filter-group input, .filter-group select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .filter-btn {
            padding: 8px 16px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .filter-btn:hover {
            background: #764ba2;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .tool-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .tool-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .tool-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .tool-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
        }
        
        .tool-category {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .tool-source {
            color: #666;
        }
        
        .tool-url {
            margin-top: 10px;
        }
        
        .tool-url a {
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .tool-url a:hover {
            text-decoration: underline;
        }
        
        .pagination {
            text-align: center;
            margin-top: 30px;
        }
        
        .pagination a, .pagination span {
            display: inline-block;
            padding: 8px 12px;
            margin: 0 2px;
            border: 1px solid #ddd;
            text-decoration: none;
            color: #333;
        }
        
        .pagination .current {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .pagination a:hover {
            background: #f0f0f0;
        }
        
        .results-info {
            margin-bottom: 20px;
            color: #666;
        }
        
        .nav-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #667eea;
            text-decoration: none;
        }
        
        .nav-link:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .tools-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Browse AI Tools</h1>
    </div>
    
    <div class="container">
        <a href="/" class="nav-link">← Back to Dashboard</a>
        
        <div class="filters">
            <form method="GET">
                <div class="filters-grid">
                    <div class="filter-group">
                        <label for="search">Search:</label>
                        <input type="text" id="search" name="search" value="{{ search }}" placeholder="Search tools...">
                    </div>
                    
                    <div class="filter-group">
                        <label for="category">Category:</label>
                        <select id="category" name="category">
                            <option value="">All Categories</option>
                            {% for cat in categories %}
                            <option value="{{ cat.category }}" {% if cat.category == category %}selected{% endif %}>
                                {{ cat.category }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="source">Source:</label>
                        <select id="source" name="source">
                            <option value="">All Sources</option>
                            {% for src in sources %}
                            <option value="{{ src.source_website }}" {% if src.source_website == source %}selected{% endif %}>
                                {{ src.source_website }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <button type="submit" class="filter-btn">Filter</button>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="results-info">
            Showing {{ tools|length }} of {{ total }} tools 
            {% if search %}for "{{ search }}"{% endif %}
            {% if category %}in {{ category }}{% endif %}
            {% if source %}from {{ source }}{% endif %}
        </div>
        
        <div class="tools-grid">
            {% for tool in tools %}
            <div class="tool-card">
                <div class="tool-name">{{ tool.name }}</div>
                <div class="tool-description">
                    {{ tool.description[:200] }}{% if tool.description|length > 200 %}...{% endif %}
                </div>
                
                {% if tool.website_url %}
                <div class="tool-url">
                    <a href="{{ tool.website_url }}" target="_blank">🔗 Visit Website</a>
                </div>
                {% endif %}
                
                <div class="tool-meta">
                    <span class="tool-category">{{ tool.category or 'Uncategorized' }}</span>
                    <span class="tool-source">{{ tool.source_website }}</span>
                </div>
            </div>
            {% endfor %}
        </div>
        
        {% if total_pages > 1 %}
        <div class="pagination">
            {% if page > 1 %}
            <a href="?page={{ page - 1 }}&search={{ search }}&category={{ category }}&source={{ source }}">‹ Previous</a>
            {% endif %}
            
            {% for p in range(1, total_pages + 1) %}
                {% if p == page %}
                <span class="current">{{ p }}</span>
                {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 2 and p <= page + 2) %}
                <a href="?page={{ p }}&search={{ search }}&category={{ category }}&source={{ source }}">{{ p }}</a>
                {% elif p == 4 and page > 6 %}
                <span>...</span>
                {% elif p == total_pages - 3 and page < total_pages - 5 %}
                <span>...</span>
                {% endif %}
            {% endfor %}
            
            {% if page < total_pages %}
            <a href="?page={{ page + 1 }}&search={{ search }}&category={{ category }}&source={{ source }}">Next ›</a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</body>
</html>
