{% extends "admin/base.html" %}

{% block title %}🔔 Notifications{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">🔔 Notifications</h1>
    <p class="page-subtitle">Manage system notifications and alerts</p>
</div>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">Recent Notifications</h3>
    </div>
    
    {% if notifications %}
    <div class="notifications-list">
        {% for notification in notifications %}
        <div class="notification-item notification-{{ notification.type }}">
            <div class="notification-icon">
                {% if notification.type == 'success' %}✅
                {% elif notification.type == 'warning' %}⚠️
                {% elif notification.type == 'error' %}❌
                {% else %}ℹ️{% endif %}
            </div>
            <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-message">{{ notification.message }}</div>
                <div class="notification-time">{{ notification.created_at }}</div>
            </div>
            <div class="notification-actions">
                <button class="btn btn-sm btn-secondary" onclick="markAsRead({{ notification.id }})">
                    Mark as Read
                </button>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="empty-state">
        <div class="empty-state-icon">🔔</div>
        <div class="empty-state-title">No Notifications</div>
        <div class="empty-state-description">
            You're all caught up! No new notifications at the moment.
        </div>
    </div>
    {% endif %}
</div>

<style>
/* Notifications specific styles */
.notifications-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.notification-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.notification-success {
    border-left-color: #10b981;
}

.notification-warning {
    border-left-color: #f59e0b;
}

.notification-error {
    border-left-color: #ef4444;
}

.notification-info {
    border-left-color: #3b82f6;
}

.notification-icon {
    font-size: 1.5rem;
    margin-top: 2px;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: #e2e8f0;
    margin-bottom: 5px;
}

.notification-message {
    color: #94a3b8;
    line-height: 1.5;
    margin-bottom: 8px;
}

.notification-time {
    font-size: 0.8rem;
    color: #64748b;
}

.notification-actions {
    display: flex;
    align-items: center;
}

@media (max-width: 768px) {
    .notification-item {
        flex-direction: column;
        gap: 10px;
    }
    
    .notification-actions {
        align-self: flex-end;
    }
}
</style>

<script>
function markAsRead(notificationId) {
    // Implementation for marking notification as read
    fetch(`/admin/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => console.error('Error:', error));
}
</script>
{% endblock %}
