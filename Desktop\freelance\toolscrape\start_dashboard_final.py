#!/usr/bin/env python3
"""
Final startup script for the admin dashboard
This version handles all known issues and provides a robust startup
"""

import os
import sys
import time
import webbrowser
import threading
import subprocess

def ensure_dependencies():
    """Ensure all required dependencies are installed"""
    python_exe = "C:/Users/<USER>/Desktop/freelance/toolscrape/.venv/Scripts/python.exe"
    
    required_packages = [
        'flask',
        'pandas', 
        'requests',
        'beautifulsoup4',
        'feedparser'
    ]
    
    print("📦 Checking dependencies...")
    for package in required_packages:
        try:
            subprocess.check_call([python_exe, "-c", f"import {package}"], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        except subprocess.CalledProcessError:
            print(f"Installing {package}...")
            subprocess.check_call([python_exe, "-m", "pip", "install", package])

def test_dashboard():
    """Test dashboard functionality"""
    import requests
    
    print("🧪 Testing dashboard...")
    
    try:
        # Test login page
        response = requests.get("http://localhost:5001/admin/login", timeout=5)
        if response.status_code == 200:
            print("✅ Login page working")
        else:
            print(f"⚠️  Login page returned {response.status_code}")
        
        # Test API scraping route
        response = requests.get("http://localhost:5001/admin/api-scraping", timeout=5)
        if response.status_code in [200, 302]:  # 302 = redirect to login
            print("✅ API scraping route working")
        else:
            print(f"⚠️  API scraping route returned {response.status_code}")
            
        return True
        
    except Exception as e:
        print(f"❌ Dashboard test failed: {e}")
        return False

def main():
    print("🚀 Starting Admin Dashboard - Final Version")
    print("=" * 50)
    
    # Ensure we're in the right directory
    os.chdir(r'c:\Users\<USER>\Desktop\freelance\toolscrape')
    print(f"📍 Working directory: {os.getcwd()}")
    
    try:
        # Install dependencies
        ensure_dependencies()
        
        # Import and start the dashboard
        print("📥 Starting dashboard server...")
        
        # Use the complete dashboard version
        from admin_dashboard_complete import app
        
        print("✅ Dashboard imported successfully!")
        
        # Verify routes
        routes = [rule.endpoint for rule in app.url_map.iter_rules()]
        api_routes = [r for r in routes if 'api_scraping' in r]
        
        print(f"🛠️  Total routes: {len(routes)}")
        print(f"🔌 API scraping routes: {api_routes}")
        
        if 'admin_api_scraping' in routes:
            print("✅ admin_api_scraping route confirmed!")
        else:
            print("❌ admin_api_scraping route missing!")
        
        # Open browser after delay
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open("http://localhost:5001/admin/login")
                print("🌐 Browser opened automatically")
                
                # Test after a moment
                time.sleep(2)
                if test_dashboard():
                    print("🎉 Dashboard is fully functional!")
                    
            except Exception as e:
                print(f"⚠️  Browser error: {e}")
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        print("\n🌐 Server starting on http://localhost:5001")
        print("🔐 Login credentials:")
        print("   Username: admin")
        print("   Password: admin123")
        print("\n📋 Available URLs:")
        print("   Login:        http://localhost:5001/admin/login")
        print("   Dashboard:    http://localhost:5001/admin/dashboard")
        print("   Tools:        http://localhost:5001/admin/tools")
        print("   API Scraping: http://localhost:5001/admin/api-scraping")
        print("   Web Scraping: http://localhost:5001/admin/scraping")
        print("\n🔄 Starting Flask server...")
        print("Press Ctrl+C to stop")
        print("=" * 50)
        
        # Start the Flask app
        app.run(host='127.0.0.1', port=5001, debug=False, use_reloader=False)
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n💡 Solutions:")
        print("1. Install missing packages: pip install -r requirements.txt")
        print("2. Check virtual environment is activated")
        input("\nPress Enter to exit...")
        
    except Exception as e:
        print(f"❌ Startup Error: {e}")
        import traceback
        print("\nFull traceback:")
        traceback.print_exc()
        input("\nPress Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Admin dashboard stopped by user")
        print("✅ Shutdown complete")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        input("Press Enter to exit...")
