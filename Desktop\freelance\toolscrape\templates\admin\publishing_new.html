{% extends "admin/base.html" %}

{% block title %}📢 Publishing Management{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">📢 Publishing Management</h1>
</div>

<!-- Status Filter Tabs -->
<div class="status-tabs">
    <a href="{{ url_for('admin_publishing') }}" class="status-tab {% if not current_status %}active{% endif %}">
        All Publications
        <span class="status-count">{{ status_counts.pending + status_counts.approved + status_counts.published + status_counts.rejected }}</span>
    </a>
    <a href="{{ url_for('admin_publishing', status='pending') }}" class="status-tab {% if current_status == 'pending' %}active{% endif %}">
        Pending
        <span class="status-count">{{ status_counts.pending }}</span>
    </a>
    <a href="{{ url_for('admin_publishing', status='approved') }}" class="status-tab {% if current_status == 'approved' %}active{% endif %}">
        Approved
        <span class="status-count">{{ status_counts.approved }}</span>
    </a>
    <a href="{{ url_for('admin_publishing', status='published') }}" class="status-tab {% if current_status == 'published' %}active{% endif %}">
        Published
        <span class="status-count">{{ status_counts.published }}</span>
    </a>
    <a href="{{ url_for('admin_publishing', status='rejected') }}" class="status-tab {% if current_status == 'rejected' %}active{% endif %}">
        Rejected
        <span class="status-count">{{ status_counts.rejected }}</span>
    </a>
</div>

<!-- Publications Grid -->
{% if publications %}
<div class="publications-grid">
    {% for pub in publications %}
    <div class="publication-card">
        <div class="publication-header">
            <div>
                <div class="publication-title">{{ pub.tool_name }}</div>
                <div class="publication-meta">
                    Category: {{ pub.category or 'Uncategorized' }} • 
                    Requested by: {{ pub.requested_by }} • 
                    {{ pub.created_at }}
                </div>
            </div>
            <div class="publication-status">
                <span class="badge badge-{{ pub.status }}">{{ pub.status.title() }}</span>
            </div>
        </div>
        
        {% if pub.description %}
        <div class="publication-description">
            {{ pub.description[:200] }}{% if pub.description|length > 200 %}...{% endif %}
        </div>
        {% endif %}
        
        {% if pub.website_url %}
        <div style="margin-bottom: 15px;">
            <a href="{{ pub.website_url }}" target="_blank" class="publication-url">🔗 {{ pub.website_url }}</a>
        </div>
        {% endif %}
        
        {% if pub.status == 'pending' %}
        <div class="publication-actions">
            <a href="{{ url_for('admin_publishing_action', publication_id=pub.id, action='approve') }}" 
               class="btn btn-success"
               onclick="return confirm('Approve this tool for publication?')">
                ✅ Approve
            </a>
            <button class="btn btn-danger" onclick="showRejectForm({{ pub.id }})">
                ❌ Reject
            </button>
        </div>
        
        <div class="reject-form" id="rejectForm{{ pub.id }}">
            <form method="GET" action="{{ url_for('admin_publishing_action', publication_id=pub.id, action='reject') }}">
                <textarea name="reason" class="form-control" placeholder="Rejection reason..." required></textarea>
                <button type="submit" class="btn btn-danger btn-sm">Confirm Rejection</button>
                <button type="button" class="btn btn-secondary btn-sm" onclick="hideRejectForm({{ pub.id }})">Cancel</button>
            </form>
        </div>
        {% elif pub.status == 'approved' %}
        <div class="publication-actions">
            <a href="{{ url_for('admin_publishing_action', publication_id=pub.id, action='publish') }}" 
               class="btn btn-primary"
               onclick="return confirm('Publish this tool to the public database?')">
                🚀 Publish Now
            </a>
        </div>
        {% elif pub.status == 'published' %}
        <div class="publication-actions">
            <span style="color: #10b981;">✅ Published {{ pub.published_at }}</span>
        </div>
        {% elif pub.status == 'rejected' %}
        <div class="publication-actions">
            <span style="color: #ef4444;">❌ Rejected</span>
            {% if pub.rejection_reason %}
            <div style="margin-top: 10px; padding: 10px; background: rgba(239, 68, 68, 0.1); border-radius: 6px; color: #fca5a5;">
                Reason: {{ pub.rejection_reason }}
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
    {% endfor %}
</div>
{% else %}
<div class="empty-state">
    <div class="empty-state-icon">📭</div>
    <div class="empty-state-title">No Publications Found</div>
    <div class="empty-state-description">
        {% if current_status %}No {{ current_status }} publications at the moment.{% else %}No publications in the queue yet.{% endif %}
    </div>
</div>
{% endif %}

<style>
/* Publishing specific styles */
.status-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.status-tab {
    padding: 12px 20px;
    border-radius: 10px;
    text-decoration: none;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #cbd5e1;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-tab:hover, .status-tab.active {
    background: rgba(59, 130, 246, 0.1);
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-1px);
}

.status-count {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-tab.active .status-count {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.publications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
    gap: 25px;
}

.publication-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.publication-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border-color: rgba(59, 130, 246, 0.3);
}

.publication-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.publication-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #e2e8f0;
    margin-bottom: 5px;
}

.publication-meta {
    color: #94a3b8;
    font-size: 0.9rem;
}

.publication-status {
    margin-left: auto;
}

.badge {
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-pending {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.badge-approved {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.badge-published {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.badge-rejected {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.publication-description {
    color: #cbd5e1;
    margin-bottom: 15px;
    line-height: 1.6;
}

.publication-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.publication-url {
    color: #3b82f6;
    text-decoration: none;
    font-size: 0.9rem;
}

.publication-url:hover {
    text-decoration: underline;
}

.reject-form {
    margin-top: 10px;
    display: none;
    padding: 15px;
    background: rgba(239, 68, 68, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.reject-form.show {
    display: block;
}

@media (max-width: 768px) {
    .status-tabs {
        flex-direction: column;
        gap: 5px;
    }
    
    .publication-header {
        flex-direction: column;
        gap: 10px;
    }
    
    .publication-actions {
        flex-direction: column;
    }
    
    .publications-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
function showRejectForm(id) {
    document.getElementById('rejectForm' + id).classList.add('show');
}

function hideRejectForm(id) {
    document.getElementById('rejectForm' + id).classList.remove('show');
}
</script>
{% endblock %}
