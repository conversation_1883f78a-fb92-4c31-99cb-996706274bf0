<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Enhanced AI Tools Database - Premium Collection</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #e2e8f0;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .enhanced-badge {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, #f59e0b, #ef4444);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
            animation: pulse 2s infinite;
            z-index: 1000;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            padding: 30px 0;
            position: relative;
        }
        
        .header h1 {
            font-size: 4rem;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
        }
        
        .header p {
            font-size: 1.4rem;
            opacity: 0.9;
            margin-bottom: 25px;
        }
        
        .header .subtitle {
            font-size: 1.1rem;
            opacity: 0.8;
            background: rgba(59, 130, 246, 0.1);
            padding: 10px 20px;
            border-radius: 50px;
            display: inline-block;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 50px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-8px);
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .stat-number {
            font-size: 3.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.2rem;
            color: #94a3b8;
            margin-bottom: 8px;
        }
        
        .stat-icon {
            font-size: 2rem;
            margin-bottom: 15px;
            opacity: 0.7;
        }
        
        .nav-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 40px 0;
            flex-wrap: wrap;
        }
        
        .nav-btn {
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .nav-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }
        
        .section {
            background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
            padding: 40px;
            border-radius: 25px;
            margin-bottom: 40px;
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }
        
        .section h2 {
            font-size: 2.2rem;
            margin-bottom: 30px;
            color: #f1f5f9;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .category-item {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(59, 130, 246, 0.2);
            transition: all 0.3s ease;
        }
        
        .category-item:hover {
            transform: translateY(-5px);
            border-color: rgba(59, 130, 246, 0.4);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        
        .category-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: #3b82f6;
            margin-bottom: 10px;
        }
        
        .category-count {
            font-size: 1.1rem;
            color: #94a3b8;
        }
        
        .recent-tools {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .tool-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.1);
            transition: all 0.3s ease;
        }
        
        .tool-card:hover {
            transform: translateY(-5px);
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        
        .tool-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #f1f5f9;
            margin-bottom: 8px;
        }
        
        .tool-description {
            color: #94a3b8;
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        
        .tool-meta {
            display: flex;
            gap: 15px;
            font-size: 0.85rem;
            flex-wrap: wrap;
        }
        
        .tool-category {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            padding: 4px 10px;
            border-radius: 10px;
        }
        
        .tool-free {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
            padding: 4px 10px;
            border-radius: 10px;
        }
        
        .footer {
            text-align: center;
            padding: 40px 0;
            color: #64748b;
            border-top: 1px solid rgba(255,255,255,0.1);
            margin-top: 60px;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="enhanced-badge">✨ ENHANCED</div>
        
        <div class="header">
            <h1>🚀 AI Tools Database</h1>
            <p>Premium Enhanced Collection</p>
            <div class="subtitle">Comprehensive AI Tools Directory with Advanced Analytics</div>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">🔧</div>
                <div class="stat-number">{{ stats.total_tools }}</div>
                <div class="stat-label">Total AI Tools</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">💎</div>
                <div class="stat-number">{{ stats.free_tools }}</div>
                <div class="stat-label">Free Tools</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">🌐</div>
                <div class="stat-number">{{ stats.tools_with_urls }}</div>
                <div class="stat-label">Tools with URLs</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-number">{{ categories|length }}</div>
                <div class="stat-label">Categories</div>
            </div>
        </div>
        
        <div class="nav-buttons">
            <a href="/tools" class="nav-btn">🔍 Browse All Tools</a>
            <a href="/categories" class="nav-btn">📂 View Categories</a>
            <a href="/api/tools" class="nav-btn">🔌 API Access</a>
            <a href="/export/csv" class="nav-btn">📥 Export Data</a>
        </div>
        
        <div class="section">
            <h2>📊 Top Categories</h2>
            <div class="category-grid">
                {% for category in categories[:8] %}
                <div class="category-item">
                    <div class="category-name">{{ category.category }}</div>
                    <div class="category-count">{{ category.count }} tools</div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="section">
            <h2>🆕 Recent Tools</h2>
            <div class="recent-tools">
                {% for tool in recent_tools[:6] %}
                <div class="tool-card">
                    <div class="tool-name">{{ tool.name }}</div>
                    <div class="tool-description">
                        {% if tool.description %}
                            {{ tool.description[:150] }}{% if tool.description|length > 150 %}...{% endif %}
                        {% else %}
                            No description available
                        {% endif %}
                    </div>
                    <div class="tool-meta">
                        {% if tool.category %}
                        <span class="tool-category">{{ tool.category }}</span>
                        {% endif %}
                        {% if tool.free_tier %}
                        <span class="tool-free">Free</span>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="footer">
            <p>Enhanced AI Tools Database - Powered by Advanced Analytics</p>
            <p>Premium Collection with Modern Interface</p>
        </div>
    </div>
</body>
</html>
