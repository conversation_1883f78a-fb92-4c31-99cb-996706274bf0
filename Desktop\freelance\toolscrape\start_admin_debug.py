#!/usr/bin/env python3
"""
Simple startup script to test the admin dashboard with better error handling
"""

import os
import sys
import time
import webbrowser
import threading

def main():
    print("🚀 Starting Enhanced Admin Dashboard (Debug Mode)...")
    print("📍 Working directory:", os.getcwd())
    print("🐍 Python path:", sys.executable)

    try:
        # Check if required files exist
        required_files = ['admin_dashboard.py', 'templates/admin/login.html']
        for file_path in required_files:
            if not os.path.exists(file_path):
                print(f"❌ Missing required file: {file_path}")
                input("Press Enter to exit...")
                return

        print("📁 Required files found")

        # Import the admin dashboard
        print("📥 Importing admin dashboard...")
        from admin_dashboard import app
        
        print("✅ Admin dashboard imported successfully!")
        
        # List all routes to verify admin_api_scraping is registered
        print("\n🛠️ Verifying route registration...")
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append((rule.endpoint, rule.rule))
        
        # Check for API scraping route
        api_scraping_routes = [r for r in routes if 'api_scraping' in r[0]]
        if api_scraping_routes:
            print("✅ API scraping routes found:")
            for endpoint, rule in api_scraping_routes:
                print(f"   {endpoint} -> {rule}")
        else:
            print("⚠️  No API scraping routes found")
            
        print(f"📊 Total routes registered: {len(routes)}")
        
        print("\n🌐 Starting server on http://localhost:5001")
        print("🔐 Login credentials: admin / admin123")
        print("📋 Main Dashboard: http://localhost:5001/admin")
        print("🔌 API Scraping: http://localhost:5001/admin/api-scraping")
        
        # Test route accessibility
        print("\n🧪 Testing route accessibility...")
        with app.test_client() as client:
            test_response = client.get('/admin/api-scraping')
            if test_response.status_code == 302:
                print("✅ API scraping route responds (redirects to login as expected)")
            elif test_response.status_code == 200:
                print("✅ API scraping route responds successfully")
            else:
                print(f"⚠️  API scraping route returned status {test_response.status_code}")
        
        # Open browser after 3 seconds
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open("http://localhost:5001/admin/login")
                print("🌐 Browser opened automatically")
            except Exception as e:
                print(f"⚠️ Could not open browser: {e}")
                print("Please manually open: http://localhost:5001/admin/login")
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        print("\n🔄 Starting Flask server...")
        print("Press Ctrl+C to stop")
        print("-" * 40)
        
        # Start the Flask app
        app.run(host='127.0.0.1', port=5001, debug=True, use_reloader=False)
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("💡 Try installing dependencies:")
        print("   pip install flask pandas requests beautifulsoup4")
        input("Press Enter to exit...")
        
    except Exception as e:
        print(f"❌ Startup Error: {e}")
        import traceback
        print("\nFull traceback:")
        traceback.print_exc()
        print("\n💡 Run the diagnostic tool: python fix_admin_dashboard.py")
        input("Press Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Admin dashboard stopped by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")
