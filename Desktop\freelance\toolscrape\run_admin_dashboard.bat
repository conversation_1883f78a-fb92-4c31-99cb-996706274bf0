@echo off
title AI Tools Enhanced Admin Dashboard
color 0A

echo.
echo ================================================================
echo    🚀 AI Tools Enhanced Admin Dashboard                        
echo ================================================================
echo.
echo Starting comprehensive admin interface...
echo Features: Tools Management, Scraping, Publishing, Analytics
echo.

cd /d "C:\Users\<USER>\Desktop\freelance\toolscrape"

REM Check if virtual environment exists
if exist ".venv\Scripts\python.exe" (
    echo ✅ Using virtual environment
    ".venv\Scripts\python.exe" launch_admin_dashboard.py
) else (
    echo ⚠️ Virtual environment not found, using system Python
    python launch_admin_dashboard.py
)

echo.
echo ================================================================
echo Admin dashboard has stopped. Press any key to exit...
pause >nul
