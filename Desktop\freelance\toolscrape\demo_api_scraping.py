#!/usr/bin/env python3
"""
🔌 API Scraping Quick Start Demo
Demonstrates the new API-based scraping functionality in the admin dashboard
"""

import os
import sys
import webbrowser
import time
import threading

def main():
    print("🔌 API Scraping Management - Quick Start Demo")
    print("=" * 60)
    print()
    
    print("📋 New Features Added:")
    print("✅ Multi-source API scraping support")
    print("✅ GitHub API integration")
    print("✅ Product Hunt API integration") 
    print("✅ Hugging Face API integration")
    print("✅ Custom REST API support")
    print("✅ GraphQL API support")
    print("✅ RSS feed monitoring")
    print("✅ Advanced configuration management")
    print("✅ Real-time execution monitoring")
    print("✅ Bulk operation support")
    print("✅ Performance analytics")
    print("✅ Error handling & logging")
    print()
    
    print("🚀 Getting Started:")
    print("1. Start the admin dashboard")
    print("2. Navigate to 'API Scraping' section")
    print("3. Create your first API configuration")
    print("4. Test the configuration")
    print("5. Run scraping and monitor results")
    print()
    
    print("📖 Documentation:")
    print("• API_SCRAPING_DOCUMENTATION.md - Comprehensive guide")
    print("• Templates included for popular APIs")
    print("• Built-in testing and validation")
    print("• Real-time monitoring dashboard")
    print()
    
    choice = input("Would you like to start the admin dashboard now? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes']:
        print()
        print("🚀 Starting Admin Dashboard...")
        print("📍 Working directory:", os.getcwd())
        
        try:
            # Import the admin dashboard
            from admin_dashboard import app
            
            print("✅ Admin dashboard imported successfully!")
            print("🌐 Starting server on http://localhost:5001")
            print("🔐 Login credentials: admin / admin123")
            print("📋 Visit: http://localhost:5001/admin/api-scraping")
            print()
            
            # Open browser after 3 seconds
            def open_browser():
                time.sleep(3)
                try:
                    webbrowser.open("http://localhost:5001/admin/api-scraping")
                    print("🌐 Browser opened automatically to API Scraping page")
                except:
                    print("⚠️ Please manually open: http://localhost:5001/admin/api-scraping")
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            print("🔄 Starting Flask server...")
            print("📖 Check the API_SCRAPING_DOCUMENTATION.md for usage guide")
            print()
            
            # Start the Flask app
            app.run(host='127.0.0.1', port=5001, debug=True, use_reloader=False)
            
        except Exception as e:
            print(f"❌ Error starting dashboard: {e}")
            print("📋 Please check the error message above")
            print("💡 Make sure all dependencies are installed:")
            print("   pip install -r requirements.txt")
            
    else:
        print()
        print("📖 To start manually, run:")
        print("   python start_admin_simple.py")
        print()
        print("🔗 Navigate to API Scraping:")
        print("   http://localhost:5001/admin/api-scraping")
        print()
        print("📚 Read the documentation:")
        print("   API_SCRAPING_DOCUMENTATION.md")
    
    print()
    print("🎉 Enjoy exploring the new API scraping capabilities!")

if __name__ == "__main__":
    main()
