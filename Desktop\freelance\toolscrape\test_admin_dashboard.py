#!/usr/bin/env python3
"""
Quick test script for the enhanced admin dashboard
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Testing admin dashboard import...")
    from admin_dashboard import app
    print("✅ Admin dashboard imported successfully!")
    
    print("\nTesting database connection...")
    import sqlite3
    conn = sqlite3.connect('ai_tools_master.db')
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM ai_tools")
    count = cursor.fetchone()[0]
    conn.close()
    print(f"✅ Database connection successful! Found {count} tools.")
    
    print("\nTesting Flask app configuration...")
    with app.app_context():
        print("✅ Flask app context works!")
    
    print("\n🎉 All tests passed! Admin dashboard is ready to run.")
    print(f"📋 Run the dashboard with: python admin_dashboard.py")
    print(f"🌐 Then visit: http://localhost:5001/admin/login")
    print(f"🔐 Login with: admin / admin123")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("📦 Please install missing dependencies")
    
except Exception as e:
    print(f"❌ Error: {e}")
    print("🔍 Please check the error details above")

input("\nPress Enter to continue...")
