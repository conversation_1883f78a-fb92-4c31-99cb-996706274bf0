import sqlite3
import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
import json
from fake_useragent import UserAgent
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
from tqdm import tqdm
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor
import urllib.parse
from urllib.parse import urljoin, urlparse
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AIToolsScraper:
    def __init__(self):
        self.ua = UserAgent()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # Initialize database
        self.init_database()
        
        # Chrome options for Selenium
        self.chrome_options = Options()
        self.chrome_options.add_argument('--headless')
        self.chrome_options.add_argument('--no-sandbox')
        self.chrome_options.add_argument('--disable-dev-shm-usage')
        self.chrome_options.add_argument(f'--user-agent={self.ua.random}')
        
    def init_database(self):
        """Initialize SQLite database with comprehensive schema"""
        self.conn = sqlite3.connect('ai_tools_database.db')
        self.cursor = self.conn.cursor()
        
        # Create main tools table
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_tools (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                category TEXT,
                subcategory TEXT,
                website_url TEXT,
                pricing_model TEXT,
                pricing_details TEXT,
                features TEXT,
                rating REAL,
                reviews_count INTEGER,
                tags TEXT,
                use_cases TEXT,
                api_available BOOLEAN,
                open_source BOOLEAN,
                free_tier BOOLEAN,
                trial_available BOOLEAN,
                image_url TEXT,
                video_url TEXT,
                source_website TEXT,
                scraped_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create categories table
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE,
                description TEXT
            )
        ''')
        
        # Create pricing table
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS pricing_plans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tool_id INTEGER,
                plan_name TEXT,
                price TEXT,
                billing_cycle TEXT,
                features TEXT,
                FOREIGN KEY (tool_id) REFERENCES ai_tools (id)
            )
        ''')
        
        self.conn.commit()
        
    def get_selenium_driver(self):
        """Get Selenium WebDriver instance"""
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=self.chrome_options)
            return driver
        except Exception as e:
            logger.error(f"Error creating WebDriver: {e}")
            return None
    
    def clean_text(self, text):
        """Clean and normalize text"""
        if not text:
            return ""
        # Remove extra whitespace and newlines
        text = re.sub(r'\s+', ' ', text.strip())
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s\-\.\,\!\?\:\;]', '', text)
        return text
    
    def extract_pricing_info(self, text):
        """Extract pricing information from text"""
        pricing_patterns = [
            r'(\$[\d,]+(?:\.\d{2})?(?:/month|/mo|/year|/yr)?)',
            r'(free|premium|pro|enterprise|starter)',
            r'(\d+(?:\.\d{2})?\s*(?:usd|eur|gbp))',
        ]
        
        pricing_info = []
        for pattern in pricing_patterns:
            matches = re.findall(pattern, text.lower())
            pricing_info.extend(matches)
        
        return ', '.join(set(pricing_info)) if pricing_info else ""
    
    def scrape_bestaito_com(self):
        """Scrape tools from bestaito.com"""
        logger.info("Scraping bestaito.com...")
        tools = []
        
        try:
            url = "https://bestaito.com/tool/?pricing=free"
            response = self.session.get(url, timeout=30)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find tool cards/listings
            tool_cards = soup.find_all(['div', 'article'], class_=re.compile(r'tool|card|item|listing'))
            
            for card in tool_cards[:50]:  # Limit to avoid overwhelming
                try:
                    name = self.clean_text(card.find(['h1', 'h2', 'h3', 'h4'], class_=re.compile(r'title|name|heading')).get_text() if card.find(['h1', 'h2', 'h3', 'h4'], class_=re.compile(r'title|name|heading')) else "")
                    description = self.clean_text(card.find(['p', 'div'], class_=re.compile(r'description|excerpt|summary')).get_text() if card.find(['p', 'div'], class_=re.compile(r'description|excerpt|summary')) else "")
                    
                    # Extract URL
                    link_elem = card.find('a', href=True)
                    url = link_elem['href'] if link_elem else ""
                    if url and not url.startswith('http'):
                        url = urljoin("https://bestaito.com", url)
                    
                    # Extract pricing
                    pricing_text = card.get_text()
                    pricing = self.extract_pricing_info(pricing_text)
                    
                    if name:
                        tools.append({
                            'name': name,
                            'description': description,
                            'website_url': url,
                            'pricing_model': pricing or "Free",
                            'source_website': 'bestaito.com',
                            'category': 'AI Tools',
                            'free_tier': True
                        })
                except Exception as e:
                    logger.warning(f"Error parsing tool card: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error scraping bestaito.com: {e}")
            
        return tools
    
    def scrape_bestaitools_com(self):
        """Scrape tools from bestaitools.com"""
        logger.info("Scraping bestaitools.com...")
        tools = []
        
        try:
            driver = self.get_selenium_driver()
            if not driver:
                return tools
                
            driver.get("https://www.bestaitools.com/?sf_paged=2")
            time.sleep(3)
            
            # Find tool elements
            tool_elements = driver.find_elements(By.CSS_SELECTOR, '[class*="tool"], [class*="card"], [class*="item"]')
            
            for element in tool_elements[:50]:
                try:
                    name = element.find_element(By.CSS_SELECTOR, 'h1, h2, h3, h4, [class*="title"], [class*="name"]').text
                    description = ""
                    try:
                        description = element.find_element(By.CSS_SELECTOR, 'p, [class*="description"], [class*="excerpt"]').text
                    except:
                        pass
                    
                    # Get URL
                    try:
                        url = element.find_element(By.CSS_SELECTOR, 'a').get_attribute('href')
                    except:
                        url = ""
                    
                    if name:
                        tools.append({
                            'name': self.clean_text(name),
                            'description': self.clean_text(description),
                            'website_url': url,
                            'source_website': 'bestaitools.com',
                            'category': 'AI Tools'
                        })
                except Exception as e:
                    continue
            
            driver.quit()
            
        except Exception as e:
            logger.error(f"Error scraping bestaitools.com: {e}")
            
        return tools
    
    def scrape_10bestaitools_com(self):
        """Scrape tools from 10bestaitools.com"""
        logger.info("Scraping 10bestaitools.com...")
        tools = []
        
        try:
            url = "https://10bestaitools.com/category/ai-tools/"
            response = self.session.get(url, timeout=30)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find tool listings
            tool_elements = soup.find_all(['article', 'div'], class_=re.compile(r'post|tool|item|card'))
            
            for element in tool_elements[:50]:
                try:
                    name = ""
                    title_elem = element.find(['h1', 'h2', 'h3', 'h4'])
                    if title_elem:
                        name = self.clean_text(title_elem.get_text())
                    
                    description = ""
                    desc_elem = element.find(['p', 'div'], class_=re.compile(r'excerpt|content|description'))
                    if desc_elem:
                        description = self.clean_text(desc_elem.get_text())
                    
                    # Extract URL
                    url = ""
                    link_elem = element.find('a', href=True)
                    if link_elem:
                        url = link_elem['href']
                        if url and not url.startswith('http'):
                            url = urljoin("https://10bestaitools.com", url)
                    
                    if name:
                        tools.append({
                            'name': name,
                            'description': description,
                            'website_url': url,
                            'source_website': '10bestaitools.com',
                            'category': 'AI Tools'
                        })
                except Exception as e:
                    continue
                    
        except Exception as e:
            logger.error(f"Error scraping 10bestaitools.com: {e}")
            
        return tools
    
    def scrape_bestfreeaitools_io(self):
        """Scrape tools from bestfreeaitools.io"""
        logger.info("Scraping bestfreeaitools.io...")
        tools = []
        
        try:
            url = "https://bestfreeaitools.io/ai-tool/"
            response = self.session.get(url, timeout=30)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find tool cards
            tool_cards = soup.find_all(['div', 'article'], class_=re.compile(r'tool|card|post|item'))
            
            for card in tool_cards[:50]:
                try:
                    # Extract name
                    name = ""
                    title_elem = card.find(['h1', 'h2', 'h3', 'h4', 'h5'])
                    if title_elem:
                        name = self.clean_text(title_elem.get_text())
                    
                    # Extract description
                    description = ""
                    desc_elem = card.find(['p', 'div'], class_=re.compile(r'description|excerpt|content'))
                    if desc_elem:
                        description = self.clean_text(desc_elem.get_text())
                    
                    # Extract URL
                    url = ""
                    link_elem = card.find('a', href=True)
                    if link_elem:
                        url = link_elem['href']
                        if url and not url.startswith('http'):
                            url = urljoin("https://bestfreeaitools.io", url)
                    
                    # Extract category if available
                    category = "AI Tools"
                    cat_elem = card.find(['span', 'div'], class_=re.compile(r'category|tag'))
                    if cat_elem:
                        category = self.clean_text(cat_elem.get_text())
                    
                    if name:
                        tools.append({
                            'name': name,
                            'description': description,
                            'website_url': url,
                            'source_website': 'bestfreeaitools.io',
                            'category': category,
                            'free_tier': True
                        })
                except Exception as e:
                    continue
                    
        except Exception as e:
            logger.error(f"Error scraping bestfreeaitools.io: {e}")
            
        return tools
    
    def scrape_best_ai_tools_org(self):
        """Scrape tools from best-ai-tools.org"""
        logger.info("Scraping best-ai-tools.org...")
        tools = []
        
        try:
            url = "https://best-ai-tools.org/"
            response = self.session.get(url, timeout=30)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find tool listings
            tool_elements = soup.find_all(['div', 'article', 'section'], class_=re.compile(r'tool|card|listing|item'))
            
            for element in tool_elements[:50]:
                try:
                    name = ""
                    title_elem = element.find(['h1', 'h2', 'h3', 'h4'])
                    if title_elem:
                        name = self.clean_text(title_elem.get_text())
                    
                    description = ""
                    desc_elem = element.find(['p', 'div'], class_=re.compile(r'description|excerpt|summary'))
                    if desc_elem:
                        description = self.clean_text(desc_elem.get_text())
                    
                    # Extract URL
                    url = ""
                    link_elem = element.find('a', href=True)
                    if link_elem:
                        url = link_elem['href']
                        if url and not url.startswith('http'):
                            url = urljoin("https://best-ai-tools.org", url)
                    
                    if name:
                        tools.append({
                            'name': name,
                            'description': description,
                            'website_url': url,
                            'source_website': 'best-ai-tools.org',
                            'category': 'AI Tools'
                        })
                except Exception as e:
                    continue
                    
        except Exception as e:
            logger.error(f"Error scraping best-ai-tools.org: {e}")
            
        return tools
    
    def scrape_aisitelist_com(self):
        """Scrape tools from aisitelist.com"""
        logger.info("Scraping aisitelist.com...")
        tools = []
        
        try:
            url = "https://aisitelist.com/"
            response = self.session.get(url, timeout=30)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find tool listings
            tool_cards = soup.find_all(['div', 'li', 'article'], class_=re.compile(r'site|tool|card|listing'))
            
            for card in tool_cards[:50]:
                try:
                    name = ""
                    title_elem = card.find(['h1', 'h2', 'h3', 'h4', 'a'])
                    if title_elem:
                        name = self.clean_text(title_elem.get_text())
                    
                    description = ""
                    desc_elem = card.find(['p', 'div'], class_=re.compile(r'description|excerpt'))
                    if desc_elem:
                        description = self.clean_text(desc_elem.get_text())
                    
                    # Extract URL
                    url = ""
                    link_elem = card.find('a', href=True)
                    if link_elem:
                        url = link_elem['href']
                        if url and not url.startswith('http'):
                            url = urljoin("https://aisitelist.com", url)
                    
                    if name:
                        tools.append({
                            'name': name,
                            'description': description,
                            'website_url': url,
                            'source_website': 'aisitelist.com',
                            'category': 'AI Tools'
                        })
                except Exception as e:
                    continue
                    
        except Exception as e:
            logger.error(f"Error scraping aisitelist.com: {e}")
            
        return tools
    
    def save_tools_to_database(self, tools):
        """Save tools to database"""
        logger.info(f"Saving {len(tools)} tools to database...")
        
        for tool in tools:
            try:
                # Check if tool already exists
                self.cursor.execute(
                    "SELECT id FROM ai_tools WHERE name = ? AND source_website = ?",
                    (tool['name'], tool['source_website'])
                )
                
                if self.cursor.fetchone():
                    # Update existing tool
                    self.cursor.execute('''
                        UPDATE ai_tools SET 
                            description = ?, category = ?, website_url = ?, 
                            pricing_model = ?, free_tier = ?, last_updated = CURRENT_TIMESTAMP
                        WHERE name = ? AND source_website = ?
                    ''', (
                        tool.get('description', ''),
                        tool.get('category', ''),
                        tool.get('website_url', ''),
                        tool.get('pricing_model', ''),
                        tool.get('free_tier', False),
                        tool['name'],
                        tool['source_website']
                    ))
                else:
                    # Insert new tool
                    self.cursor.execute('''
                        INSERT INTO ai_tools (
                            name, description, category, website_url, pricing_model,
                            free_tier, source_website
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        tool['name'],
                        tool.get('description', ''),
                        tool.get('category', ''),
                        tool.get('website_url', ''),
                        tool.get('pricing_model', ''),
                        tool.get('free_tier', False),
                        tool['source_website']
                    ))
            except Exception as e:
                logger.error(f"Error saving tool {tool.get('name', 'Unknown')}: {e}")
                continue
        
        self.conn.commit()
        logger.info("Tools saved to database successfully!")
    
    def scrape_all_sources(self):
        """Scrape all sources and save to database"""
        logger.info("Starting comprehensive scraping of all sources...")
        
        all_tools = []
        
        # Scrape each source
        scrapers = [
            self.scrape_bestaito_com,
            self.scrape_bestaitools_com,
            self.scrape_10bestaitools_com,
            self.scrape_bestfreeaitools_io,
            self.scrape_best_ai_tools_org,
            self.scrape_aisitelist_com
        ]
        
        for scraper in scrapers:
            try:
                tools = scraper()
                all_tools.extend(tools)
                logger.info(f"Scraped {len(tools)} tools from {scraper.__name__}")
                time.sleep(2)  # Be respectful to servers
            except Exception as e:
                logger.error(f"Error in {scraper.__name__}: {e}")
                continue
        
        # Save all tools to database
        self.save_tools_to_database(all_tools)
        
        logger.info(f"Scraping completed! Total tools collected: {len(all_tools)}")
        return all_tools
    
    def export_to_csv(self, filename="ai_tools_database.csv"):
        """Export database to CSV"""
        query = "SELECT * FROM ai_tools"
        df = pd.read_sql_query(query, self.conn)
        df.to_csv(filename, index=False)
        logger.info(f"Database exported to {filename}")
    
    def get_statistics(self):
        """Get database statistics"""
        stats = {}
        
        # Total tools
        self.cursor.execute("SELECT COUNT(*) FROM ai_tools")
        stats['total_tools'] = self.cursor.fetchone()[0]
        
        # Tools by source
        self.cursor.execute("SELECT source_website, COUNT(*) FROM ai_tools GROUP BY source_website")
        stats['by_source'] = dict(self.cursor.fetchall())
        
        # Tools by category
        self.cursor.execute("SELECT category, COUNT(*) FROM ai_tools GROUP BY category")
        stats['by_category'] = dict(self.cursor.fetchall())
        
        # Free tools count
        self.cursor.execute("SELECT COUNT(*) FROM ai_tools WHERE free_tier = 1")
        stats['free_tools'] = self.cursor.fetchone()[0]
        
        return stats
    
    def close(self):
        """Close database connection"""
        self.conn.close()

if __name__ == "__main__":
    scraper = AIToolsScraper()
    
    try:
        # Scrape all sources
        all_tools = scraper.scrape_all_sources()
        
        # Export to CSV
        scraper.export_to_csv()
        
        # Print statistics
        stats = scraper.get_statistics()
        print("\n=== SCRAPING STATISTICS ===")
        print(f"Total tools: {stats['total_tools']}")
        print(f"Free tools: {stats['free_tools']}")
        print("\nTools by source:")
        for source, count in stats['by_source'].items():
            print(f"  {source}: {count}")
        print("\nTools by category:")
        for category, count in stats['by_category'].items():
            print(f"  {category}: {count}")
            
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
    finally:
        scraper.close()
