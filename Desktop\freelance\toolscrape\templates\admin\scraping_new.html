{% extends "admin/base.html" %}

{% block title %}🕷️ Scraping Management{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">🕷️ Scraping Management</h1>
    <a href="{{ url_for('admin_new_scraping_job') }}" class="btn btn-primary">+ New Scraping Job</a>
</div>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">Scraping Jobs</h3>
    </div>
    
    {% if jobs %}
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th>Job Details</th>
                    <th>Status</th>
                    <th>Results</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for job in jobs %}
                <tr>
                    <td>
                        <div class="job-info">
                            <div class="job-name">{{ job.job_name }}</div>
                            <div class="job-url">{{ job.target_url }}</div>
                        </div>
                    </td>
                    <td>
                        <span class="status-badge status-{{ job.status }}">
                            {{ job.status.title() }}
                        </span>
                    </td>
                    <td>
                        {% if job.tools_found %}
                            <span class="metric-value">{{ job.tools_found }}</span> tools found
                        {% else %}
                            <span class="text-muted">-</span>
                        {% endif %}
                        
                        {% if job.error_message %}
                            <div class="error-message">
                                {{ job.error_message[:50] }}{% if job.error_message|length > 50 %}...{% endif %}
                            </div>
                        {% endif %}
                    </td>
                    <td>
                        <div class="timestamp">{{ job.created_at }}</div>
                    </td>
                    <td>
                        {% if job.status == 'pending' %}
                        <a href="{{ url_for('admin_run_scraping_job', job_id=job.id) }}" class="btn btn-success btn-sm">
                            ▶️ Run Job
                        </a>
                        {% else %}
                        <span class="text-muted">{{ job.status.title() }}</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="empty-state">
        <div class="empty-state-icon">🕷️</div>
        <div class="empty-state-title">No Scraping Jobs Yet</div>
        <div class="empty-state-description">
            Create your first scraping job to automatically discover and add AI tools to your database.
        </div>
        <a href="{{ url_for('admin_new_scraping_job') }}" class="btn btn-primary">Create First Job</a>
    </div>
    {% endif %}
</div>

<style>
/* Additional styles specific to scraping page */
.job-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.job-name {
    font-weight: 600;
    color: #e2e8f0;
}

.job-url {
    font-size: 0.85rem;
    color: #94a3b8;
    font-family: 'Monaco', 'Consolas', monospace;
}

.error-message {
    color: #ef4444;
    font-size: 0.8rem;
    margin-top: 5px;
    padding: 4px 8px;
    background: rgba(239, 68, 68, 0.1);
    border-radius: 4px;
}

.metric-value {
    font-weight: 600;
    color: #3b82f6;
}

.timestamp {
    font-size: 0.9rem;
    color: #94a3b8;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

.status-running {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.status-completed {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.status-failed {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
    }
    
    .job-url {
        word-break: break-all;
    }
}
</style>
{% endblock %}
