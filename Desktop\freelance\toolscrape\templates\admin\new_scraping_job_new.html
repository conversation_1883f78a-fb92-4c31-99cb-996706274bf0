{% extends "admin/base.html" %}

{% block title %}🆕 New Scraping Job{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">🆕 New Scraping Job</h1>
    <p class="page-subtitle">Create a new web scraping job to automatically discover AI tools</p>
</div>

<div class="form-container">
    <form method="POST" class="scraping-form">
        <div class="form-group">
            <label for="job_name">Job Name *</label>
            <input type="text" id="job_name" name="job_name" required placeholder="e.g., Product Hunt Scrape">
            <div class="form-help">Give this scraping job a descriptive name</div>
        </div>
        
        <div class="form-group">
            <label for="target_url">Target URL *</label>
            <input type="url" id="target_url" name="target_url" required placeholder="https://example.com">
            <div class="form-help">The website URL to scrape for AI tools</div>
        </div>
        
        <div class="form-group">
            <label for="scraper_type">Scraper Type</label>
            <select id="scraper_type" name="scraper_type">
                <option value="general">General Website Scraper</option>
                <option value="producthunt">Product Hunt Specific</option>
                <option value="github">GitHub Repository</option>
                <option value="custom">Custom Scraper</option>
            </select>
            <div class="form-help">Choose the appropriate scraper for the target website</div>
        </div>
        
        <div class="form-group">
            <label for="description">Description</label>
            <textarea id="description" name="description" placeholder="Describe what this scraping job will do..." rows="3"></textarea>
            <div class="form-help">Optional description of the scraping job</div>
        </div>
        
        <div class="form-group">
            <label>Scraping Options</label>
            <div class="checkbox-group">
                <input type="checkbox" id="extract_images" name="extract_images" value="1">
                <label for="extract_images">Extract tool screenshots/images</label>
            </div>
            <div class="checkbox-group">
                <input type="checkbox" id="follow_links" name="follow_links" value="1">
                <label for="follow_links">Follow internal links (max 2 levels)</label>
            </div>
            <div class="checkbox-group">
                <input type="checkbox" id="schedule_recurring" name="schedule_recurring" value="1">
                <label for="schedule_recurring">Schedule as recurring job (weekly)</label>
            </div>
        </div>
        
        <div class="form-actions">
            <a href="{{ url_for('admin_scraping') }}" class="btn btn-secondary">Cancel</a>
            <button type="submit" class="btn btn-primary">🚀 Create Scraping Job</button>
        </div>
    </form>
</div>

<style>
/* Form specific styles */
.form-container {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.scraping-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
}

.checkbox-group input {
    transform: scale(1.2);
}

.checkbox-group label {
    margin: 0;
    color: #94a3b8;
    font-weight: 500;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 40px;
}

.form-help {
    font-size: 0.9rem;
    color: #94a3b8;
    margin-top: 5px;
}

@media (max-width: 768px) {
    .form-container {
        padding: 25px;
        margin: 0 15px;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}
