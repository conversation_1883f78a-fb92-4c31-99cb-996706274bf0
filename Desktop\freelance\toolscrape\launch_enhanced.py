#!/usr/bin/env python3
"""
Simple launcher for the enhanced web interface
"""

import sys
import os

try:
    print("🚀 Starting Enhanced AI Tools Web Interface...")
    print("=" * 50)
    
    # Import and run the enhanced web interface
    from web_interface_enhanced import app
    
    print("✅ Enhanced web interface loaded successfully!")
    print("📊 Starting Flask server...")
    print("🌐 Access the interface at: http://localhost:5000")
    print("=" * 50)
    
    # Start the Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)
    
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Please check if all dependencies are installed.")
    
except Exception as e:
    print(f"❌ Error starting web interface: {e}")
    import traceback
    traceback.print_exc()
    
finally:
    input("Press Enter to exit...")
