<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Import/Export - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
            color: #e2e8f0;
        }
        
        .admin-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px 0;
        }
        
        .header-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-logo {
            font-size: 1.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .admin-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .nav-link {
            color: #94a3b8;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }
        
        .logout-btn {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }
        
        .page-subtitle {
            color: #94a3b8;
            font-size: 1.1rem;
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 40px;
            margin-bottom: 50px;
        }
        
        .action-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(59, 130, 246, 0.3);
        }
        
        .action-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.8;
        }
        
        .action-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #e2e8f0;
            margin-bottom: 15px;
        }
        
        .action-description {
            color: #94a3b8;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .export-section {
            margin-bottom: 40px;
        }
        
        .export-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .export-option {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .export-option:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
        }
        
        .export-checkbox {
            margin-bottom: 10px;
        }
        
        .export-checkbox input {
            margin-right: 8px;
            transform: scale(1.2);
        }
        
        .field-selection {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .field-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .field-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            border-radius: 6px;
            transition: background 0.2s ease;
        }
        
        .field-checkbox:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        
        .field-checkbox input {
            transform: scale(1.1);
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #f59e0b, #d97706);
            color: white;
        }
        
        .btn-large {
            padding: 15px 30px;
            font-size: 1.1rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        
        .upload-area {
            border: 2px dashed rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover, .upload-area.dragover {
            border-color: #3b82f6;
            background: rgba(59, 130, 246, 0.05);
        }
        
        .upload-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0.6;
        }
        
        .upload-text {
            color: #94a3b8;
            margin-bottom: 20px;
        }
        
        .file-input {
            display: none;
        }
        
        .import-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .import-option {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #cbd5e1;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 16px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.05);
            color: #e2e8f0;
            font-size: 14px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
            display: none;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid;
        }
        
        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.3);
            color: #10b981;
        }
        
        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }
        
        @media (max-width: 768px) {
            .action-grid {
                grid-template-columns: 1fr;
            }
            
            .export-options, .import-options {
                grid-template-columns: 1fr;
            }
            
            .field-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="header-content">
            <div class="admin-logo">📊 AI Tools Admin</div>
            <nav class="admin-nav">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-link">Dashboard</a>
                <a href="{{ url_for('admin_tools') }}" class="nav-link">Tools</a>
                <a href="{{ url_for('admin_scraping') }}" class="nav-link">Scraping</a>
                <a href="{{ url_for('admin_publishing') }}" class="nav-link">Publishing</a>
                <a href="{{ url_for('admin_analytics') }}" class="nav-link">Analytics</a>
                <a href="{{ url_for('admin_logout') }}" class="logout-btn">Logout</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="page-header">
            <h1 class="page-title">Import & Export Data</h1>
            <p class="page-subtitle">Manage your AI tools database with powerful import and export capabilities</p>
        </div>

        <div class="action-grid">
            <!-- Export Section -->
            <div class="action-card">
                <div class="action-icon">📤</div>
                <h2 class="action-title">Export Data</h2>
                <p class="action-description">
                    Export your AI tools database in various formats including CSV, JSON, and Excel. 
                    Choose specific fields and apply filters to customize your export.
                </p>
                
                <div class="export-section">
                    <h3 style="margin-bottom: 20px;">Export Format</h3>
                    <div class="export-options">
                        <div class="export-option">
                            <div class="export-checkbox">
                                <input type="radio" name="export_format" value="csv" id="csv" checked>
                                <label for="csv">📄 CSV</label>
                            </div>
                            <small>Comma-separated values</small>
                        </div>
                        <div class="export-option">
                            <div class="export-checkbox">
                                <input type="radio" name="export_format" value="json" id="json">
                                <label for="json">📋 JSON</label>
                            </div>
                            <small>JavaScript Object Notation</small>
                        </div>
                        <div class="export-option">
                            <div class="export-checkbox">
                                <input type="radio" name="export_format" value="excel" id="excel">
                                <label for="excel">📊 Excel</label>
                            </div>
                            <small>Microsoft Excel format</small>
                        </div>
                    </div>
                    
                    <div class="field-selection">
                        <h4>Select Fields to Export</h4>
                        <div class="field-grid">
                            <label class="field-checkbox">
                                <input type="checkbox" name="fields" value="id" checked>
                                ID
                            </label>
                            <label class="field-checkbox">
                                <input type="checkbox" name="fields" value="name" checked>
                                Name
                            </label>
                            <label class="field-checkbox">
                                <input type="checkbox" name="fields" value="description" checked>
                                Description
                            </label>
                            <label class="field-checkbox">
                                <input type="checkbox" name="fields" value="category" checked>
                                Category
                            </label>
                            <label class="field-checkbox">
                                <input type="checkbox" name="fields" value="website_url" checked>
                                Website URL
                            </label>
                            <label class="field-checkbox">
                                <input type="checkbox" name="fields" value="pricing_model">
                                Pricing Model
                            </label>
                            <label class="field-checkbox">
                                <input type="checkbox" name="fields" value="free_tier">
                                Free Tier
                            </label>
                            <label class="field-checkbox">
                                <input type="checkbox" name="fields" value="rating">
                                Rating
                            </label>
                            <label class="field-checkbox">
                                <input type="checkbox" name="fields" value="created_at">
                                Created Date
                            </label>
                            <label class="field-checkbox">
                                <input type="checkbox" name="fields" value="is_verified">
                                Verified Status
                            </label>
                        </div>
                    </div>
                    
                    <button class="btn btn-primary btn-large" onclick="exportData()">
                        📤 Export Data
                    </button>
                </div>
            </div>

            <!-- Import Section -->
            <div class="action-card">
                <div class="action-icon">📥</div>
                <h2 class="action-title">Import Data</h2>
                <p class="action-description">
                    Import AI tools data from CSV or JSON files. Supports batch imports and 
                    duplicate detection to maintain data integrity.
                </p>
                
                <form method="POST" action="{{ url_for('admin_import_data') }}" enctype="multipart/form-data" id="importForm">
                    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                        <div class="upload-icon">📁</div>
                        <div class="upload-text">
                            <strong>Click to browse</strong> or drag and drop your file here
                        </div>
                        <p>Supports CSV and JSON formats (max 16MB)</p>
                        <input type="file" id="fileInput" name="file" class="file-input" accept=".csv,.json" onchange="handleFileSelect(this)">
                    </div>
                    
                    <div class="progress-bar" id="progressBar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    
                    <div class="import-options">
                        <div class="import-option">
                            <div class="form-group">
                                <label class="form-label">Import Mode</label>
                                <select name="import_mode" class="form-control">
                                    <option value="add_new">Add New Only</option>
                                    <option value="update_existing">Update Existing</option>
                                    <option value="add_and_update">Add New & Update</option>
                                </select>
                            </div>
                        </div>
                        <div class="import-option">
                            <div class="form-group">
                                <label class="form-label">Duplicate Handling</label>
                                <select name="duplicate_handling" class="form-control">
                                    <option value="skip">Skip Duplicates</option>
                                    <option value="replace">Replace Duplicates</option>
                                    <option value="merge">Merge Data</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-success btn-large" id="importBtn" disabled>
                        📥 Import Data
                    </button>
                </form>
            </div>
        </div>

        <!-- Quick Actions -->
        <div style="text-align: center; margin-top: 50px;">
            <h3 style="margin-bottom: 30px; color: #e2e8f0;">Quick Export Options</h3>
            <div style="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap;">
                <a href="{{ url_for('admin_export_data', format='csv') }}" class="btn btn-primary">
                    📄 Full CSV Export
                </a>
                <a href="{{ url_for('admin_export_data', format='json') }}" class="btn btn-primary">
                    📋 Full JSON Export
                </a>
                <a href="{{ url_for('admin_export_data', format='excel') }}" class="btn btn-primary">
                    📊 Full Excel Export
                </a>
            </div>
        </div>
    </div>

    <script>
        function exportData() {
            const format = document.querySelector('input[name="export_format"]:checked').value;
            const selectedFields = Array.from(document.querySelectorAll('input[name="fields"]:checked'))
                .map(input => input.value);
            
            if (selectedFields.length === 0) {
                alert('Please select at least one field to export');
                return;
            }
            
            const params = new URLSearchParams({
                format: format,
                fields: selectedFields
            });
            
            window.open(`{{ url_for('admin_export_data') }}?${params}`, '_blank');
        }
        
        function handleFileSelect(input) {
            const file = input.files[0];
            const importBtn = document.getElementById('importBtn');
            
            if (file) {
                const uploadText = document.querySelector('.upload-text');
                uploadText.innerHTML = `<strong>${file.name}</strong> (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
                importBtn.disabled = false;
            } else {
                importBtn.disabled = true;
            }
        }
        
        // Drag and drop functionality
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('fileInput').files = files;
                handleFileSelect(document.getElementById('fileInput'));
            }
        });
        
        // Form submission with progress
        document.getElementById('importForm').addEventListener('submit', function(e) {
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            
            progressBar.style.display = 'block';
            
            // Simulate progress (in real implementation, you'd track actual upload progress)
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 30;
                if (progress > 90) progress = 90;
                progressFill.style.width = progress + '%';
            }, 200);
            
            // Clean up on form completion (you'd handle this with actual upload events)
            setTimeout(() => {
                clearInterval(interval);
                progressFill.style.width = '100%';
            }, 3000);
        });
        
        // Field selection helpers
        document.addEventListener('DOMContentLoaded', function() {
            // Add "Select All" functionality
            const fieldGrid = document.querySelector('.field-grid');
            const selectAllBtn = document.createElement('button');
            selectAllBtn.textContent = 'Select All';
            selectAllBtn.className = 'btn btn-secondary';
            selectAllBtn.type = 'button';
            selectAllBtn.style.marginBottom = '15px';
            
            selectAllBtn.addEventListener('click', function() {
                const checkboxes = fieldGrid.querySelectorAll('input[type="checkbox"]');
                const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                checkboxes.forEach(cb => cb.checked = !allChecked);
                selectAllBtn.textContent = allChecked ? 'Select All' : 'Deselect All';
            });
            
            fieldGrid.parentNode.insertBefore(selectAllBtn, fieldGrid);
        });
    </script>
</body>
</html>
