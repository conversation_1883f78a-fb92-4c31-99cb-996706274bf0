# AI Tools Scraper - Comprehensive Database Builder

A comprehensive Python-based web scraping system that collects AI tools information from multiple sources and creates a searchable database.

## 🚀 Features

- **Multi-Source Scraping**: Scrapes AI tools from 8+ popular websites
- **GitHub Integration**: Extracts tools from awesome-ai-tools repository
- **HuggingFace Models**: Collects top AI models from HuggingFace
- **SQLite Database**: Stores all data in a structured database
- **Web Interface**: Beautiful Flask-based web interface to browse tools
- **Multiple Export Formats**: CSV, JSON exports available
- **Comprehensive Analytics**: Detailed reports and statistics

## 📊 Data Sources

### Web Sources
- ✅ **bestaito.com** - Free AI tools
- ✅ **bestaitools.com** - Curated AI tools
- ✅ **10bestaitools.com** - Top AI tools
- ✅ **bestfreeaitools.io** - Free AI tools
- ✅ **best-ai-tools.org** - Best AI tools
- ✅ **aisitelist.com** - AI site listings
- ✅ **futuretools.io** - Future AI tools
- ✅ **opentools.ai** - Open AI tools

### Repository Sources
- ✅ **GitHub**: mahseema/awesome-ai-tools (315+ tools)

### AI Model Sources
- ✅ **HuggingFace**: Top models (DeepSeek-R1, Llama-3.1, FLUX.1, etc.)

## 🛠️ Installation

1. **Clone or download the project files**
2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

## 🚀 Usage

### Quick Start
Run the complete scraping system:
```bash
python run_scraper.py
```

This will:
1. Scrape all AI tools websites
2. Extract tools from GitHub repositories
3. Collect HuggingFace model data
4. Generate comprehensive reports
5. Export data in multiple formats

### Individual Components

**Run main scraper only**:
```bash
python ai_tools_scraper.py
```

**Run extended scraper (GitHub + HuggingFace)**:
```bash
python extended_scraper.py
```

**Analyze existing database**:
```bash
python database_analyzer.py
```

**Launch web interface**:
```bash
python web_interface.py
```
Then visit: http://localhost:5000

## 📁 Output Files

After running the scraper, you'll get:

- **`ai_tools_database.db`** - SQLite database with all data
- **`ai_tools_YYYYMMDD_HHMMSS.csv`** - CSV export of tools
- **`github_repos_YYYYMMDD_HHMMSS.csv`** - GitHub repositories data
- **`huggingface_models_YYYYMMDD_HHMMSS.csv`** - HuggingFace models data
- **`ai_tools_database_YYYYMMDD_HHMMSS.json`** - JSON export of all data
- **`ai_tools_report_YYYYMMDD_HHMMSS.txt`** - Comprehensive analysis report
- **`scraping.log`** - Detailed scraping logs

## 🗃️ Database Schema

### Main Tables

**ai_tools**
- `id` - Primary key
- `name` - Tool name
- `description` - Tool description
- `category` - Tool category
- `website_url` - Official website
- `pricing_model` - Pricing information
- `free_tier` - Has free tier (boolean)
- `open_source` - Is open source (boolean)
- `source_website` - Where it was scraped from
- `scraped_date` - When it was added

**github_repos**
- Repository information (stars, forks, language, etc.)

**huggingface_models**
- Model information (parameters, downloads, likes, etc.)

## 🌐 Web Interface

The included web interface provides:

- **Dashboard**: Overview statistics and recent additions
- **Browse Tools**: Searchable and filterable tool listings
- **Export**: Direct CSV download
- **API Endpoints**: JSON data access

### API Endpoints
- `GET /api/tools` - All tools as JSON
- `GET /api/stats` - Database statistics
- `GET /export/csv` - Download CSV export

## ⚙️ Configuration

### Customizing Sources
Edit the source URLs in `source.md` or modify the scraper methods in:
- `ai_tools_scraper.py` - Main website scrapers
- `extended_scraper.py` - GitHub and HuggingFace scrapers

### Rate Limiting
The scrapers include built-in delays to be respectful to target websites. Modify sleep times in the code if needed.

### Browser Configuration
Chrome options are configured for headless operation. Modify `chrome_options` in the scraper classes for different browsers or settings.

## 📈 Sample Statistics

After a complete scrape, you might see results like:

```
Total AI Tools: 500+
GitHub Repositories: 5+
HuggingFace Models: 8+
Free Tools: 300+
Open Source Tools: 150+

Top Sources:
- bestaito.com: 120 tools
- mahseema/awesome-ai-tools: 85 tools
- bestaitools.com: 75 tools
- futuretools.io: 60 tools
```

## 🔧 Troubleshooting

### Common Issues

**ChromeDriver errors**:
- The system automatically downloads ChromeDriver
- Ensure Chrome browser is installed
- Check firewall settings

**Website blocking**:
- Some sites may rate-limit or block automated requests
- The scraper includes random delays and user-agent rotation
- Consider using VPN if needed

**Database locked**:
- Close any open database connections
- Delete `ai_tools_database.db` to start fresh

### Error Logs
Check `scraping.log` for detailed error information and debugging.

## 🚀 Advanced Usage

### Custom Scrapers
Add new sources by:
1. Creating a new method in `AIToolsScraper` class
2. Following the existing pattern for data extraction
3. Adding the method to `scrape_all_sources()`

### Database Queries
Direct SQLite queries on `ai_tools_database.db`:
```sql
-- Find free AI tools in specific category
SELECT * FROM ai_tools 
WHERE free_tier = 1 AND category LIKE '%Image%';

-- Tools by popularity (if available)
SELECT source_website, COUNT(*) as tool_count 
FROM ai_tools 
GROUP BY source_website 
ORDER BY tool_count DESC;
```

## 📝 License

This project is for educational and research purposes. Please respect the terms of service of the websites being scraped and use responsibly.

## 🤝 Contributing

To contribute:
1. Add new scraping sources
2. Improve data extraction accuracy
3. Enhance the web interface
4. Add new export formats
5. Improve error handling

## 📞 Support

For issues or questions:
1. Check the error logs in `scraping.log`
2. Review the troubleshooting section
3. Ensure all dependencies are installed correctly

---

**Happy Scraping!** 🤖✨
