# 🚀 AI Tools Enhanced Admin Dashboard

A comprehensive, modern admin interface for managing the AI Tools database with advanced features including CRUD operations, web scraping, publishing workflows, analytics, and more.

## ✨ Features

### 🔧 **Tools Management**
- **CRUD Operations**: Add, edit, delete, and view AI tools
- **Bulk Operations**: Select multiple tools for batch actions
- **Advanced Filtering**: Filter by category, pricing, verification status
- **Smart Search**: Search across names, descriptions, and categories
- **Sorting**: Sort by various fields (name, date, rating, popularity)

### 🕷️ **Web Scraping System**
- **Job Management**: Create and monitor scraping jobs
- **Target Websites**: Scrape multiple AI tool directories
- **Auto-Detection**: Smart detection of tool information
- **Progress Tracking**: Real-time job status and results

### 📢 **Publishing Workflow**
- **Approval System**: Tools go through approval before publishing
- **Queue Management**: Manage pending, approved, and published tools
- **Verification**: Mark tools as verified after review
- **Rejection Handling**: Provide reasons for tool rejections

### 📊 **Analytics & Reporting**
- **Interactive Charts**: Category distribution, growth trends
- **Data Visualization**: Daily additions, pricing models
- **Top Sources**: Track which websites provide the most tools
- **Export Reports**: Generate analytics reports

### 📥📤 **Import/Export System**
- **Multiple Formats**: CSV, JSON, Excel support
- **Field Selection**: Choose which fields to export
- **Bulk Import**: Import tools from external sources
- **Duplicate Handling**: Smart detection and handling of duplicates

### 🔔 **Notifications System**
- **Real-time Alerts**: Get notified of important events
- **Activity Tracking**: Monitor tool modifications and user actions
- **Admin Messages**: System-wide notifications and announcements

### 👥 **User Management**
- **Role-based Access**: Different permission levels
- **Session Management**: Secure login/logout
- **Activity Logging**: Track all user actions

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- SQLite database with AI tools data
- Required Python packages (automatically installed)

### Installation & Setup

1. **Navigate to the project directory:**
   ```bash
   cd C:\Users\<USER>\Desktop\freelance\toolscrape
   ```

2. **Run the setup (automatic):**
   
   **Option A: Windows Batch File**
   ```cmd
   run_admin_dashboard.bat
   ```
   
   **Option B: Python Launcher**
   ```bash
   python launch_admin_dashboard.py
   ```

3. **Access the dashboard:**
   - Open your browser to: `http://localhost:5001/admin/login`
   - Login with default credentials:
     - **Username**: `admin`
     - **Password**: `admin123`

## 🔐 Default Login

- **Username**: `admin`
- **Password**: `admin123`

> ⚠️ **Security Note**: Change the default password in production!

## 📊 Dashboard Sections

### 🏠 **Main Dashboard**
- Overview statistics (total tools, categories, verified tools)
- Recent activity feed
- Quick access to all major functions
- Real-time notifications
- Category breakdown with charts

### 🔧 **Tools Management** (`/admin/tools`)
- Complete tools database with pagination
- Advanced filtering and search
- Bulk operations (delete, verify, publish, categorize)
- Individual tool editing and management
- Export filtered results

### 🕷️ **Scraping Management** (`/admin/scraping`)
- Create new scraping jobs
- Monitor job progress and results
- View scraping history
- Manage target websites
- Auto-run capabilities

### 📢 **Publishing Queue** (`/admin/publishing`)
- Review tools pending publication
- Approve or reject submissions
- Batch publishing operations
- Publication history tracking
- Rejection reason management

### 📊 **Analytics** (`/admin/analytics`)
- Interactive charts and graphs
- Growth trends and patterns
- Category and pricing analysis
- Source website statistics
- Exportable reports

### 📥📤 **Import/Export** (`/admin/import_export`)
- Drag-and-drop file uploads
- Multiple export formats
- Field selection for exports
- Import validation and error handling
- Bulk data operations

## 🛠️ Advanced Features

### Bulk Operations
- Select multiple tools using checkboxes
- Apply actions to selected tools:
  - Delete multiple tools
  - Mark as verified
  - Add to publishing queue
  - Update categories
  - Export selection

### Real-time Features
- Live notification updates
- Auto-refresh job status
- Progressive data loading
- Background task monitoring

### Data Management
- Automatic backup creation
- Data validation and sanitization
- Duplicate detection and merging
- Relationship integrity checks

## 📁 File Structure

```
toolscrape/
├── admin_dashboard.py              # Main admin backend
├── launch_admin_dashboard.py       # Enhanced launcher
├── run_admin_dashboard.bat        # Windows batch launcher
├── templates/admin/               # Admin HTML templates
│   ├── login.html                # Login page
│   ├── dashboard.html            # Main dashboard
│   ├── tools.html               # Tools management
│   ├── scraping.html            # Scraping management
│   ├── publishing.html          # Publishing workflow
│   ├── analytics.html           # Analytics dashboard
│   ├── import_export.html       # Import/Export interface
│   ├── notifications.html       # Notifications center
│   ├── add_tool.html           # Add new tool
│   └── edit_tool.html          # Edit existing tool
├── ai_tools_master.db            # Main database
└── requirements.txt              # Python dependencies
```

## 🔧 Configuration

### Database Configuration
The admin dashboard automatically connects to `ai_tools_master.db`. Make sure this file exists and contains the `ai_tools` table with the following structure:

```sql
CREATE TABLE ai_tools (
    id INTEGER PRIMARY KEY,
    name TEXT,
    description TEXT,
    website_url TEXT,
    category TEXT,
    subcategory TEXT,
    pricing_model TEXT,
    free_tier BOOLEAN,
    trial_available BOOLEAN,
    api_available BOOLEAN,
    rating REAL,
    features TEXT,
    tags TEXT,
    use_cases TEXT,
    source_website TEXT,
    created_at TIMESTAMP,
    is_verified BOOLEAN,
    popularity_score INTEGER
);
```

### Environment Variables
Set these environment variables for production:

```bash
FLASK_ENV=production
SECRET_KEY=your-secret-key-here
DATABASE_URL=path/to/your/database.db
```

## 🚀 Production Deployment

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set production environment:**
   ```bash
   export FLASK_ENV=production
   ```

3. **Run with WSGI server:**
   ```bash
   gunicorn -w 4 -b 0.0.0.0:5001 admin_dashboard:app
   ```

## 🔍 Troubleshooting

### Common Issues

**Database Connection Error:**
- Ensure `ai_tools_master.db` exists in the current directory
- Check database file permissions
- Verify SQLite installation

**Missing Dependencies:**
- Run: `pip install -r requirements.txt`
- For Excel support: `pip install openpyxl xlsxwriter`

**Port Already in Use:**
- Change port in `admin_dashboard.py`: `app.run(port=5002)`
- Or kill existing processes using port 5001

**Template Not Found:**
- Ensure `templates/admin/` directory exists
- Check template file permissions
- Verify Flask template path configuration

## 📈 Performance Tips

- **Database Optimization**: Regular VACUUM and ANALYZE operations
- **Pagination**: Large datasets are automatically paginated
- **Caching**: Browser caching for static assets
- **Background Jobs**: Long-running tasks use background processing

## 🔒 Security Features

- **Session Management**: Secure session handling
- **Input Validation**: All inputs are validated and sanitized
- **SQL Injection Protection**: Parameterized queries
- **XSS Prevention**: Template auto-escaping
- **CSRF Protection**: Built-in CSRF tokens

## 📝 API Endpoints

### Admin API Routes
- `GET /admin/api/tools` - Get all tools data
- `GET /admin/api/stats` - Get dashboard statistics
- `POST /admin/tools/bulk` - Bulk operations on tools
- `GET /admin/export_data` - Export data in various formats

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is proprietary software. All rights reserved.

## 📞 Support

For support and questions:
- Check the troubleshooting section above
- Review the console output for error messages
- Ensure all dependencies are installed correctly

---

**🎉 Enjoy your enhanced AI Tools Admin Dashboard!**

*Built with ❤️ using Flask, SQLite, and modern web technologies.*
