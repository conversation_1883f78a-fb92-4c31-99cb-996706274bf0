#!/usr/bin/env python3
"""
Quick test of the enhanced web interface
"""

try:
    print("Testing enhanced web interface...")
    
    # Test imports
    from web_interface_enhanced import app, get_db_connection
    print("✅ Imports successful")
    
    # Test database connection
    conn = get_db_connection()
    total = conn.execute('SELECT COUNT(*) FROM ai_tools').fetchone()[0]
    print(f"✅ Database connection: {total} tools found")
    conn.close()
    
    print("🎉 Enhanced web interface is ready!")
    print("🚀 Run: .\\run_enhanced.bat to start")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
