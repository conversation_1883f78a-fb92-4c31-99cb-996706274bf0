#!/usr/bin/env python3
"""
Minimal Admin Dashboard Test
Isolates issues with a basic Flask app
"""

from flask import Flask, render_template, redirect, url_for, session, request, flash
import sqlite3
import os

app = Flask(__name__)
app.secret_key = 'test_key_123'

def get_db_connection():
    """Simple database connection"""
    try:
        conn = sqlite3.connect('ai_tools_master.db')
        conn.row_factory = sqlite3.Row
        return conn
    except Exception as e:
        print(f"Database connection error: {e}")
        return None

@app.route('/')
def index():
    """Root route"""
    return redirect(url_for('admin_login'))

@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    """Simple admin login"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if username == 'admin' and password == 'admin123':
            session['admin_logged_in'] = True
            session['admin_username'] = username
            flash('Login successful!', 'success')
            return redirect(url_for('admin_dashboard'))
        else:
            flash('Invalid credentials', 'error')
    
    return render_template('admin/login.html')

@app.route('/admin')
@app.route('/admin/')
def admin_dashboard():
    """Main admin dashboard"""
    if 'admin_logged_in' not in session:
        return redirect(url_for('admin_login'))
    
    # Get basic stats
    conn = get_db_connection()
    stats = {
        'total_tools': 0,
        'total_jobs': 0,
        'total_categories': 0
    }
    
    if conn:
        try:
            # Try to get tool count
            result = conn.execute('SELECT COUNT(*) as count FROM ai_tools').fetchone()
            stats['total_tools'] = result['count'] if result else 0
        except:
            # Table might not exist
            pass
        
        try:
            # Try to get categories
            result = conn.execute('SELECT COUNT(DISTINCT category) as count FROM ai_tools').fetchone()
            stats['total_categories'] = result['count'] if result else 0
        except:
            pass
        
        conn.close()
    
    return render_template('admin/dashboard.html', 
                         total_tools=stats['total_tools'],
                         total_categories=stats['total_categories'],
                         recent_jobs=[], 
                         recent_modifications=[],
                         top_categories=[])

@app.route('/admin/api-scraping')
def admin_api_scraping():
    """API scraping page"""
    if 'admin_logged_in' not in session:
        return redirect(url_for('admin_login'))
    
    return render_template('admin/api_scraping.html', 
                         configs=[], 
                         recent_results=[])

@app.route('/admin/logout')
def admin_logout():
    """Logout"""
    session.clear()
    flash('Logged out successfully!', 'success')
    return redirect(url_for('admin_login'))

if __name__ == '__main__':
    print("🧪 Starting Minimal Admin Dashboard Test...")
    print("📍 Working directory:", os.getcwd())
    print("🌐 URL: http://localhost:5001/admin/login")
    print("🔐 Credentials: admin / admin123")
    
    try:
        app.run(host='127.0.0.1', port=5001, debug=True)
    except Exception as e:
        print(f"❌ Error starting test app: {e}")
        import traceback
        traceback.print_exc()
