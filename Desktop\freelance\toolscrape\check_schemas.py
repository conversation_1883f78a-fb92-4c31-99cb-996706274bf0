import sqlite3

def check_database_schema(db_name):
    print(f"\n=== {db_name} ===")
    try:
        conn = sqlite3.connect(db_name)
        cursor = conn.cursor()
        
        # Get table names
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"Tables: {[t[0] for t in tables]}")
        
        # Check ai_tools table structure
        if any('ai_tools' in str(t) for t in tables):
            cursor.execute("PRAGMA table_info(ai_tools)")
            columns = cursor.fetchall()
            print("ai_tools columns:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
                
            # Count records
            cursor.execute("SELECT COUNT(*) FROM ai_tools")
            count = cursor.fetchone()[0]
            print(f"Records: {count}")
        
        conn.close()
    except Exception as e:
        print(f"Error: {e}")

# Check both databases
check_database_schema('ai_tools_database.db')
check_database_schema('ai_tools_master.db')
