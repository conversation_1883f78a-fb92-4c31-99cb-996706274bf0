<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📢 Publishing Management - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
            color: #e2e8f0;
        }
        
        .admin-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px 0;
        }
        
        .header-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-logo {
            font-size: 1.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .admin-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .nav-link {
            color: #94a3b8;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }
        
        .logout-btn {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
        }
        
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .status-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 5px;
        }
        
        .status-tab {
            padding: 12px 20px;
            border-radius: 8px;
            text-decoration: none;
            color: #94a3b8;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-tab.active {
            background: #3b82f6;
            color: white;
        }
        
        .status-count {
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .publications-grid {
            display: grid;
            gap: 20px;
        }
        
        .publication-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        
        .publication-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        
        .publication-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .publication-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #e2e8f0;
            margin-bottom: 5px;
        }
        
        .publication-meta {
            color: #94a3b8;
            font-size: 0.9rem;
        }
        
        .publication-status {
            margin-left: auto;
        }
        
        .badge {
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .badge-pending {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
        }
        
        .badge-approved {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
        }
        
        .badge-published {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }
        
        .badge-rejected {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }
        
        .publication-description {
            color: #cbd5e1;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .publication-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            border: none;
            cursor: pointer;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #cbd5e1;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .publication-url {
            color: #3b82f6;
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .publication-url:hover {
            text-decoration: underline;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #94a3b8;
        }
        
        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        .reject-form {
            margin-top: 10px;
            display: none;
        }
        
        .reject-form.show {
            display: block;
        }
        
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.05);
            color: #e2e8f0;
            margin-bottom: 10px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        @media (max-width: 768px) {
            .status-tabs {
                flex-direction: column;
                gap: 5px;
            }
            
            .publication-header {
                flex-direction: column;
                gap: 10px;
            }
            
            .publication-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="header-content">
            <div class="admin-logo">📢 AI Tools Admin</div>
            <nav class="admin-nav">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-link">Dashboard</a>
                <a href="{{ url_for('admin_tools') }}" class="nav-link">Tools</a>
                <a href="{{ url_for('admin_scraping') }}" class="nav-link">Scraping</a>
                <a href="{{ url_for('admin_publishing') }}" class="nav-link active">Publishing</a>
                <a href="{{ url_for('admin_analytics') }}" class="nav-link">Analytics</a>
                <a href="{{ url_for('admin_logout') }}" class="logout-btn">Logout</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="page-header">
            <h1 class="page-title">Publishing Management</h1>
        </div>

        <!-- Status Filter Tabs -->
        <div class="status-tabs">
            <a href="{{ url_for('admin_publishing') }}" class="status-tab {% if not current_status %}active{% endif %}">
                All Publications
                <span class="status-count">{{ status_counts.pending + status_counts.approved + status_counts.published + status_counts.rejected }}</span>
            </a>
            <a href="{{ url_for('admin_publishing', status='pending') }}" class="status-tab {% if current_status == 'pending' %}active{% endif %}">
                Pending
                <span class="status-count">{{ status_counts.pending }}</span>
            </a>
            <a href="{{ url_for('admin_publishing', status='approved') }}" class="status-tab {% if current_status == 'approved' %}active{% endif %}">
                Approved
                <span class="status-count">{{ status_counts.approved }}</span>
            </a>
            <a href="{{ url_for('admin_publishing', status='published') }}" class="status-tab {% if current_status == 'published' %}active{% endif %}">
                Published
                <span class="status-count">{{ status_counts.published }}</span>
            </a>
            <a href="{{ url_for('admin_publishing', status='rejected') }}" class="status-tab {% if current_status == 'rejected' %}active{% endif %}">
                Rejected
                <span class="status-count">{{ status_counts.rejected }}</span>
            </a>
        </div>

        <!-- Publications Grid -->
        {% if publications %}
        <div class="publications-grid">
            {% for pub in publications %}
            <div class="publication-card">
                <div class="publication-header">
                    <div>
                        <div class="publication-title">{{ pub.tool_name }}</div>
                        <div class="publication-meta">
                            Category: {{ pub.category or 'Uncategorized' }} • 
                            Requested by: {{ pub.requested_by }} • 
                            {{ pub.created_at }}
                        </div>
                    </div>
                    <div class="publication-status">
                        <span class="badge badge-{{ pub.status }}">{{ pub.status.title() }}</span>
                    </div>
                </div>
                
                {% if pub.description %}
                <div class="publication-description">
                    {{ pub.description[:200] }}{% if pub.description|length > 200 %}...{% endif %}
                </div>
                {% endif %}
                
                {% if pub.website_url %}
                <div style="margin-bottom: 15px;">
                    <a href="{{ pub.website_url }}" target="_blank" class="publication-url">🔗 {{ pub.website_url }}</a>
                </div>
                {% endif %}
                
                {% if pub.status == 'pending' %}
                <div class="publication-actions">
                    <a href="{{ url_for('admin_publishing_action', publication_id=pub.id, action='approve') }}" 
                       class="btn btn-success"
                       onclick="return confirm('Approve this tool for publication?')">
                        ✅ Approve
                    </a>
                    <button class="btn btn-danger" onclick="showRejectForm({{ pub.id }})">
                        ❌ Reject
                    </button>
                </div>
                
                <div class="reject-form" id="rejectForm{{ pub.id }}">
                    <form method="GET" action="{{ url_for('admin_publishing_action', publication_id=pub.id, action='reject') }}">
                        <textarea name="reason" class="form-control" placeholder="Rejection reason..." required></textarea>
                        <button type="submit" class="btn btn-danger btn-sm">Confirm Rejection</button>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="hideRejectForm({{ pub.id }})">Cancel</button>
                    </form>
                </div>
                {% elif pub.status == 'approved' %}
                <div class="publication-actions">
                    <a href="{{ url_for('admin_publishing_action', publication_id=pub.id, action='publish') }}" 
                       class="btn btn-primary"
                       onclick="return confirm('Publish this tool to the public database?')">
                        🚀 Publish Now
                    </a>
                </div>
                {% elif pub.status == 'published' %}
                <div class="publication-actions">
                    <span style="color: #10b981;">✅ Published {{ pub.published_at }}</span>
                </div>
                {% elif pub.status == 'rejected' %}
                <div class="publication-actions">
                    <span style="color: #ef4444;">❌ Rejected</span>
                    {% if pub.rejection_reason %}
                    <div style="margin-top: 10px; padding: 10px; background: rgba(239, 68, 68, 0.1); border-radius: 6px; color: #fca5a5;">
                        Reason: {{ pub.rejection_reason }}
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <div class="empty-state-icon">📭</div>
            <h3>No Publications Found</h3>
            <p>{% if current_status %}No {{ current_status }} publications at the moment.{% else %}No publications in the queue yet.{% endif %}</p>
        </div>
        {% endif %}
    </div>

    <script>
        function showRejectForm(id) {
            document.getElementById('rejectForm' + id).classList.add('show');
        }
        
        function hideRejectForm(id) {
            document.getElementById('rejectForm' + id).classList.remove('show');
        }
    </script>
</body>
</html>
