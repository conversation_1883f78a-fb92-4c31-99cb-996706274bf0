"""
Comprehensive AI Tools Scraper - Massive Collection Strategy
This scraper is designed to collect 5000+ tools by using:
1. Multiple entry points per site
2. Advanced pagination detection
3. Category-specific scraping
4. Better element detection
5. Parallel processing where appropriate
"""

import asyncio
import aiohttp
import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import sqlite3
import time
import re
import json
import logging
from fake_useragent import UserAgent
from urllib.parse import urljoin, urlparse
from tqdm import tqdm
import pandas as pd

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MassiveAIToolsScraper:
    def __init__(self):
        self.ua = UserAgent()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none'
        })
        
        self.conn = sqlite3.connect('ai_tools_database.db')
        self.cursor = self.conn.cursor()
        
        # Chrome options for maximum compatibility
        self.chrome_options = Options()
        self.chrome_options.add_argument('--headless')
        self.chrome_options.add_argument('--no-sandbox')
        self.chrome_options.add_argument('--disable-dev-shm-usage')
        self.chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        self.chrome_options.add_argument('--disable-extensions')
        self.chrome_options.add_argument('--disable-plugins')
        self.chrome_options.add_argument('--disable-images')
        self.chrome_options.add_argument('--disable-javascript')
        self.chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        self.chrome_options.add_experimental_option('useAutomationExtension', False)
        self.chrome_options.add_argument(f'--user-agent={self.ua.random}')
    
    def get_driver(self):
        """Get webdriver instance"""
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=self.chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            logger.error(f"Error creating driver: {e}")
            return None
    
    def clean_text(self, text):
        """Clean text content"""
        if not text:
            return ""
        text = re.sub(r'\s+', ' ', text.strip())
        text = re.sub(r'[^\w\s\-\.\,\!\?\:\;\(\)\[\]\/\&\%\$\@\#]', '', text)
        return text[:500]
    
    def scrape_comprehensive_bestaito(self):
        """Comprehensive scraping of bestaito.com"""
        logger.info("🔥 MASSIVE SCRAPING: bestaito.com")
        tools = []
        
        # All possible entry points and categories
        urls_to_scrape = [
            # Main pages
            "https://bestaito.com/tool/",
            "https://bestaito.com/tools/",
            "https://bestaito.com/ai-tools/",
            
            # Pricing filters
            "https://bestaito.com/tool/?pricing=free",
            "https://bestaito.com/tool/?pricing=premium", 
            "https://bestaito.com/tool/?pricing=freemium",
            "https://bestaito.com/tool/?pricing=paid",
            
            # Categories
            "https://bestaito.com/category/ai-writing-tools/",
            "https://bestaito.com/category/ai-image-generators/",
            "https://bestaito.com/category/ai-video-tools/",
            "https://bestaito.com/category/ai-audio-tools/",
            "https://bestaito.com/category/ai-code-tools/",
            "https://bestaito.com/category/ai-chatbots/",
            "https://bestaito.com/category/ai-design-tools/",
            "https://bestaito.com/category/ai-productivity-tools/",
            "https://bestaito.com/category/ai-marketing-tools/",
            "https://bestaito.com/category/ai-seo-tools/",
            "https://bestaito.com/category/ai-data-analysis/",
            "https://bestaito.com/category/ai-research-tools/",
            "https://bestaito.com/category/ai-education-tools/",
            "https://bestaito.com/category/ai-healthcare-tools/",
            "https://bestaito.com/category/ai-finance-tools/",
            "https://bestaito.com/category/ai-gaming-tools/",
            "https://bestaito.com/category/ai-sales-tools/",
            "https://bestaito.com/category/ai-customer-service/",
            
            # Paginated URLs (try many pages)
            *[f"https://bestaito.com/tool/?page={i}" for i in range(1, 21)],
            *[f"https://bestaito.com/tools/?page={i}" for i in range(1, 21)],
        ]
        
        for base_url in urls_to_scrape:
            try:
                logger.info(f"Scraping: {base_url}")
                response = self.session.get(base_url, timeout=30)
                
                if response.status_code != 200:
                    continue
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Multiple strategies to find tools
                tool_elements = []
                
                # Strategy 1: Look for common tool container patterns
                selectors = [
                    'div[class*="tool"]',
                    'div[class*="card"]',
                    'div[class*="item"]',
                    'article[class*="post"]',
                    'div[class*="entry"]',
                    'div[class*="listing"]',
                    'div[class*="product"]',
                    '.tool-card', '.ai-tool', '.tool-item',
                    '.grid-item', '.list-item', '.content-item'
                ]
                
                for selector in selectors:
                    elements = soup.select(selector)
                    if elements:
                        tool_elements.extend(elements)
                
                # Strategy 2: Find all divs with links that might be tools
                if not tool_elements:
                    all_divs = soup.find_all('div')
                    tool_elements = [div for div in all_divs if div.find('a', href=True) and len(div.get_text().strip()) > 20]
                
                # Strategy 3: Look for structured data
                json_ld = soup.find_all('script', type='application/ld+json')
                for script in json_ld:
                    try:
                        data = json.loads(script.string)
                        if isinstance(data, list):
                            for item in data:
                                if isinstance(item, dict) and 'name' in item:
                                    tools.append({
                                        'name': self.clean_text(item.get('name', '')),
                                        'description': self.clean_text(item.get('description', '')),
                                        'website_url': item.get('url', ''),
                                        'category': 'AI Tools',
                                        'source_website': 'bestaito.com',
                                        'source_page_url': base_url
                                    })
                    except:
                        pass
                
                # Extract tools from found elements
                for element in tool_elements[:200]:  # Increased limit
                    try:
                        # Extract name
                        name = ""
                        name_selectors = [
                            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                            '.title', '.name', '.tool-name', '.product-name',
                            '[class*="title"]', '[class*="name"]', '[class*="heading"]'
                        ]
                        
                        for sel in name_selectors:
                            name_elem = element.select_one(sel)
                            if name_elem and name_elem.get_text().strip():
                                name = self.clean_text(name_elem.get_text())
                                break
                        
                        # If no name found, try link text
                        if not name:
                            link = element.find('a')
                            if link and link.get_text().strip():
                                name = self.clean_text(link.get_text())
                        
                        # Extract description
                        description = ""
                        desc_selectors = [
                            'p', '.description', '.excerpt', '.summary', '.content',
                            '[class*="desc"]', '[class*="excerpt"]', '[class*="summary"]'
                        ]
                        
                        for sel in desc_selectors:
                            desc_elem = element.select_one(sel)
                            if desc_elem and desc_elem.get_text().strip():
                                description = self.clean_text(desc_elem.get_text())
                                break
                        
                        # Extract URL
                        website_url = ""
                        link = element.find('a', href=True)
                        if link:
                            href = link['href']
                            if href.startswith('http'):
                                website_url = href
                            elif href.startswith('/'):
                                website_url = urljoin("https://bestaito.com", href)
                        
                        # Extract category
                        category = "AI Tools"
                        cat_selectors = [
                            '.category', '.tag', '.badge', '[class*="cat"]', '[class*="tag"]'
                        ]
                        
                        for sel in cat_selectors:
                            cat_elem = element.select_one(sel)
                            if cat_elem and cat_elem.get_text().strip():
                                category = self.clean_text(cat_elem.get_text())
                                break
                        
                        # Extract pricing info
                        element_text = element.get_text().lower()
                        pricing = "Unknown"
                        if any(word in element_text for word in ['free', 'gratis', 'no cost']):
                            pricing = "Free"
                        elif any(word in element_text for word in ['premium', 'paid', 'subscription']):
                            pricing = "Premium"
                        elif 'freemium' in element_text:
                            pricing = "Freemium"
                        
                        # Only add if we have a meaningful name
                        if name and len(name) > 2 and not any(skip in name.lower() for skip in ['read more', 'click here', 'learn more', 'view all']):
                            tools.append({
                                'name': name,
                                'description': description,
                                'website_url': website_url,
                                'pricing_model': pricing,
                                'category': category,
                                'source_website': 'bestaito.com',
                                'source_page_url': base_url,
                                'free_tier': pricing == "Free" or pricing == "Freemium"
                            })
                            
                    except Exception as e:
                        continue
                
                logger.info(f"Found {len([t for t in tools if t['source_page_url'] == base_url])} tools on this page")
                time.sleep(1)  # Rate limiting
                
            except Exception as e:
                logger.error(f"Error scraping {base_url}: {e}")
                continue
        
        unique_tools = []
        seen_names = set()
        for tool in tools:
            if tool['name'] not in seen_names:
                unique_tools.append(tool)
                seen_names.add(tool['name'])
        
        logger.info(f"🎯 bestaito.com: {len(unique_tools)} unique tools found")
        return unique_tools
    
    def scrape_massive_futuretools(self):
        """Massive scraping of futuretools.io using multiple strategies"""
        logger.info("🔥 MASSIVE SCRAPING: futuretools.io")
        tools = []
        
        driver = self.get_driver()
        if not driver:
            return tools
        
        try:
            # Multiple entry points
            base_urls = [
                "https://www.futuretools.io/",
                "https://www.futuretools.io/tools",
                "https://www.futuretools.io/ai-tools",
                
                # Category pages
                "https://www.futuretools.io/tools?category=writing",
                "https://www.futuretools.io/tools?category=image",
                "https://www.futuretools.io/tools?category=video",
                "https://www.futuretools.io/tools?category=audio", 
                "https://www.futuretools.io/tools?category=code",
                "https://www.futuretools.io/tools?category=productivity",
                "https://www.futuretools.io/tools?category=marketing",
                "https://www.futuretools.io/tools?category=business",
                "https://www.futuretools.io/tools?category=education",
                "https://www.futuretools.io/tools?category=research",
                "https://www.futuretools.io/tools?category=design",
                "https://www.futuretools.io/tools?category=analytics",
                
                # Paginated pages
                *[f"https://www.futuretools.io/tools?page={i}" for i in range(1, 51)],
            ]
            
            for url in base_urls:
                try:
                    logger.info(f"Loading: {url}")
                    driver.get(url)
                    time.sleep(3)
                    
                    # Scroll to trigger lazy loading
                    for scroll in range(5):
                        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                        time.sleep(2)
                    
                    # Multiple selectors to find tools
                    selectors = [
                        '[data-testid*="tool"]',
                        '.tool-card', '.tool-item',
                        '[class*="card"]', '[class*="item"]', '[class*="tool"]',
                        '.grid-item', '.list-item', 
                        'div[role="listitem"]',
                        'article'
                    ]
                    
                    elements = []
                    for selector in selectors:
                        found = driver.find_elements(By.CSS_SELECTOR, selector)
                        if found:
                            elements.extend(found)
                    
                    # Remove duplicates
                    unique_elements = []
                    seen_elements = set()
                    for elem in elements:
                        elem_text = elem.text[:100]  # First 100 chars as identifier
                        if elem_text not in seen_elements and len(elem_text) > 10:
                            unique_elements.append(elem)
                            seen_elements.add(elem_text)
                    
                    page_tools = 0
                    for element in unique_elements[:150]:  # Process more elements
                        try:
                            # Extract name
                            name = ""
                            name_selectors = ['h1', 'h2', 'h3', 'h4', '.title', '[class*="title"]', '[class*="name"]']
                            for sel in name_selectors:
                                try:
                                    name_elem = element.find_element(By.CSS_SELECTOR, sel)
                                    if name_elem and name_elem.text.strip():
                                        name = name_elem.text.strip()
                                        break
                                except:
                                    continue
                            
                            # Extract description
                            description = ""
                            desc_selectors = ['p', '.description', '[class*="desc"]', '[class*="excerpt"]']
                            for sel in desc_selectors:
                                try:
                                    desc_elem = element.find_element(By.CSS_SELECTOR, sel)
                                    if desc_elem and desc_elem.text.strip():
                                        description = desc_elem.text.strip()
                                        break
                                except:
                                    continue
                            
                            # Extract URL
                            website_url = ""
                            try:
                                link = element.find_element(By.CSS_SELECTOR, 'a[href]')
                                if link:
                                    website_url = link.get_attribute('href')
                            except:
                                pass
                            
                            # Extract category
                            category = "AI Tools"
                            try:
                                cat_elem = element.find_element(By.CSS_SELECTOR, '[class*="category"], [class*="tag"], .badge')
                                if cat_elem:
                                    category = cat_elem.text.strip()
                            except:
                                pass
                            
                            if name and len(name) > 2:
                                tools.append({
                                    'name': self.clean_text(name),
                                    'description': self.clean_text(description),
                                    'website_url': website_url,
                                    'category': category,
                                    'source_website': 'futuretools.io',
                                    'source_page_url': url
                                })
                                page_tools += 1
                                
                        except Exception as e:
                            continue
                    
                    logger.info(f"Found {page_tools} tools on this page")
                    
                    # Check if we should continue (no tools found = likely end of pagination)
                    if page_tools == 0 and 'page=' in url:
                        break
                        
                    time.sleep(2)
                    
                except Exception as e:
                    logger.error(f"Error loading {url}: {e}")
                    continue
                    
        finally:
            driver.quit()
        
        # Deduplicate
        unique_tools = []
        seen_names = set()
        for tool in tools:
            if tool['name'] not in seen_names:
                unique_tools.append(tool)
                seen_names.add(tool['name'])
        
        logger.info(f"🎯 futuretools.io: {len(unique_tools)} unique tools found")
        return unique_tools
    
    def scrape_site_comprehensive(self, base_url, site_name, use_selenium=False):
        """Comprehensive scraping strategy for any site"""
        logger.info(f"🔥 MASSIVE SCRAPING: {site_name}")
        tools = []
        
        # Generate many possible URLs
        urls_to_try = [
            base_url,
            f"{base_url}/tools",
            f"{base_url}/ai-tools", 
            f"{base_url}/directory",
            f"{base_url}/tools/",
            f"{base_url}/ai-tools/",
            f"{base_url}/directory/",
            f"{base_url}/browse",
            f"{base_url}/list",
            f"{base_url}/catalog",
            f"{base_url}/marketplace",
        ]
        
        # Add paginated URLs
        for page in range(1, 21):
            urls_to_try.extend([
                f"{base_url}?page={page}",
                f"{base_url}/page/{page}",
                f"{base_url}?p={page}",
                f"{base_url}/tools?page={page}",
                f"{base_url}/tools/page/{page}",
                f"{base_url}?offset={page*20}",
                f"{base_url}?start={page*20}"
            ])
        
        # Add category URLs if we know common patterns
        categories = ['writing', 'image', 'video', 'audio', 'code', 'productivity', 'marketing', 'business', 'design', 'analytics', 'research', 'education']
        for cat in categories:
            urls_to_try.extend([
                f"{base_url}/category/{cat}",
                f"{base_url}/tools/{cat}",
                f"{base_url}/{cat}",
                f"{base_url}?category={cat}",
                f"{base_url}/tools?category={cat}"
            ])
        
        if use_selenium:
            driver = self.get_driver()
            if not driver:
                return tools
        
        try:
            for url in urls_to_try[:100]:  # Limit to prevent infinite requests
                try:
                    logger.info(f"Trying: {url}")
                    
                    if use_selenium:
                        driver.get(url)
                        time.sleep(3)
                        
                        # Scroll to load content
                        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                        time.sleep(2)
                        
                        html = driver.page_source
                        soup = BeautifulSoup(html, 'html.parser')
                    else:
                        response = self.session.get(url, timeout=20)
                        if response.status_code != 200:
                            continue
                        soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Multiple strategies to find tools
                    tool_elements = []
                    
                    # Strategy 1: Common tool selectors
                    selectors = [
                        'div[class*="tool"]', 'div[class*="card"]', 'div[class*="item"]',
                        'article', 'div[class*="post"]', 'div[class*="entry"]',
                        'div[class*="listing"]', 'div[class*="product"]',
                        '.tool', '.card', '.item', '.listing', '.product',
                        '[data-testid*="tool"]', '[data-testid*="item"]'
                    ]
                    
                    for selector in selectors:
                        elements = soup.select(selector)
                        if elements:
                            tool_elements.extend(elements)
                    
                    # Strategy 2: Look for links with meaningful text
                    if not tool_elements:
                        links = soup.find_all('a', href=True)
                        for link in links:
                            parent = link.parent
                            if parent and len(parent.get_text().strip()) > 30:
                                tool_elements.append(parent)
                    
                    page_tools = 0
                    for element in tool_elements[:100]:
                        try:
                            # Extract tool information
                            name = ""
                            for tag in ['h1', 'h2', 'h3', 'h4', 'h5']:
                                title_elem = element.find(tag)
                                if title_elem and title_elem.get_text().strip():
                                    name = self.clean_text(title_elem.get_text())
                                    break
                            
                            if not name:
                                # Try link text
                                link = element.find('a')
                                if link and link.get_text().strip():
                                    name = self.clean_text(link.get_text())
                            
                            description = ""
                            desc_elem = element.find('p')
                            if desc_elem:
                                description = self.clean_text(desc_elem.get_text())
                            
                            website_url = ""
                            link = element.find('a', href=True)
                            if link:
                                href = link['href']
                                if href.startswith('http'):
                                    website_url = href
                                elif href.startswith('/'):
                                    website_url = urljoin(base_url, href)
                            
                            if name and len(name) > 2 and not any(skip in name.lower() for skip in ['read more', 'click here', 'view all', 'see more', 'next page']):
                                tools.append({
                                    'name': name,
                                    'description': description,
                                    'website_url': website_url,
                                    'category': 'AI Tools',
                                    'source_website': site_name,
                                    'source_page_url': url
                                })
                                page_tools += 1
                                
                        except Exception as e:
                            continue
                    
                    if page_tools > 0:
                        logger.info(f"Found {page_tools} tools on {url}")
                    
                    time.sleep(1)  # Rate limiting
                    
                except Exception as e:
                    logger.error(f"Error scraping {url}: {e}")
                    continue
                    
        finally:
            if use_selenium and 'driver' in locals():
                driver.quit()
        
        # Deduplicate
        unique_tools = []
        seen_names = set()
        for tool in tools:
            if tool['name'] not in seen_names:
                unique_tools.append(tool)
                seen_names.add(tool['name'])
        
        logger.info(f"🎯 {site_name}: {len(unique_tools)} unique tools found")
        return unique_tools
    
    def save_tools_batch(self, tools):
        """Save tools to database in batch"""
        if not tools:
            return 0
        
        saved = 0
        for tool in tools:
            try:
                self.cursor.execute('''
                    INSERT OR IGNORE INTO ai_tools (
                        name, description, category, website_url, pricing_model,
                        free_tier, source_website, source_page_url
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    tool['name'],
                    tool.get('description', ''),
                    tool.get('category', ''),
                    tool.get('website_url', ''),
                    tool.get('pricing_model', ''),
                    tool.get('free_tier', False),
                    tool['source_website'],
                    tool.get('source_page_url', '')
                ))
                saved += 1
            except Exception as e:
                continue
        
        self.conn.commit()
        logger.info(f"💾 Saved {saved} tools to database")
        return saved
    
    def run_massive_scraping(self):
        """Run the massive scraping operation"""
        logger.info("🚀 STARTING MASSIVE AI TOOLS SCRAPING")
        logger.info("Target: 5000+ tools")
        
        all_tools = []
        
        # 1. Comprehensive bestaito.com
        bestaito_tools = self.scrape_comprehensive_bestaito()
        all_tools.extend(bestaito_tools)
        self.save_tools_batch(bestaito_tools)
        
        # 2. Massive futuretools.io
        futuretools_tools = self.scrape_massive_futuretools()
        all_tools.extend(futuretools_tools)
        self.save_tools_batch(futuretools_tools)
        
        # 3. Comprehensive scraping of other major sites
        sites_to_scrape = [
            ("https://www.bestaitools.com", "bestaitools.com", True),  # Use Selenium
            ("https://10bestaitools.com", "10bestaitools.com", False),
            ("https://bestfreeaitools.io", "bestfreeaitools.io", False),
            ("https://best-ai-tools.org", "best-ai-tools.org", False),
            ("https://aisitelist.com", "aisitelist.com", False),
            ("https://opentools.ai", "opentools.ai", True),  # Use Selenium
            ("https://www.insidr.ai", "insidr.ai", True),  # Use Selenium
        ]
        
        for base_url, site_name, use_selenium in sites_to_scrape:
            try:
                logger.info(f"🎯 Starting comprehensive scraping of {site_name}")
                site_tools = self.scrape_site_comprehensive(base_url, site_name, use_selenium)
                all_tools.extend(site_tools)
                self.save_tools_batch(site_tools)
                time.sleep(3)  # Rate limiting between sites
            except Exception as e:
                logger.error(f"Error with {site_name}: {e}")
                continue
        
        # Get final count
        self.cursor.execute("SELECT COUNT(*) FROM ai_tools")
        total_count = self.cursor.fetchone()[0]
        
        logger.info(f"🎉 MASSIVE SCRAPING COMPLETED!")
        logger.info(f"📊 Total tools in database: {total_count}")
        logger.info(f"🆕 Tools collected in this session: {len(all_tools)}")
        
        return all_tools
    
    def close(self):
        """Close database connection"""
        self.conn.close()

if __name__ == "__main__":
    scraper = MassiveAIToolsScraper()
    
    try:
        tools = scraper.run_massive_scraping()
        
        # Export results
        scraper.cursor.execute("SELECT * FROM ai_tools")
        all_data = scraper.cursor.fetchall()
        
        if all_data:
            columns = [description[0] for description in scraper.cursor.description]
            df = pd.DataFrame(all_data, columns=columns)
            timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
            df.to_csv(f'massive_scrape_results_{timestamp}.csv', index=False)
            logger.info(f"Results exported to massive_scrape_results_{timestamp}.csv")
        
    except Exception as e:
        logger.error(f"Error in massive scraping: {e}")
    finally:
        scraper.close()
