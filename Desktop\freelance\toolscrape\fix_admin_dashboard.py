#!/usr/bin/env python3
"""
Admin Dashboard Error Diagnostic Tool
This script will identify and fix common issues with the admin dashboard
"""

import os
import sys
import subprocess

def check_and_fix_issues():
    print("🔧 Admin Dashboard Error Diagnostic")
    print("=" * 50)
    
    # 1. Check Python version
    print(f"🐍 Python version: {sys.version}")
    
    # 2. Check if we're in the right directory
    print(f"📍 Current directory: {os.getcwd()}")
    
    required_files = [
        'admin_dashboard.py',
        'ai_tools_master.db',
        'templates/admin/login.html',
        'templates/admin/dashboard.html'
    ]
    
    print("\n📁 Checking required files:")
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
    
    # 3. Check dependencies
    print("\n📦 Checking dependencies:")
    required_packages = [
        'flask',
        'pandas', 
        'requests',
        'beautifulsoup4'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'beautifulsoup4':
                import bs4
                print(f"✅ {package}")
            else:
                __import__(package)
                print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_packages.append(package)
    
    # 4. Install missing packages
    if missing_packages:
        print(f"\n🔧 Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("✅ Packages installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install packages: {e}")
            return False
    
    # 5. Install optional packages
    optional_packages = ['feedparser']
    print(f"\n🔧 Installing optional packages: {', '.join(optional_packages)}")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + optional_packages)
        print("✅ Optional packages installed")
    except subprocess.CalledProcessError as e:
        print(f"⚠️ Optional package installation failed: {e}")
    
    # 6. Test admin dashboard import
    print("\n🧪 Testing admin dashboard import:")
    try:
        import admin_dashboard
        print("✅ Admin dashboard imported successfully")
        
        # Test Flask app
        app = admin_dashboard.app
        print("✅ Flask app created")
        
        # Test routes
        with app.app_context():
            print("✅ Flask app context works")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def start_dashboard():
    """Start the dashboard after fixing issues"""
    print("\n🚀 Starting Admin Dashboard...")
    
    try:
        import admin_dashboard
        app = admin_dashboard.app
        
        print("🌐 Server starting on http://localhost:5001")
        print("🔐 Login: admin / admin123")
        print("📋 Visit: http://localhost:5001/admin/login")
        print("🔌 API Scraping: http://localhost:5001/admin/api-scraping")
        print("\nPress Ctrl+C to stop the server")
        print("-" * 50)
        
        app.run(host='127.0.0.1', port=5001, debug=True, use_reloader=False)
        
    except Exception as e:
        print(f"❌ Failed to start dashboard: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function"""
    if check_and_fix_issues():
        choice = input("\n✅ All checks passed! Start the dashboard? (y/n): ").lower()
        if choice in ['y', 'yes']:
            start_dashboard()
        else:
            print("👋 You can start manually with: python start_admin_simple.py")
    else:
        print("\n❌ Issues found. Please fix the errors above and try again.")
        print("💡 Common solutions:")
        print("   - Run: pip install -r requirements.txt")
        print("   - Check you're in the correct directory")
        print("   - Verify all files are present")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Stopped by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
