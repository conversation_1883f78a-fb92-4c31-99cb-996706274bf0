#!/usr/bin/env python3
"""Quick status check for the admin dashboard"""

import requests
import time

def check_dashboard_status():
    """Check if all main dashboard routes are working"""
    base_url = "http://localhost:5001"
    
    routes_to_test = [
        ("/admin", "Admin Dashboard"),
        ("/admin/login", "Login Page"),
        ("/admin/dashboard", "Main Dashboard"), 
        ("/admin/tools", "Tools Management"),
        ("/admin/api-scraping", "API Scraping"),
        ("/admin/scraping", "Web Scraping"),
        ("/admin/analytics", "Analytics"),
        ("/admin/import_export", "Import/Export"),
        ("/admin/publishing", "Publishing")
    ]
    
    print("🔍 Admin Dashboard Status Check")
    print("=" * 40)
    
    working_routes = 0
    total_routes = len(routes_to_test)
    
    for route, description in routes_to_test:
        try:
            response = requests.get(f"{base_url}{route}", timeout=5)
            status_icon = "✅" if response.status_code in [200, 302] else "❌"
            print(f"{status_icon} {description:<20} ({response.status_code})")
            
            if response.status_code in [200, 302]:
                working_routes += 1
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {description:<20} (Connection Failed)")
    
    print("=" * 40)
    print(f"📊 Status: {working_routes}/{total_routes} routes working")
    
    if working_routes == total_routes:
        print("🎉 All routes are working perfectly!")
    elif working_routes >= total_routes * 0.8:
        print("✅ Dashboard is mostly functional")
    else:
        print("⚠️  Some issues detected")
    
    print("\n🌐 Access URLs:")
    print("   Main:         http://localhost:5001/admin")
    print("   Login:        http://localhost:5001/admin/login")
    print("   API Scraping: http://localhost:5001/admin/api-scraping")
    print("\n🔐 Login Credentials:")
    print("   Username: admin")
    print("   Password: admin123")

if __name__ == "__main__":
    check_dashboard_status()
