import sqlite3

# Connect to the fixed database
conn = sqlite3.connect('ai_tools_database_fixed.db')

# Get basic stats
total_tools = conn.execute('SELECT COUNT(*) FROM ai_tools').fetchone()[0]
print(f'Total tools in fixed DB: {total_tools}')

# Get sample tools
print('\nSample tools:')
for row in conn.execute('SELECT name, category, source_website, free_tier FROM ai_tools LIMIT 5').fetchall():
    print(f'  {row[0]} - {row[1]} - {row[2]} - Free: {row[3]}')

# Get category breakdown
print('\nTop categories:')
for row in conn.execute('SELECT category, COUNT(*) FROM ai_tools WHERE category IS NOT NULL GROUP BY category ORDER BY COUNT(*) DESC LIMIT 10').fetchall():
    print(f'  {row[0]}: {row[1]}')

# Get source breakdown
print('\nSources:')
for row in conn.execute('SELECT source_website, COUNT(*) FROM ai_tools GROUP BY source_website ORDER BY COUNT(*) DESC LIMIT 10').fetchall():
    print(f'  {row[0]}: {row[1]}')

conn.close()
