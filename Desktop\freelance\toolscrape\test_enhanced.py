#!/usr/bin/env python3
"""
Test script to verify the enhanced web interface works
"""

try:
    print("Testing enhanced web interface...")
    
    # Test database connection
    import sqlite3
    conn = sqlite3.connect('ai_tools_master.db')
    
    # Test basic queries
    total = conn.execute('SELECT COUNT(*) FROM ai_tools').fetchone()[0]
    print(f"✅ Database connection: {total} tools found")
    
    # Test required columns exist
    try:
        conn.execute('SELECT name, description, category, source_website, website_url, free_tier, created_at FROM ai_tools LIMIT 1').fetchone()
        print("✅ All required columns exist")
    except Exception as e:
        print(f"❌ Column error: {e}")
    
    conn.close()
    
    # Test Flask import
    from flask import Flask
    print("✅ Flask import successful")
    
    # Test other imports
    import pandas as pd
    print("✅ Pandas import successful")
    
    print("\n🎉 Enhanced web interface should work!")
    print("Run: .\\run_web.bat to start the server")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
