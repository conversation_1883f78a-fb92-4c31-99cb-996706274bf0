"""
Site Inspector and Targeted Scraper
This script first inspects sites to understand their structure, then scrapes accordingly
"""

import requests
from bs4 import BeautifulSoup
import sqlite3
import time
import logging
from fake_useragent import UserAgent
import pandas as pd
from datetime import datetime
import re

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SiteInspectorAndScraper:
    def __init__(self):
        self.ua = UserAgent()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # Fix database schema
        self.conn = sqlite3.connect('ai_tools_database.db')
        self.cursor = self.conn.cursor()
        self.fix_database_schema()
        
        self.all_tools = []
    
    def fix_database_schema(self):
        """Fix database schema issues"""
        try:
            # Check if created_at column exists
            self.cursor.execute("PRAGMA table_info(ai_tools)")
            columns = [col[1] for col in self.cursor.fetchall()]
            
            if 'created_at' not in columns:
                logger.info("Adding created_at column to database")
                self.cursor.execute('ALTER TABLE ai_tools ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
                self.conn.commit()
                
        except Exception as e:
            logger.error(f"Database schema fix error: {e}")
    
    def clean_text(self, text):
        """Clean text content"""
        if not text:
            return ""
        text = re.sub(r'\s+', ' ', text.strip())
        return text[:500]
    
    def inspect_site_structure(self, url, site_name):
        """Inspect a site to understand its structure"""
        logger.info(f"🔍 INSPECTING: {site_name} - {url}")
        
        try:
            response = self.session.get(url, timeout=15)
            if response.status_code != 200:
                logger.error(f"❌ {site_name}: HTTP {response.status_code}")
                return None
                
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for common patterns that might contain tools
            potential_selectors = []
            
            # Look for elements with tool-related classes
            tool_keywords = ['tool', 'ai', 'app', 'product', 'card', 'item', 'listing', 'grid']
            for keyword in tool_keywords:
                elements = soup.find_all(attrs={'class': re.compile(keyword, re.I)})
                if elements:
                    for elem in elements[:3]:  # Check first 3
                        classes = ' '.join(elem.get('class', []))
                        if classes and classes not in potential_selectors:
                            potential_selectors.append(f".{classes.replace(' ', '.')}")
            
            # Look for repeated structures (likely tool listings)
            common_tags = ['article', 'div', 'section', 'li']
            for tag in common_tags:
                elements = soup.find_all(tag)
                if len(elements) > 10:  # If there are many, might be tool listings
                    # Check if they have similar structure
                    if elements:
                        sample_elem = elements[0]
                        if sample_elem.find(['h1', 'h2', 'h3', 'h4']) and (sample_elem.find('p') or sample_elem.find('a')):
                            potential_selectors.append(tag)
            
            logger.info(f"📋 {site_name}: Potential selectors found: {potential_selectors[:5]}")
            
            # Try to extract some sample data
            tools_found = []
            for selector in potential_selectors[:3]:  # Try top 3 selectors
                try:
                    elements = soup.select(selector)
                    if elements:
                        logger.info(f"🔍 Testing selector '{selector}': {len(elements)} elements")
                        
                        for elem in elements[:5]:  # Test first 5 elements
                            # Try to extract name
                            name = ""
                            for name_tag in ['h1', 'h2', 'h3', 'h4', 'h5']:
                                name_elem = elem.find(name_tag)
                                if name_elem:
                                    name = self.clean_text(name_elem.get_text())
                                    break
                            
                            # Try to extract description
                            description = ""
                            desc_elem = elem.find('p')
                            if desc_elem:
                                description = self.clean_text(desc_elem.get_text())
                            
                            # Try to extract URL
                            url_elem = elem.find('a', href=True)
                            website_url = ""
                            if url_elem:
                                website_url = url_elem['href']
                            
                            if name and len(name) > 3:
                                tools_found.append({
                                    'name': name,
                                    'description': description,
                                    'website_url': website_url,
                                    'selector_used': selector
                                })
                except Exception as e:
                    continue
            
            if tools_found:
                logger.info(f"✅ {site_name}: Found {len(tools_found)} sample tools")
                for tool in tools_found[:3]:
                    logger.info(f"   📌 {tool['name'][:50]}...")
                return {'url': url, 'selectors': potential_selectors, 'sample_tools': tools_found}
            else:
                logger.warning(f"⚠️ {site_name}: No tools found with automatic detection")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error inspecting {site_name}: {e}")
            return None
    
    def scrape_with_proven_selectors(self):
        """Scrape using sites and selectors that we know work"""
        logger.info("🎯 SCRAPING WITH PROVEN SELECTORS")
        
        # These are sites with known working selectors from previous analysis
        proven_sources = [
            {
                'name': 'Best AI Tools',
                'url': 'https://www.bestaitools.com/',
                'selectors': ['.tool-item', '.ai-tool-card', '.grid-item', 'article'],
                'pagination': True,
                'max_pages': 20
            },
            {
                'name': 'Insidr AI',
                'url': 'https://insidr.ai/ai-tools/',
                'selectors': ['.tool-card', '.ai-tool', '.listing-item'],
                'pagination': False
            },
            {
                'name': 'AI Tool Guru',
                'url': 'https://www.aitoolguru.com/',
                'selectors': ['.tool-listing', '.ai-tool', '.product-item'],
                'pagination': True,
                'max_pages': 15
            }
        ]
        
        total_tools = []
        
        for source in proven_sources:
            logger.info(f"🚀 SCRAPING: {source['name']}")
            
            if source.get('pagination'):
                # Try multiple pages
                for page in range(1, source.get('max_pages', 10) + 1):
                    page_url = f"{source['url']}?page={page}" if '?' not in source['url'] else f"{source['url']}&page={page}"
                    tools = self.scrape_single_page(page_url, source['selectors'], source['name'])
                    
                    if tools:
                        total_tools.extend(tools)
                        logger.info(f"📄 Page {page}: {len(tools)} tools")
                        time.sleep(1)
                    else:
                        break  # No more tools, stop pagination
            else:
                # Single page
                tools = self.scrape_single_page(source['url'], source['selectors'], source['name'])
                if tools:
                    total_tools.extend(tools)
        
        logger.info(f"✅ PROVEN SELECTORS: {len(total_tools)} tools collected")
        return total_tools
    
    def scrape_single_page(self, url, selectors, source_name):
        """Scrape a single page with given selectors"""
        try:
            response = self.session.get(url, timeout=15)
            if response.status_code != 200:
                return []
            
            soup = BeautifulSoup(response.content, 'html.parser')
            tools = []
            
            for selector in selectors:
                elements = soup.select(selector)
                if elements:
                    logger.info(f"🎯 Using selector '{selector}': {len(elements)} elements")
                    
                    for elem in elements:
                        try:
                            # Extract name
                            name = ""
                            for tag in ['h1', 'h2', 'h3', 'h4', 'h5']:
                                name_elem = elem.find(tag)
                                if name_elem:
                                    name = self.clean_text(name_elem.get_text())
                                    break
                            
                            if not name:
                                # Try getting name from link text
                                link = elem.find('a')
                                if link:
                                    name = self.clean_text(link.get_text())
                            
                            if not name or len(name) < 3:
                                continue
                            
                            # Extract description
                            description = ""
                            desc_elem = elem.find('p')
                            if desc_elem:
                                description = self.clean_text(desc_elem.get_text())
                            
                            # Extract URL
                            website_url = ""
                            link = elem.find('a', href=True)
                            if link:
                                href = link['href']
                                if href.startswith('http'):
                                    website_url = href
                                elif href.startswith('/'):
                                    website_url = f"https://{url.split('/')[2]}{href}"
                            
                            # Extract pricing info
                            elem_text = elem.get_text().lower()
                            pricing = "Unknown"
                            if any(word in elem_text for word in ['free', 'gratis', '$0']):
                                pricing = "Free"
                            elif any(word in elem_text for word in ['premium', 'paid', '$']):
                                pricing = "Premium"
                            elif 'freemium' in elem_text:
                                pricing = "Freemium"
                            
                            tool = {
                                'name': name,
                                'description': description,
                                'website_url': website_url,
                                'pricing_model': pricing,
                                'category': 'AI Tools',
                                'source_website': source_name.lower().replace(' ', ''),
                                'source_page_url': url,
                                'free_tier': pricing in ['Free', 'Freemium']
                            }
                            
                            # Check for duplicates
                            if not any(t['name'].lower() == name.lower() for t in tools):
                                tools.append(tool)
                                
                        except Exception as e:
                            continue
                    
                    break  # Found tools with this selector, stop trying others
            
            return tools
            
        except Exception as e:
            logger.error(f"Error scraping {url}: {e}")
            return []
    
    def scrape_github_awesome_lists(self):
        """Scrape GitHub awesome lists for AI tools"""
        logger.info("🚀 SCRAPING: GitHub Awesome AI Tools Lists")
        
        awesome_lists = [
            'https://raw.githubusercontent.com/mahseema/awesome-ai-tools/main/README.md',
            'https://raw.githubusercontent.com/steven2358/awesome-generative-ai/main/README.md'
        ]
        
        tools = []
        
        for url in awesome_lists:
            try:
                response = self.session.get(url, timeout=15)
                if response.status_code == 200:
                    content = response.text
                    
                    # Parse markdown links
                    import re
                    
                    # Find all markdown links [name](url)
                    pattern = r'\\[([^\\]]+)\\]\\(([^\\)]+)\\)'
                    matches = re.findall(pattern, content)
                    
                    for name, link in matches:
                        if any(keyword in name.lower() for keyword in ['ai', 'tool', 'generator', 'assistant']):
                            tool = {
                                'name': self.clean_text(name),
                                'description': f"Tool from awesome list: {name}",
                                'website_url': link,
                                'pricing_model': 'Unknown',
                                'category': 'AI Tools',
                                'source_website': 'github-awesome',
                                'source_page_url': url,
                                'free_tier': False
                            }
                            
                            if not any(t['name'].lower() == name.lower() for t in tools):
                                tools.append(tool)
                
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error scraping GitHub awesome list {url}: {e}")
                continue
        
        logger.info(f"✅ GITHUB AWESOME: {len(tools)} tools collected")
        return tools
    
    def scrape_producthunt_ai_tools(self):
        """Scrape Product Hunt for AI tools"""
        logger.info("🚀 SCRAPING: Product Hunt AI Tools")
        
        urls = [
            'https://www.producthunt.com/topics/artificial-intelligence',
            'https://www.producthunt.com/topics/machine-learning',
            'https://www.producthunt.com/topics/automation'
        ]
        
        tools = []
        
        for url in urls:
            try:
                response = self.session.get(url, timeout=15)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Product Hunt specific selectors
                    selectors = ['[data-test="post-item"]', '.styles_item__', 'article']
                    
                    for selector in selectors:
                        elements = soup.select(selector)
                        if elements:
                            for elem in elements[:20]:  # Limit to avoid too many
                                try:
                                    name_elem = elem.find(['h3', 'h4', 'h5'])
                                    if name_elem:
                                        name = self.clean_text(name_elem.get_text())
                                        
                                        desc_elem = elem.find('p')
                                        description = ""
                                        if desc_elem:
                                            description = self.clean_text(desc_elem.get_text())
                                        
                                        link = elem.find('a', href=True)
                                        website_url = ""
                                        if link:
                                            website_url = f"https://www.producthunt.com{link['href']}"
                                        
                                        if name and len(name) > 3:
                                            tool = {
                                                'name': name,
                                                'description': description,
                                                'website_url': website_url,
                                                'pricing_model': 'Unknown',
                                                'category': 'AI Tools',
                                                'source_website': 'producthunt',
                                                'source_page_url': url,
                                                'free_tier': False
                                            }
                                            
                                            if not any(t['name'].lower() == name.lower() for t in tools):
                                                tools.append(tool)
                                                
                                except Exception as e:
                                    continue
                            break
                
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"Error scraping Product Hunt {url}: {e}")
                continue
        
        logger.info(f"✅ PRODUCT HUNT: {len(tools)} tools collected")
        return tools
    
    def save_tools_to_database(self, tools):
        """Save tools to database"""
        saved_count = 0
        
        for tool in tools:
            try:
                self.cursor.execute('''
                    INSERT OR IGNORE INTO ai_tools 
                    (name, description, website_url, pricing_model, category, 
                     source_website, source_page_url, free_tier)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    tool['name'],
                    tool['description'],
                    tool['website_url'],
                    tool['pricing_model'],
                    tool['category'],
                    tool['source_website'],
                    tool['source_page_url'],
                    tool['free_tier']
                ))
                
                if self.cursor.rowcount > 0:
                    saved_count += 1
                    
            except Exception as e:
                logger.error(f"Error saving tool {tool['name']}: {e}")
                continue
        
        self.conn.commit()
        logger.info(f"💾 Saved {saved_count} new tools to database")
        return saved_count
    
    def run_comprehensive_scraping(self):
        """Run comprehensive scraping with multiple strategies"""
        logger.info("🚀🚀🚀 STARTING COMPREHENSIVE SCRAPING WITH SITE INSPECTION 🚀🚀🚀")
        
        start_time = time.time()
        
        # Get initial count
        self.cursor.execute('SELECT COUNT(*) FROM ai_tools')
        initial_count = self.cursor.fetchone()[0]
        logger.info(f"Starting with {initial_count} tools in database")
        
        all_tools = []
        
        # Strategy 1: Inspect and scrape known sites
        sites_to_inspect = [
            ('https://www.bestaitools.com/', 'Best AI Tools'),
            ('https://insidr.ai/ai-tools/', 'Insidr AI'),
            ('https://www.futuretools.io/', 'Future Tools'),
            ('https://opentools.ai/', 'Open Tools'),
        ]
        
        for url, name in sites_to_inspect:
            result = self.inspect_site_structure(url, name)
            if result and result['sample_tools']:
                # Use the working selectors to scrape more
                for selector in result['selectors'][:2]:  # Try top 2 selectors
                    tools = self.scrape_single_page(url, [selector], name)
                    if tools:
                        all_tools.extend(tools)
                        break
        
        # Strategy 2: Use proven selectors
        proven_tools = self.scrape_with_proven_selectors()
        all_tools.extend(proven_tools)
        
        # Strategy 3: GitHub awesome lists
        github_tools = self.scrape_github_awesome_lists()
        all_tools.extend(github_tools)
        
        # Strategy 4: Product Hunt
        ph_tools = self.scrape_producthunt_ai_tools()
        all_tools.extend(ph_tools)
        
        # Remove duplicates
        unique_tools = []
        seen_names = set()
        for tool in all_tools:
            name_key = tool['name'].lower().strip()
            if name_key not in seen_names:
                unique_tools.append(tool)
                seen_names.add(name_key)
        
        # Save to database
        saved_count = self.save_tools_to_database(unique_tools)
        
        # Final summary
        self.cursor.execute('SELECT COUNT(*) FROM ai_tools')
        final_count = self.cursor.fetchone()[0]
        
        elapsed_time = time.time() - start_time
        
        logger.info(f"\n{'='*80}")
        logger.info(f"🏁 COMPREHENSIVE SCRAPING COMPLETED 🏁")
        logger.info(f"{'='*80}")
        logger.info(f"⏱️  Time elapsed: {elapsed_time/60:.2f} minutes")
        logger.info(f"📊 Initial tools: {initial_count}")
        logger.info(f"📊 Final tools: {final_count}")
        logger.info(f"📊 New tools added: {final_count - initial_count}")
        logger.info(f"📊 Tools collected this session: {len(unique_tools)}")
        logger.info(f"📊 Tools saved: {saved_count}")
        
        # Export results
        self.export_results()
        
        return final_count
    
    def export_results(self):
        """Export results to CSV"""
        try:
            df = pd.read_sql_query('SELECT * FROM ai_tools ORDER BY id DESC', self.conn)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"comprehensive_scrape_results_{timestamp}.csv"
            df.to_csv(filename, index=False)
            
            logger.info(f"📁 Results exported to: {filename}")
            logger.info(f"📊 Total rows exported: {len(df)}")
            
            # Show summary by source
            if 'source_website' in df.columns:
                source_summary = df['source_website'].value_counts()
                logger.info(f"\n📈 Tools by source:")
                for source, count in source_summary.items():
                    logger.info(f"  {source}: {count} tools")
                    
        except Exception as e:
            logger.error(f"Error exporting results: {e}")

def main():
    scraper = SiteInspectorAndScraper()
    final_count = scraper.run_comprehensive_scraping()
    
    if final_count >= 1000:
        print(f"\n🎉 Good progress! Collected {final_count} tools")
    else:
        print(f"\n⚠️ Collected {final_count} tools. More strategies needed.")
    
    scraper.conn.close()

if __name__ == "__main__":
    main()
