import sqlite3

conn = sqlite3.connect('ai_tools_master.db')

print('Tables:')
for table in conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall():
    print(f'  {table[0]}')

print('\nAI Tools table structure:')
for col in conn.execute('PRAGMA table_info(ai_tools)').fetchall():
    print(f'  {col[1]} ({col[2]})')

print('\nSample data count:')
count = conn.execute('SELECT COUNT(*) FROM ai_tools').fetchone()[0]
print(f'  Total tools: {count}')

conn.close()
