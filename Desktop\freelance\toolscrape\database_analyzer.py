import sqlite3
import pandas as pd
import json
import csv
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class DatabaseAnalyzer:
    def __init__(self, db_path="ai_tools_database.db"):
        self.conn = sqlite3.connect(db_path)
        self.cursor = self.conn.cursor()
    
    def get_comprehensive_statistics(self):
        """Get comprehensive database statistics"""
        stats = {}
        
        # Basic counts
        self.cursor.execute("SELECT COUNT(*) FROM ai_tools")
        stats['total_tools'] = self.cursor.fetchone()[0]
        
        self.cursor.execute("SELECT COUNT(*) FROM github_repos")
        stats['github_repos'] = self.cursor.fetchone()[0]
        
        self.cursor.execute("SELECT COUNT(*) FROM huggingface_models")
        stats['huggingface_models'] = self.cursor.fetchone()[0]
        
        # Tools by source
        self.cursor.execute("SELECT source_website, COUNT(*) FROM ai_tools GROUP BY source_website ORDER BY COUNT(*) DESC")
        stats['tools_by_source'] = dict(self.cursor.fetchall())
        
        # Tools by category
        self.cursor.execute("SELECT category, COUNT(*) FROM ai_tools GROUP BY category ORDER BY COUNT(*) DESC")
        stats['tools_by_category'] = dict(self.cursor.fetchall())
        
        # Free vs paid tools
        self.cursor.execute("SELECT COUNT(*) FROM ai_tools WHERE free_tier = 1")
        stats['free_tools'] = self.cursor.fetchone()[0]
        
        self.cursor.execute("SELECT COUNT(*) FROM ai_tools WHERE open_source = 1")
        stats['open_source_tools'] = self.cursor.fetchone()[0]
        
        # Tools with URLs
        self.cursor.execute("SELECT COUNT(*) FROM ai_tools WHERE website_url IS NOT NULL AND website_url != ''")
        stats['tools_with_urls'] = self.cursor.fetchone()[0]
        
        # Recent additions (last 7 days)
        self.cursor.execute("SELECT COUNT(*) FROM ai_tools WHERE scraped_date >= datetime('now', '-7 days')")
        stats['recent_additions'] = self.cursor.fetchone()[0]
        
        return stats
    
    def export_all_to_csv(self):
        """Export all tables to CSV files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Export main tools table
        df_tools = pd.read_sql_query("SELECT * FROM ai_tools", self.conn)
        filename_tools = f"ai_tools_{timestamp}.csv"
        df_tools.to_csv(filename_tools, index=False)
        logger.info(f"AI tools exported to {filename_tools}")
        
        # Export GitHub repos if exists
        try:
            df_github = pd.read_sql_query("SELECT * FROM github_repos", self.conn)
            if not df_github.empty:
                filename_github = f"github_repos_{timestamp}.csv"
                df_github.to_csv(filename_github, index=False)
                logger.info(f"GitHub repos exported to {filename_github}")
        except:
            pass
        
        # Export HuggingFace models if exists
        try:
            df_hf = pd.read_sql_query("SELECT * FROM huggingface_models", self.conn)
            if not df_hf.empty:
                filename_hf = f"huggingface_models_{timestamp}.csv"
                df_hf.to_csv(filename_hf, index=False)
                logger.info(f"HuggingFace models exported to {filename_hf}")
        except:
            pass
        
        return filename_tools
    
    def export_to_json(self):
        """Export all data to JSON format"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ai_tools_database_{timestamp}.json"
        
        data = {}
        
        # Export tools
        df_tools = pd.read_sql_query("SELECT * FROM ai_tools", self.conn)
        data['ai_tools'] = df_tools.to_dict('records')
        
        # Export GitHub repos if exists
        try:
            df_github = pd.read_sql_query("SELECT * FROM github_repos", self.conn)
            if not df_github.empty:
                data['github_repos'] = df_github.to_dict('records')
        except:
            data['github_repos'] = []
        
        # Export HuggingFace models if exists
        try:
            df_hf = pd.read_sql_query("SELECT * FROM huggingface_models", self.conn)
            if not df_hf.empty:
                data['huggingface_models'] = df_hf.to_dict('records')
        except:
            data['huggingface_models'] = []
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"Database exported to {filename}")
        return filename
    
    def search_tools(self, query, category=None, source=None, free_only=False):
        """Search tools in database"""
        sql = "SELECT * FROM ai_tools WHERE 1=1"
        params = []
        
        if query:
            sql += " AND (name LIKE ? OR description LIKE ?)"
            params.extend([f"%{query}%", f"%{query}%"])
        
        if category:
            sql += " AND category LIKE ?"
            params.append(f"%{category}%")
        
        if source:
            sql += " AND source_website LIKE ?"
            params.append(f"%{source}%")
        
        if free_only:
            sql += " AND free_tier = 1"
        
        sql += " ORDER BY name"
        
        df = pd.read_sql_query(sql, self.conn, params=params)
        return df
    
    def get_tool_details(self, tool_name):
        """Get detailed information about a specific tool"""
        self.cursor.execute("SELECT * FROM ai_tools WHERE name = ?", (tool_name,))
        return self.cursor.fetchone()
    
    def get_category_breakdown(self):
        """Get detailed breakdown by categories"""
        query = """
        SELECT 
            category,
            COUNT(*) as total_tools,
            SUM(CASE WHEN free_tier = 1 THEN 1 ELSE 0 END) as free_tools,
            SUM(CASE WHEN open_source = 1 THEN 1 ELSE 0 END) as open_source_tools,
            GROUP_CONCAT(DISTINCT source_website) as sources
        FROM ai_tools 
        WHERE category IS NOT NULL AND category != ''
        GROUP BY category 
        ORDER BY total_tools DESC
        """
        
        df = pd.read_sql_query(query, self.conn)
        return df
    
    def get_source_analysis(self):
        """Get analysis by source websites"""
        query = """
        SELECT 
            source_website,
            COUNT(*) as total_tools,
            SUM(CASE WHEN free_tier = 1 THEN 1 ELSE 0 END) as free_tools,
            SUM(CASE WHEN open_source = 1 THEN 1 ELSE 0 END) as open_source_tools,
            COUNT(DISTINCT category) as categories_covered,
            MIN(scraped_date) as first_scraped,
            MAX(scraped_date) as last_scraped
        FROM ai_tools 
        GROUP BY source_website 
        ORDER BY total_tools DESC
        """
        
        df = pd.read_sql_query(query, self.conn)
        return df
    
    def generate_report(self):
        """Generate comprehensive report"""
        report = []
        
        # Get statistics
        stats = self.get_comprehensive_statistics()
        
        report.append("=" * 60)
        report.append("AI TOOLS DATABASE COMPREHENSIVE REPORT")
        report.append("=" * 60)
        report.append(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        report.append("OVERVIEW:")
        report.append(f"  Total AI Tools: {stats['total_tools']}")
        report.append(f"  GitHub Repositories: {stats['github_repos']}")
        report.append(f"  HuggingFace Models: {stats['huggingface_models']}")
        report.append(f"  Free Tools: {stats['free_tools']}")
        report.append(f"  Open Source Tools: {stats['open_source_tools']}")
        report.append(f"  Tools with URLs: {stats['tools_with_urls']}")
        report.append(f"  Recent Additions (7 days): {stats['recent_additions']}")
        report.append("")
        
        report.append("TOOLS BY SOURCE:")
        for source, count in stats['tools_by_source'].items():
            report.append(f"  {source}: {count}")
        report.append("")
        
        report.append("TOOLS BY CATEGORY:")
        for category, count in stats['tools_by_category'].items():
            if category:
                report.append(f"  {category}: {count}")
        report.append("")
        
        # Category breakdown
        cat_breakdown = self.get_category_breakdown()
        if not cat_breakdown.empty:
            report.append("DETAILED CATEGORY BREAKDOWN:")
            for _, row in cat_breakdown.iterrows():
                report.append(f"  {row['category']}:")
                report.append(f"    Total: {row['total_tools']}")
                report.append(f"    Free: {row['free_tools']}")
                report.append(f"    Open Source: {row['open_source_tools']}")
                report.append(f"    Sources: {row['sources']}")
                report.append("")
        
        # Source analysis
        source_analysis = self.get_source_analysis()
        if not source_analysis.empty:
            report.append("SOURCE ANALYSIS:")
            for _, row in source_analysis.iterrows():
                report.append(f"  {row['source_website']}:")
                report.append(f"    Total Tools: {row['total_tools']}")
                report.append(f"    Free Tools: {row['free_tools']}")
                report.append(f"    Open Source: {row['open_source_tools']}")
                report.append(f"    Categories Covered: {row['categories_covered']}")
                report.append(f"    First Scraped: {row['first_scraped']}")
                report.append(f"    Last Scraped: {row['last_scraped']}")
                report.append("")
        
        report.append("=" * 60)
        
        # Save report to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"ai_tools_report_{timestamp}.txt"
        
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        # Also print to console
        print('\n'.join(report))
        
        logger.info(f"Report saved to {report_filename}")
        return report_filename
    
    def close(self):
        """Close database connection"""
        self.conn.close()

if __name__ == "__main__":
    analyzer = DatabaseAnalyzer()
    
    try:
        # Generate comprehensive report
        analyzer.generate_report()
        
        # Export data
        csv_file = analyzer.export_all_to_csv()
        json_file = analyzer.export_to_json()
        
        print(f"\nData exported to:")
        print(f"  CSV: {csv_file}")
        print(f"  JSON: {json_file}")
        
    except Exception as e:
        logger.error(f"Error in analysis: {e}")
    finally:
        analyzer.close()
