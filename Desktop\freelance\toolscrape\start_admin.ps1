Write-Host "🚀 Starting AI Tools Admin Dashboard" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Blue

$currentDir = Get-Location
Write-Host "📍 Current directory: $currentDir" -ForegroundColor Yellow

# Check if admin_dashboard.py exists
if (Test-Path "admin_dashboard.py") {
    Write-Host "✅ admin_dashboard.py found" -ForegroundColor Green
} else {
    Write-Host "❌ admin_dashboard.py not found" -ForegroundColor Red
    exit 1
}

# Check if database exists
if (Test-Path "ai_tools_master.db") {
    Write-Host "✅ ai_tools_master.db found" -ForegroundColor Green
} else {
    Write-Host "❌ ai_tools_master.db not found" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🐍 Starting Python Flask server..." -ForegroundColor Cyan
Write-Host "🌐 Admin Dashboard will be available at: http://localhost:5001/admin/login" -ForegroundColor Magenta
Write-Host "🔐 Login credentials: admin / admin123" -ForegroundColor Yellow
Write-Host ""
Write-Host "💡 Press Ctrl+C to stop the server" -ForegroundColor Gray
Write-Host "=" * 50 -ForegroundColor Blue

# Start the admin dashboard
python admin_dashboard.py
