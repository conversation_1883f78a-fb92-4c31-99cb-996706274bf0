#!/usr/bin/env python3
"""
Enhanced Admin Dashboard Launcher
Starts the comprehensive AI Tools admin interface with all features
"""

import os
import sys
import time
import threading
import webbrowser
from datetime import datetime

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'flask', 'pandas', 'requests', 'beautifulsoup4', 
        'openpyxl', 'werkzeug'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("📦 Installing missing packages...")
        
        import subprocess
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ Installed {package}")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {package}")
                return False
    
    print("✅ All dependencies are available")
    return True

def check_database():
    """Check if the database exists and is accessible"""
    import sqlite3
    
    db_path = 'ai_tools_master.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        print("📋 Please ensure the database file exists in the current directory")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM ai_tools")
        count = cursor.fetchone()[0]
        conn.close()
        
        print(f"✅ Database accessible with {count} tools")
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def open_browser_delayed():
    """Open browser after a delay to ensure server is running"""
    time.sleep(3)
    admin_url = "http://localhost:5001/admin/login"
    print(f"🌐 Opening admin dashboard: {admin_url}")
    
    try:
        webbrowser.open(admin_url)
    except Exception as e:
        print(f"❌ Could not open browser automatically: {e}")
        print(f"📋 Please manually open: {admin_url}")

def main():
    """Main launcher function"""
    print("=" * 60)
    print("🚀 AI Tools Enhanced Admin Dashboard Launcher")
    print("=" * 60)
    print(f"📅 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check dependencies
    print("🔍 Checking dependencies...")
    if not check_dependencies():
        print("❌ Dependency check failed. Please install required packages.")
        input("Press Enter to exit...")
        return False
    
    # Check database
    print("\n🔍 Checking database...")
    if not check_database():
        print("❌ Database check failed.")
        input("Press Enter to exit...")
        return False
    
    # Start browser opening thread
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    print("\n🚀 Starting Enhanced Admin Dashboard...")
    print("📋 Features available:")
    print("   • 🔧 Tools Management (CRUD, Bulk Operations)")
    print("   • 🕷️ Web Scraping Management")
    print("   • 📢 Publishing System with Approval Workflow")
    print("   • 📊 Analytics & Reporting with Charts")
    print("   • 📥📤 Import/Export (CSV, JSON, Excel)")
    print("   • 🔔 Real-time Notifications")
    print("   • 👥 User Management & Authentication")
    print("   • 🔍 Advanced Search & Filtering")
    print()
    print("🔐 Default login credentials:")
    print("   Username: admin")
    print("   Password: admin123")
    print()
    print("🌐 Admin dashboard will be available at: http://localhost:5001/admin")
    print("💡 Press Ctrl+C to stop the server")
    print("=" * 60)
    
    try:
        # Import and run the admin dashboard
        from admin_dashboard import app
        
        # Configure for production-like environment
        app.config.update(
            DEBUG=True,  # Set to False in production
            TESTING=False,
            SECRET_KEY='enhanced_admin_dashboard_2025_production_key'
        )
        
        # Start the Flask application
        app.run(
            host='0.0.0.0',
            port=5001,
            debug=True,
            use_reloader=False  # Disable reloader to prevent double startup
        )
        
    except KeyboardInterrupt:
        print("\n\n🛑 Admin dashboard stopped by user")
        print("👋 Thank you for using AI Tools Enhanced Admin Dashboard!")
        
    except Exception as e:
        print(f"\n❌ Error starting admin dashboard: {e}")
        print("📋 Please check the error message above and try again")
        input("Press Enter to exit...")
        
    return True

if __name__ == "__main__":
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Run the launcher
    success = main()
    
    if not success:
        sys.exit(1)
