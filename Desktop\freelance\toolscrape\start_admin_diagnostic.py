#!/usr/bin/env python3
"""
Admin Dashboard Diagnostic Startup Script
Provides detailed error information for troubleshooting
"""

import os
import sys
import time
import webbrowser
import traceback

def check_dependencies():
    """Check if all required dependencies are available"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'flask', 'sqlite3', 'pandas', 'requests', 'beautifulsoup4'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'beautifulsoup4':
                import bs4
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_packages.append(package)
    
    # Check optional packages
    optional_packages = ['feedparser']
    for package in optional_packages:
        try:
            __import__(package)
            print(f"✅ {package} (optional)")
        except ImportError:
            print(f"⚠️ {package} (optional) - install with: pip install {package}")
    
    if missing_packages:
        print(f"\n❌ Missing required packages: {', '.join(missing_packages)}")
        print("📦 Install with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All required dependencies available")
    return True

def check_database():
    """Check database connectivity and tables"""
    print("\n🗄️ Checking database...")
    
    try:
        import sqlite3
        
        # Check if database files exist
        db_files = ['ai_tools_master.db', 'ai_tools_database.db']
        for db_file in db_files:
            if os.path.exists(db_file):
                print(f"✅ Database file: {db_file}")
            else:
                print(f"⚠️ Database file missing: {db_file}")
        
        # Test connection
        conn = sqlite3.connect('ai_tools_master.db')
        cursor = conn.cursor()
        
        # Check main tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = ['ai_tools', 'admin_users', 'scraping_jobs']
        for table in expected_tables:
            if table in tables:
                print(f"✅ Table: {table}")
            else:
                print(f"⚠️ Table missing: {table}")
        
        conn.close()
        print("✅ Database connectivity OK")
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def start_admin_dashboard():
    """Start the admin dashboard with error handling"""
    print("\n🚀 Starting Admin Dashboard...")
    print("📍 Working directory:", os.getcwd())
    print("🐍 Python path:", sys.executable)
    
    try:
        # Import the admin dashboard
        print("📥 Importing admin dashboard...")
        from admin_dashboard import app
        
        print("✅ Admin dashboard imported successfully!")
        
        # Test Flask app initialization
        with app.app_context():
            print("✅ Flask app context initialized")
        
        print("🌐 Starting server on http://localhost:5001")
        print("🔐 Login credentials: admin / admin123")
        print("📋 Visit: http://localhost:5001/admin/login")
        print("🔌 API Scraping: http://localhost:5001/admin/api-scraping")
        
        # Open browser after 3 seconds
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open("http://localhost:5001/admin/login")
                print("🌐 Browser opened automatically")
            except Exception as e:
                print(f"⚠️ Could not open browser automatically: {e}")
                print("Please manually open: http://localhost:5001/admin/login")
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        print("\n🔄 Starting Flask server...")
        print("Press Ctrl+C to stop the server")
        print("-" * 50)
        
        # Start the Flask app
        app.run(host='127.0.0.1', port=5001, debug=True, use_reloader=False)
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("📋 This usually means a dependency is missing")
        print("💡 Try: pip install -r requirements.txt")
        return False
        
    except Exception as e:
        print(f"❌ Startup error: {e}")
        print("\n📋 Full error traceback:")
        traceback.print_exc()
        return False

def main():
    """Main diagnostic and startup function"""
    print("🔧 Admin Dashboard - Diagnostic Startup")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Cannot start - missing dependencies")
        input("Press Enter to exit...")
        return
    
    # Check database
    if not check_database():
        print("\n⚠️ Database issues detected - will attempt to initialize")
    
    # Start dashboard
    start_admin_dashboard()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Admin dashboard stopped by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        traceback.print_exc()
        input("Press Enter to exit...")
