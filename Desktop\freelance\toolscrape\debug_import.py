#!/usr/bin/env python3
"""Minimal test script to debug the admin dashboard import issue"""

import sys
import os
import traceback

print("Starting import test...")
print(f"Python path: {sys.executable}")
print(f"Working directory: {os.getcwd()}")

try:
    # Check if the file exists
    if not os.path.exists('admin_dashboard.py'):
        print("❌ admin_dashboard.py file not found!")
        sys.exit(1)
    
    print("✅ admin_dashboard.py file exists")
    
    # Try to compile the file first
    print("Checking syntax...")
    with open('admin_dashboard.py', 'r') as f:
        code = f.read()
    
    try:
        compile(code, 'admin_dashboard.py', 'exec')
        print("✅ Syntax check passed")
    except SyntaxError as e:
        print(f"❌ Syntax error: {e}")
        print(f"Line {e.lineno}: {e.text}")
        sys.exit(1)
    
    # Try importing
    print("Attempting import...")
    import admin_dashboard
    print("✅ Import successful!")
    
    # Check for Flask app
    if hasattr(admin_dashboard, 'app'):
        app = admin_dashboard.app
        print(f"✅ Flask app found: {app}")
        
        # List all routes with their endpoints
        routes = {}
        for rule in app.url_map.iter_rules():
            routes[rule.endpoint] = rule.rule
        
        print(f"\nTotal routes: {len(routes)}")
        
        # Check for API scraping routes
        api_routes = {k: v for k, v in routes.items() if 'api' in k.lower()}
        print(f"API-related routes: {len(api_routes)}")
        for endpoint, rule in api_routes.items():
            print(f"  {endpoint}: {rule}")
        
        # Check for the specific route
        if 'admin_api_scraping' in routes:
            print(f"✅ admin_api_scraping found: {routes['admin_api_scraping']}")
        else:
            print("❌ admin_api_scraping NOT found!")
            
            # Look for similar routes
            similar = {k: v for k, v in routes.items() if 'scraping' in k.lower()}
            print(f"Similar routes with 'scraping': {similar}")
    else:
        print("❌ No Flask app found in module")

except Exception as e:
    print(f"❌ Error during import: {e}")
    print("\nFull traceback:")
    traceback.print_exc()

print("\nTest completed.")
