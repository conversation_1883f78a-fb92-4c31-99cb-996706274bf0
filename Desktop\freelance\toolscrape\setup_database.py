#!/usr/bin/env python3
"""Initialize and verify database for admin dashboard"""

import sqlite3
import os

def setup_database():
    """Setup the database with all required tables"""
    
    db_path = 'ai_tools_master.db'
    print(f"Setting up database: {db_path}")
    
    # Create connection
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Create admin users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT DEFAULT 'admin',
                last_login TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create scraping jobs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS scraping_jobs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                site_url TEXT NOT NULL,
                target_elements TEXT,
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completed_at TIMESTAMP,
                results_count INTEGER DEFAULT 0
            )
        ''')
        
        # Create AI tools table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_tools (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                category TEXT,
                url TEXT,
                free_tier BOOLEAN DEFAULT 0,
                verified BOOLEAN DEFAULT 0,
                published BOOLEAN DEFAULT 0,
                source TEXT DEFAULT 'manual',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create API scraping configs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_scraping_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_name TEXT UNIQUE NOT NULL,
                api_type TEXT NOT NULL,
                api_url TEXT NOT NULL,
                api_key TEXT,
                headers TEXT,
                parameters TEXT,
                data_mapping TEXT,
                is_active BOOLEAN DEFAULT 1,
                rate_limit INTEGER DEFAULT 60,
                last_run TIMESTAMP,
                total_runs INTEGER DEFAULT 0,
                success_rate REAL DEFAULT 0.0,
                created_by TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create API scraping results table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_scraping_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_id INTEGER,
                job_id INTEGER,
                status TEXT DEFAULT 'pending',
                tools_found INTEGER DEFAULT 0,
                response_data TEXT,
                error_message TEXT,
                execution_time REAL,
                started_at TIMESTAMP,
                completed_at TIMESTAMP,
                FOREIGN KEY (config_id) REFERENCES api_scraping_configs (id),
                FOREIGN KEY (job_id) REFERENCES scraping_jobs (id)
            )
        ''')
        
        # Create admin notifications table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                type TEXT DEFAULT 'info',
                is_read BOOLEAN DEFAULT 0,
                target_user TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Insert default admin user
        try:
            cursor.execute('''
                INSERT OR IGNORE INTO admin_users (username, password, role) 
                VALUES (?, ?, ?)
            ''', ('admin', 'admin123', 'super_admin'))
        except:
            pass  # User might already exist
        
        # Insert some sample data for testing
        cursor.execute('''
            INSERT OR IGNORE INTO ai_tools (name, description, category, url, free_tier) 
            VALUES (?, ?, ?, ?, ?)
        ''', ('ChatGPT', 'AI chatbot by OpenAI', 'Conversational AI', 'https://chat.openai.com', 1))
        
        cursor.execute('''
            INSERT OR IGNORE INTO ai_tools (name, description, category, url, free_tier) 
            VALUES (?, ?, ?, ?, ?)
        ''', ('Midjourney', 'AI image generation', 'Image Generation', 'https://midjourney.com', 0))
        
        conn.commit()
        print("✅ Database setup completed successfully")
        
        # Verify tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"📊 Tables created: {[table[0] for table in tables]}")
        
        # Check admin user
        cursor.execute("SELECT username FROM admin_users WHERE username='admin'")
        admin_user = cursor.fetchone()
        if admin_user:
            print("✅ Admin user exists")
        else:
            print("❌ Admin user not found")
        
        # Check sample data
        cursor.execute("SELECT COUNT(*) FROM ai_tools")
        tool_count = cursor.fetchone()[0]
        print(f"📊 AI tools in database: {tool_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False
        
    finally:
        conn.close()

if __name__ == "__main__":
    setup_database()
