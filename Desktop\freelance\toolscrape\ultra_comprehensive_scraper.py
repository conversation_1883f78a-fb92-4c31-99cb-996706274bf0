"""
Ultra-Comprehensive AI Tools Scraper - 5000+ Tools Target
This scraper is designed to collect 5000+ tools by:
1. Targeting high-volume sources with proven selectors
2. Using API endpoints where available
3. Implementing aggressive pagination and category scraping
4. Adding new high-volume sources
5. Better duplicate detection and data quality
"""

import asyncio
import aiohttp
import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
import sqlite3
import time
import re
import json
import logging
from fake_useragent import UserAgent
from urllib.parse import urljoin, urlparse, quote
from tqdm import tqdm
import pandas as pd
from concurrent.futures import ThreadPoolExecutor
import threading
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UltraComprehensiveAIToolsScraper:
    def __init__(self):
        self.ua = UserAgent()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        self.conn = sqlite3.connect('ai_tools_database.db', check_same_thread=False)
        self.cursor = self.conn.cursor()
        self.lock = threading.Lock()
        
        # Initialize database
        self.init_database()
        
        # Chrome options
        self.chrome_options = Options()
        self.chrome_options.add_argument('--headless')
        self.chrome_options.add_argument('--no-sandbox')
        self.chrome_options.add_argument('--disable-dev-shm-usage')
        self.chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        self.chrome_options.add_argument('--window-size=1920,1080')
        self.chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        self.chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Collected tools storage
        self.all_tools = []
        self.seen_names = set()
    
    def init_database(self):
        """Initialize the database table"""
        try:
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS ai_tools (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    website_url TEXT,
                    pricing_model TEXT,
                    category TEXT,
                    source_website TEXT,
                    source_page_url TEXT,
                    free_tier BOOLEAN,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(name, website_url)
                )
            ''')
            self.conn.commit()
        except Exception as e:
            logger.error(f"Database initialization error: {e}")
    
    def get_driver(self):
        """Get webdriver instance"""
        try:
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=self.chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            logger.error(f"Error creating driver: {e}")
            return None
    
    def clean_text(self, text):
        """Clean and normalize text"""
        if not text:
            return ""
        text = re.sub(r'\s+', ' ', text.strip())
        text = re.sub(r'[^\w\s\-\.\,\!\?\:\;\(\)\[\]\/\&\%\$\@\#\+\=]', '', text)
        return text[:500]
    
    def add_tool_to_collection(self, tool):
        """Add tool to collection with duplicate checking"""
        name_key = tool['name'].lower().strip()
        if name_key not in self.seen_names and len(name_key) > 2:
            # Additional quality checks
            if not any(skip in name_key for skip in ['read more', 'click here', 'learn more', 'view all', 'see more', 'load more']):
                self.all_tools.append(tool)
                self.seen_names.add(name_key)
                return True
        return False

    def scrape_opentools_comprehensive(self):
        """Comprehensive scraping of OpenTools.ai - Known to have 1000+ tools"""
        logger.info("🚀 SCRAPING: OpenTools.ai (High-volume target)")
        tools = []
        
        # OpenTools.ai has a searchable database - let's exploit it
        base_urls = [
            "https://opentools.ai/",
            "https://opentools.ai/tools",
            "https://opentools.ai/categories"
        ]
        
        # Known categories that have many tools
        categories = [
            "writing", "design", "development", "productivity", "marketing", "business",
            "education", "research", "analytics", "automation", "content", "social-media",
            "seo", "email", "finance", "hr", "sales", "customer-service", "project-management",
            "communication", "video", "audio", "image", "data", "ai-detection", "chatbots",
            "coding", "art", "music", "gaming", "health", "travel", "fitness", "lifestyle"
        ]
        
        for category in categories:
            try:
                url = f"https://opentools.ai/category/{category}"
                logger.info(f"Scraping OpenTools category: {category}")
                
                response = self.session.get(url, timeout=15)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # OpenTools.ai specific selectors
                    tool_selectors = [
                        '.tool-item', '.tool-card', '.ai-tool', '[data-tool]',
                        '.grid-item', '.card', '.tool', '.item',
                        'article', '.product', '.listing'
                    ]
                    
                    for selector in tool_selectors:
                        elements = soup.select(selector)
                        if elements:
                            for element in elements:
                                try:
                                    # Extract name
                                    name = ""
                                    name_selectors = ['h1', 'h2', 'h3', '.title', '.name', '.tool-name', 'a']
                                    for sel in name_selectors:
                                        name_elem = element.select_one(sel)
                                        if name_elem:
                                            name = self.clean_text(name_elem.get_text())
                                            if name and len(name) > 2:
                                                break
                                    
                                    if not name:
                                        continue
                                    
                                    # Extract description
                                    description = ""
                                    desc_selectors = ['p', '.description', '.excerpt', '.summary']
                                    for sel in desc_selectors:
                                        desc_elem = element.select_one(sel)
                                        if desc_elem:
                                            description = self.clean_text(desc_elem.get_text())
                                            break
                                    
                                    # Extract URL
                                    website_url = ""
                                    link = element.find('a', href=True)
                                    if link:
                                        href = link['href']
                                        if href.startswith('http'):
                                            website_url = href
                                        elif href.startswith('/'):
                                            website_url = urljoin("https://opentools.ai", href)
                                    
                                    # Pricing detection
                                    element_text = element.get_text().lower()
                                    pricing = "Unknown"
                                    if any(word in element_text for word in ['free', 'gratis', '$0']):
                                        pricing = "Free"
                                    elif any(word in element_text for word in ['premium', 'paid', '$', 'subscription']):
                                        pricing = "Premium"
                                    elif 'freemium' in element_text:
                                        pricing = "Freemium"
                                    
                                    tool = {
                                        'name': name,
                                        'description': description,
                                        'website_url': website_url,
                                        'pricing_model': pricing,
                                        'category': category.replace('-', ' ').title(),
                                        'source_website': 'opentools.ai',
                                        'source_page_url': url,
                                        'free_tier': pricing in ["Free", "Freemium"]
                                    }
                                    
                                    if self.add_tool_to_collection(tool):
                                        tools.append(tool)
                                        
                                except Exception as e:
                                    continue
                            
                            if elements:
                                break  # Found tools with this selector, no need to try others
                
                time.sleep(1)  # Rate limiting
                
            except Exception as e:
                logger.error(f"Error scraping OpenTools category {category}: {e}")
                continue
        
        logger.info(f"🎯 OpenTools.ai: {len(tools)} tools collected")
        return tools
    
    def scrape_futurtools_aggressive(self):
        """Aggressive scraping of FutureTools.io using Selenium"""
        logger.info("🚀 SCRAPING: FutureTools.io (High-volume target)")
        tools = []
        
        driver = self.get_driver()
        if not driver:
            return tools
        
        try:
            # FutureTools has infinite scroll - we need to trigger it
            driver.get("https://www.futuretools.io/tools")
            time.sleep(3)
            
            # Scroll to load more tools
            last_height = driver.execute_script("return document.body.scrollHeight")
            scroll_attempts = 0
            max_scrolls = 50  # Aggressive scrolling
            
            while scroll_attempts < max_scrolls:
                # Scroll down
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                
                # Check if new content loaded
                new_height = driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    # Try clicking "Load More" button if it exists
                    try:
                        load_more = driver.find_element(By.XPATH, "//button[contains(text(), 'Load') or contains(text(), 'More') or contains(text(), 'Show')]")
                        driver.execute_script("arguments[0].click();", load_more)
                        time.sleep(3)
                        new_height = driver.execute_script("return document.body.scrollHeight")
                    except:
                        break
                
                if new_height == last_height:
                    break
                    
                last_height = new_height
                scroll_attempts += 1
                logger.info(f"Scrolled {scroll_attempts} times, page height: {new_height}")
            
            # Now extract all tools
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            
            # FutureTools specific selectors
            tool_selectors = [
                '[data-testid="tool-card"]', '.tool-card', '.tool-item',
                '.grid > div', '.flex.flex-col', 'article', '.card'
            ]
            
            for selector in tool_selectors:
                elements = soup.select(selector)
                if elements:
                    logger.info(f"Found {len(elements)} elements with selector: {selector}")
                    
                    for element in elements:
                        try:
                            # Extract name
                            name = ""
                            name_selectors = ['h2', 'h3', '.font-bold', '.text-lg', '.text-xl', 'a']
                            for sel in name_selectors:
                                name_elem = element.select_one(sel)
                                if name_elem:
                                    name = self.clean_text(name_elem.get_text())
                                    if name and len(name) > 2:
                                        break
                            
                            if not name:
                                continue
                            
                            # Extract description
                            description = ""
                            desc_selectors = ['p', '.text-gray', '.opacity-', '.text-sm']
                            for sel in desc_selectors:
                                desc_elem = element.select_one(sel)
                                if desc_elem:
                                    desc_text = desc_elem.get_text()
                                    if desc_text and len(desc_text) > 10:
                                        description = self.clean_text(desc_text)
                                        break
                            
                            # Extract URL
                            website_url = ""
                            link = element.find('a', href=True)
                            if link:
                                href = link['href']
                                if href.startswith('http'):
                                    website_url = href
                                elif href.startswith('/'):
                                    website_url = urljoin("https://www.futuretools.io", href)
                            
                            # Extract category and pricing
                            element_text = element.get_text().lower()
                            
                            # Category detection
                            category = "AI Tools"
                            if 'writing' in element_text:
                                category = "Writing"
                            elif 'design' in element_text:
                                category = "Design"
                            elif 'code' in element_text or 'develop' in element_text:
                                category = "Development"
                            elif 'video' in element_text:
                                category = "Video"
                            elif 'image' in element_text or 'photo' in element_text:
                                category = "Image"
                            
                            # Pricing detection
                            pricing = "Unknown"
                            if any(word in element_text for word in ['free', 'gratis', '$0']):
                                pricing = "Free"
                            elif any(word in element_text for word in ['premium', 'paid', 'subscription']):
                                pricing = "Premium"
                            elif 'freemium' in element_text:
                                pricing = "Freemium"
                            
                            tool = {
                                'name': name,
                                'description': description,
                                'website_url': website_url,
                                'pricing_model': pricing,
                                'category': category,
                                'source_website': 'futuretools.io',
                                'source_page_url': 'https://www.futuretools.io/tools',
                                'free_tier': pricing in ["Free", "Freemium"]
                            }
                            
                            if self.add_tool_to_collection(tool):
                                tools.append(tool)
                                
                        except Exception as e:
                            continue
                    
                    break  # Found tools with this selector
            
        except Exception as e:
            logger.error(f"Error scraping FutureTools: {e}")
        finally:
            driver.quit()
        
        logger.info(f"🎯 FutureTools.io: {len(tools)} tools collected")
        return tools
    
    def scrape_aitoptools_massive(self):
        """Scrape AI Top Tools - Another high-volume source"""
        logger.info("🚀 SCRAPING: AITopTools (High-volume target)")
        tools = []
        
        base_urls = [
            "https://aitoptools.com/",
            "https://aitoptools.com/tools",
            "https://aitoptools.com/categories"
        ]
        
        # Scrape main pages with pagination
        for page in range(1, 21):  # Check up to 20 pages
            try:
                url = f"https://aitoptools.com/tools?page={page}"
                response = self.session.get(url, timeout=15)
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Check if page has tools
                    tool_elements = soup.select('.tool, .card, .item, .grid-item, article, .product')
                    
                    if not tool_elements:
                        break  # No more tools
                    
                    for element in tool_elements:
                        try:
                            # Extract tool data
                            name = ""
                            for sel in ['h1', 'h2', 'h3', '.title', '.name']:
                                name_elem = element.select_one(sel)
                                if name_elem:
                                    name = self.clean_text(name_elem.get_text())
                                    break
                            
                            if not name:
                                continue
                            
                            description = ""
                            for sel in ['p', '.description', '.excerpt']:
                                desc_elem = element.select_one(sel)
                                if desc_elem:
                                    description = self.clean_text(desc_elem.get_text())
                                    break
                            
                            website_url = ""
                            link = element.find('a', href=True)
                            if link:
                                href = link['href']
                                if href.startswith('http'):
                                    website_url = href
                            
                            tool = {
                                'name': name,
                                'description': description,
                                'website_url': website_url,
                                'pricing_model': "Unknown",
                                'category': "AI Tools",
                                'source_website': 'aitoptools.com',
                                'source_page_url': url,
                                'free_tier': False
                            }
                            
                            if self.add_tool_to_collection(tool):
                                tools.append(tool)
                                
                        except Exception as e:
                            continue
                    
                    logger.info(f"Page {page}: {len(tool_elements)} tools found")
                    time.sleep(1)
                    
                else:
                    break
                    
            except Exception as e:
                logger.error(f"Error scraping AITopTools page {page}: {e}")
                break
        
        logger.info(f"🎯 AITopTools: {len(tools)} tools collected")
        return tools
    
    def scrape_theresanaiforthat(self):
        """Scrape There's An AI For That - Major source"""
        logger.info("🚀 SCRAPING: TheresAnAIForThat (High-volume target)")
        tools = []
        
        # This site has a lot of tools, let's use pagination aggressively
        for page in range(1, 50):  # Check many pages
            try:
                url = f"https://theresanaiforthat.com/tools/?page={page}"
                response = self.session.get(url, timeout=15)
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Site-specific selectors
                    tool_elements = soup.select('.tool-card, .ai-tool, .tool-item, .grid-item, article')
                    
                    if not tool_elements:
                        # Try alternative selectors
                        tool_elements = soup.select('[data-tool], .card, .item')
                    
                    if not tool_elements:
                        break
                    
                    for element in tool_elements:
                        try:
                            name = ""
                            for sel in ['h2', 'h3', '.tool-name', '.title']:
                                name_elem = element.select_one(sel)
                                if name_elem:
                                    name = self.clean_text(name_elem.get_text())
                                    break
                            
                            if not name:
                                continue
                            
                            description = ""
                            for sel in ['p', '.description', '.summary']:
                                desc_elem = element.select_one(sel)
                                if desc_elem:
                                    description = self.clean_text(desc_elem.get_text())
                                    break
                            
                            website_url = ""
                            link = element.find('a', href=True)
                            if link:
                                href = link['href']
                                if href.startswith('http'):
                                    website_url = href
                            
                            tool = {
                                'name': name,
                                'description': description,
                                'website_url': website_url,
                                'pricing_model': "Unknown",
                                'category': "AI Tools",
                                'source_website': 'theresanaiforthat.com',
                                'source_page_url': url,
                                'free_tier': False
                            }
                            
                            if self.add_tool_to_collection(tool):
                                tools.append(tool)
                                
                        except Exception as e:
                            continue
                    
                    logger.info(f"TAAFT Page {page}: {len(tool_elements)} tools found")
                    time.sleep(1)
                    
                else:
                    break
                    
            except Exception as e:
                logger.error(f"Error scraping TAAFT page {page}: {e}")
                break
        
        logger.info(f"🎯 TheresAnAIForThat: {len(tools)} tools collected")
        return tools
    
    def scrape_additional_high_volume_sources(self):
        """Scrape additional high-volume sources"""
        logger.info("🚀 SCRAPING: Additional high-volume sources")
        tools = []
        
        sources = [
            {
                'name': 'AI Tool Directory',
                'urls': ['https://aitool.directory/', 'https://aitool.directory/tools'],
                'selectors': ['.tool-card', '.ai-tool', '.directory-item']
            },
            {
                'name': 'AI Toolbox',
                'urls': ['https://aitoolbox.co/', 'https://aitoolbox.co/tools'],
                'selectors': ['.tool-item', '.toolbox-item', '.ai-tool']
            },
            {
                'name': 'AI Collection',
                'urls': ['https://aicollection.com/', 'https://aicollection.com/tools'],
                'selectors': ['.collection-item', '.tool', '.ai-tool']
            }
        ]
        
        for source in sources:
            for url in source['urls']:
                try:
                    response = self.session.get(url, timeout=15)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        for selector in source['selectors']:
                            elements = soup.select(selector)
                            if elements:
                                for element in elements:
                                    try:
                                        name = ""
                                        for sel in ['h1', 'h2', 'h3', '.title', '.name']:
                                            name_elem = element.select_one(sel)
                                            if name_elem:
                                                name = self.clean_text(name_elem.get_text())
                                                break
                                        
                                        if not name:
                                            continue
                                        
                                        description = ""
                                        for sel in ['p', '.description', '.excerpt']:
                                            desc_elem = element.select_one(sel)
                                            if desc_elem:
                                                description = self.clean_text(desc_elem.get_text())
                                                break
                                        
                                        website_url = ""
                                        link = element.find('a', href=True)
                                        if link:
                                            href = link['href']
                                            if href.startswith('http'):
                                                website_url = href
                                        
                                        tool = {
                                            'name': name,
                                            'description': description,
                                            'website_url': website_url,
                                            'pricing_model': "Unknown",
                                            'category': "AI Tools",
                                            'source_website': source['name'].lower().replace(' ', ''),
                                            'source_page_url': url,
                                            'free_tier': False
                                        }
                                        
                                        if self.add_tool_to_collection(tool):
                                            tools.append(tool)
                                            
                                    except Exception as e:
                                        continue
                                break
                    
                    time.sleep(1)
                    
                except Exception as e:
                    logger.error(f"Error scraping {url}: {e}")
                    continue
        
        logger.info(f"🎯 Additional sources: {len(tools)} tools collected")
        return tools
    
    def save_tools_to_database(self, tools):
        """Save tools to database with proper duplicate handling"""
        saved_count = 0
        
        with self.lock:
            for tool in tools:
                try:
                    self.cursor.execute('''
                        INSERT OR IGNORE INTO ai_tools 
                        (name, description, website_url, pricing_model, category, 
                         source_website, source_page_url, free_tier)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        tool['name'],
                        tool['description'],
                        tool['website_url'],
                        tool['pricing_model'],
                        tool['category'],
                        tool['source_website'],
                        tool['source_page_url'],
                        tool['free_tier']
                    ))
                    
                    if self.cursor.rowcount > 0:
                        saved_count += 1
                        
                except Exception as e:
                    logger.error(f"Error saving tool {tool['name']}: {e}")
                    continue
            
            self.conn.commit()
        
        logger.info(f"💾 Saved {saved_count} new tools to database")
        return saved_count
    
    def run_comprehensive_scraping(self):
        """Run comprehensive scraping targeting 5000+ tools"""
        logger.info("🚀🚀🚀 STARTING ULTRA-COMPREHENSIVE SCRAPING - TARGET: 5000+ TOOLS 🚀🚀🚀")
        
        start_time = time.time()
        
        # Get initial count
        self.cursor.execute('SELECT COUNT(*) FROM ai_tools')
        initial_count = self.cursor.fetchone()[0]
        logger.info(f"Starting with {initial_count} tools in database")
        
        # List of scraping functions to run
        scraping_functions = [
            self.scrape_opentools_comprehensive,
            self.scrape_futurtools_aggressive,
            self.scrape_aitoptools_massive,
            self.scrape_theresanaiforthat,
            self.scrape_additional_high_volume_sources
        ]
        
        total_collected = 0
        
        for func in scraping_functions:
            try:
                logger.info(f"\n{'='*60}")
                logger.info(f"Running: {func.__name__}")
                logger.info(f"{'='*60}")
                
                tools = func()
                if tools:
                    saved = self.save_tools_to_database(tools)
                    total_collected += saved
                    
                    # Get current count
                    self.cursor.execute('SELECT COUNT(*) FROM ai_tools')
                    current_count = self.cursor.fetchone()[0]
                    
                    logger.info(f"✅ {func.__name__}: +{saved} tools (Total in DB: {current_count})")
                else:
                    logger.warning(f"❌ {func.__name__}: No tools collected")
                
            except Exception as e:
                logger.error(f"❌ Error in {func.__name__}: {e}")
                continue
        
        # Final summary
        self.cursor.execute('SELECT COUNT(*) FROM ai_tools')
        final_count = self.cursor.fetchone()[0]
        
        elapsed_time = time.time() - start_time
        
        logger.info(f"\n{'='*80}")
        logger.info(f"🏁 ULTRA-COMPREHENSIVE SCRAPING COMPLETED 🏁")
        logger.info(f"{'='*80}")
        logger.info(f"⏱️  Time elapsed: {elapsed_time/60:.2f} minutes")
        logger.info(f"📊 Initial tools: {initial_count}")
        logger.info(f"📊 Final tools: {final_count}")
        logger.info(f"📊 New tools added: {final_count - initial_count}")
        logger.info(f"🎯 Target achieved: {'✅ YES' if final_count >= 5000 else '❌ NO'}")
        
        # Export results
        self.export_results()
        
        return final_count
    
    def export_results(self):
        """Export all tools to CSV"""
        try:
            # Export to CSV
            df = pd.read_sql_query('SELECT * FROM ai_tools ORDER BY created_at DESC', self.conn)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"ultra_comprehensive_scrape_results_{timestamp}.csv"
            df.to_csv(filename, index=False)
            
            logger.info(f"📁 Results exported to: {filename}")
            logger.info(f"📊 Total rows exported: {len(df)}")
            
            # Show summary by source
            source_summary = df['source_website'].value_counts()
            logger.info(f"\n📈 Tools by source:")
            for source, count in source_summary.items():
                logger.info(f"  {source}: {count} tools")
                
        except Exception as e:
            logger.error(f"Error exporting results: {e}")

def main():
    scraper = UltraComprehensiveAIToolsScraper()
    final_count = scraper.run_comprehensive_scraping()
    
    if final_count >= 5000:
        print(f"\n🎉🎉🎉 SUCCESS! Collected {final_count} tools - TARGET ACHIEVED! 🎉🎉🎉")
    else:
        print(f"\n⚠️ Collected {final_count} tools. Need {5000 - final_count} more to reach target.")
    
    scraper.conn.close()

if __name__ == "__main__":
    main()
