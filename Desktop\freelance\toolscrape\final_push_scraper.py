"""
Final Push Scraper - Get to 5000+ Tools
This scraper targets remaining sources and uses aggressive techniques to reach 5000+ tools
"""

import requests
from bs4 import BeautifulSoup
import sqlite3
import time
import logging
from fake_useragent import UserAgent
import pandas as pd
from datetime import datetime
import re
import json
from urllib.parse import urljoin, urlparse, quote
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import random

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalPushScraper:
    def __init__(self):
        self.ua = UserAgent()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache'
        })
        
        # Use existing database
        self.conn = sqlite3.connect('ai_tools_database_fixed.db', check_same_thread=False)
        self.cursor = self.conn.cursor()
        
        self.all_tools = []
        self.seen_names = set()
        self.lock = threading.Lock()
        
        # Load existing tools
        self.load_existing_tools()
    
    def load_existing_tools(self):
        """Load existing tools for deduplication"""
        try:
            self.cursor.execute('SELECT name FROM ai_tools')
            existing = self.cursor.fetchall()
            self.seen_names = {name[0].lower().strip() for name in existing}
            logger.info(f"Loaded {len(self.seen_names)} existing tools for deduplication")
        except Exception as e:
            logger.error(f"Error loading existing tools: {e}")
    
    def clean_text(self, text):
        """Clean and normalize text"""
        if not text:
            return ""
        text = re.sub(r'\\s+', ' ', text.strip())
        text = re.sub(r'[^\\w\\s\\-\\.\\,\\!\\?\\:\\;\\(\\)\\[\\]\\/\\&\\%\\$\\@\\#\\+\\=]', '', text)
        return text[:500]
    
    def add_tool_to_collection(self, tool):
        """Add tool to collection with duplicate checking"""
        name_key = tool['name'].lower().strip()
        if name_key and len(name_key) > 2:
            skip_keywords = ['read more', 'click here', 'learn more', 'view all', 'see more', 'load more', 'show more', 'continue reading', 'more info']
            if not any(skip in name_key for skip in skip_keywords):
                with self.lock:
                    if name_key not in self.seen_names:
                        self.all_tools.append(tool)
                        self.seen_names.add(name_key)
                        return True
        return False
    
    def extract_tool_data(self, element, source_name, source_url):
        """Extract tool data from HTML element"""
        try:
            # Extract name with maximum strategies
            name = ""
            
            # Strategy 1: Headings
            for tag in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                name_elem = element.find(tag)
                if name_elem:
                    name = self.clean_text(name_elem.get_text())
                    if name and len(name) > 2:
                        break
            
            # Strategy 2: Classes
            if not name:
                name_selectors = ['.title', '.name', '.tool-name', '.product-name', '.ai-tool-name', '.card-title', '.item-title']
                for sel in name_selectors:
                    name_elem = element.select_one(sel)
                    if name_elem:
                        name = self.clean_text(name_elem.get_text())
                        if name and len(name) > 2:
                            break
            
            # Strategy 3: Links and attributes
            if not name:
                links = element.find_all('a')
                for link in links:
                    if link.get('title'):
                        name = self.clean_text(link.get('title'))
                        if name and len(name) > 2:
                            break
                    elif link.get_text().strip():
                        link_text = self.clean_text(link.get_text())
                        if link_text and len(link_text) > 2 and len(link_text) < 100:
                            name = link_text
                            break
            
            # Strategy 4: Text content of spans, divs, etc.
            if not name:
                for tag in ['span', 'div', 'strong', 'b']:
                    elem = element.find(tag)
                    if elem:
                        text = self.clean_text(elem.get_text())
                        if text and 3 <= len(text) <= 50:
                            name = text
                            break
            
            if not name or len(name) < 3:
                return None
            
            # Extract description
            description = ""
            desc_selectors = ['p', '.description', '.excerpt', '.summary', '.content', '.desc', '.details', '.card-text']
            for sel in desc_selectors:
                desc_elem = element.select_one(sel)
                if desc_elem:
                    desc_text = desc_elem.get_text()
                    if desc_text and len(desc_text) > 10:
                        description = self.clean_text(desc_text)
                        break
            
            # Extract URL
            website_url = ""
            link = element.find('a', href=True)
            if link:
                href = link['href']
                if href.startswith('http'):
                    website_url = href
                elif href.startswith('/'):
                    parsed_source = urlparse(source_url)
                    website_url = f"{parsed_source.scheme}://{parsed_source.netloc}{href}"
            
            # Enhanced category and pricing detection
            element_text = element.get_text().lower()
            
            # Category detection
            category = "AI Tools"
            category_map = {
                'Writing': ['writing', 'content', 'text', 'copy', 'blog', 'article', 'copywriting'],
                'Design': ['design', 'graphic', 'ui', 'ux', 'visual', 'logo', 'creative'],
                'Development': ['code', 'develop', 'program', 'api', 'github', 'software', 'coding'],
                'Video': ['video', 'movie', 'film', 'youtube', 'streaming', 'editing'],
                'Image': ['image', 'photo', 'picture', 'visual', 'art', 'graphics', 'generator'],
                'Audio': ['audio', 'music', 'sound', 'voice', 'podcast', 'speech'],
                'Marketing': ['marketing', 'seo', 'ads', 'social media', 'campaign', 'promotion'],
                'Business': ['business', 'finance', 'crm', 'sales', 'analytics', 'management'],
                'Productivity': ['productivity', 'automation', 'workflow', 'task', 'efficiency'],
                'Education': ['education', 'learning', 'teach', 'course', 'study', 'training'],
                'Data': ['data', 'analysis', 'visualization', 'dashboard', 'statistics'],
                'Communication': ['chat', 'messaging', 'communication', 'collaboration']
            }
            
            for cat_name, keywords in category_map.items():
                if any(keyword in element_text for keyword in keywords):
                    category = cat_name
                    break
            
            # Pricing detection
            pricing = "Unknown"
            if any(word in element_text for word in ['free', 'gratis', '$0', 'no cost', 'open source', 'opensource']):
                pricing = "Free"
            elif any(word in element_text for word in ['premium', 'paid', '$', 'subscription', 'pro', 'price', 'buy']):
                pricing = "Premium"
            elif 'freemium' in element_text:
                pricing = "Freemium"
            
            return {
                'name': name,
                'description': description,
                'website_url': website_url,
                'pricing_model': pricing,
                'category': category,
                'source_website': source_name.lower().replace(' ', '').replace('.', ''),
                'source_page_url': source_url,
                'free_tier': pricing in ["Free", "Freemium"]
            }
            
        except Exception as e:
            return None
    
    def get_massive_additional_sources(self):
        """Get additional sources not covered in previous scraping"""
        return [
            # Additional AI tool directories
            ('https://www.aitools.com/', 'AI Tools Com'),
            ('https://aitoolsclub.com/', 'AI Tools Club'),
            ('https://www.aitoolshunt.com/', 'AI Tools Hunt'),
            ('https://aitoolsapi.com/', 'AI Tools API'),
            ('https://www.aitoolsmarket.com/', 'AI Tools Market'),
            ('https://aitools.zone/', 'AI Tools Zone'),
            ('https://www.aitoolscollection.com/', 'AI Tools Collection'),
            
            # Specific AI category sites
            ('https://www.aiwritingtools.com/', 'AI Writing Tools'),
            ('https://www.aidesigntools.com/', 'AI Design Tools'),
            ('https://www.aiimagetools.com/', 'AI Image Tools'),
            ('https://www.aivideotools.com/', 'AI Video Tools'),
            ('https://www.aiaudiotools.com/', 'AI Audio Tools'),
            
            # Blog/Review sites
            ('https://www.airevolution.net/', 'AI Revolution'),
            ('https://www.theverge.com/ai-artificial-intelligence', 'The Verge AI'),
            ('https://techcrunch.com/category/artificial-intelligence/', 'TechCrunch AI'),
            ('https://www.zdnet.com/topic/artificial-intelligence/', 'ZDNet AI'),
            ('https://www.wired.com/tag/artificial-intelligence/', 'Wired AI'),
            
            # Additional tool aggregators
            ('https://stackshare.io/artificial-intelligence', 'StackShare AI'),
            ('https://www.g2.com/categories/ai-tools', 'G2 AI Tools'),
            ('https://capterra.com/artificial-intelligence-software/', 'Capterra AI'),
            ('https://www.softwareadvice.com/ai/', 'Software Advice AI'),
            
            # Community/Forum sites
            ('https://www.indiehackers.com/search?q=ai', 'Indie Hackers AI'),
            ('https://news.ycombinator.com/item?id=ai', 'Hacker News AI'),
            
            # More specific directories
            ('https://www.startupbuffer.com/ai-tools', 'Startup Buffer AI'),
            ('https://www.geekwire.com/tag/artificial-intelligence/', 'GeekWire AI'),
            ('https://sifted.eu/rankings/ai-tools', 'Sifted AI'),
            
            # International sources
            ('https://www.les-ai.com/', 'Les AI (French)'),
            ('https://www.ki-tools.de/', 'KI Tools (German)'),
            ('https://www.aitools.es/', 'AI Tools ES (Spanish)'),
            
            # Startup directories with AI focus
            ('https://betalist.com/markets/artificial-intelligence', 'BetaList AI'),
            ('https://www.launchingnext.com/tag/ai/', 'Launching Next AI'),
            ('https://rocketship.fm/artificial-intelligence/', 'Rocketship AI'),
        ]
    
    def scrape_ultra_aggressive_site(self, url, site_name, max_pages=100):
        """Ultra-aggressive site scraping with maximum selectors"""
        logger.info(f"🚀 ULTRA-AGGRESSIVE: {site_name}")
        tools = []
        
        # Ultra-comprehensive selector list
        selectors = [
            # Tool-specific
            '[class*="tool"]', '[class*="ai"]', '[class*="item"]', '[class*="card"]', 
            '[class*="product"]', '[class*="listing"]', '[class*="directory"]',
            '[id*="tool"]', '[id*="ai"]', '[id*="item"]', '[id*="card"]',
            
            # Content structures
            'article', '.grid > div', '.row > div', '.col > div', '.flex > div',
            '.list-item', '.grid-item', '.card-item', '.directory-item',
            
            # CMS/Framework specific
            '.post', '.entry', '.content-item', '.block', '.module',
            '.component', '.widget', '.panel', '.section',
            
            # Generic containers
            'li', 'div[class]', 'section > div', 'main > div',
            '.container > div', '.wrapper > div', '.content > div',
            
            # Table structures
            'tr', 'td', 'tbody > tr',
            
            # List structures
            'ul > li', 'ol > li', 'dl > dt', 'dl > dd',
            
            # Bootstrap/Framework classes
            '.col-', '.card-', '.list-group-item', '.media',
            '.thumbnail', '.well', '.jumbotron'
        ]
        
        for page in range(1, max_pages + 1):
            tools_found_this_page = 0
            
            # Try multiple pagination patterns
            page_urls = [
                f"{url}?page={page}",
                f"{url}?p={page}",
                f"{url}/page/{page}",
                f"{url}/page/{page}/",
                f"{url}?paged={page}",
                f"{url}?sf_paged={page}",
                f"{url}?offset={page*20}",
                f"{url}?start={page*10}",
                f"{url}#page={page}",
                f"{url}?pagenum={page}",
                f"{url}?pg={page}"
            ]
            
            for page_url in page_urls:
                try:
                    # Random delay and user agent rotation
                    time.sleep(random.uniform(0.3, 1.5))
                    self.session.headers['User-Agent'] = self.ua.random
                    
                    response = self.session.get(page_url, timeout=20)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        # Try each selector
                        for selector in selectors:
                            try:
                                elements = soup.select(selector)
                                
                                # Skip if too few or too many elements
                                if len(elements) < 2 or len(elements) > 200:
                                    continue
                                
                                page_tools = 0
                                for elem in elements[:100]:  # Process up to 100 elements
                                    tool = self.extract_tool_data(elem, site_name, page_url)
                                    if tool and self.add_tool_to_collection(tool):
                                        tools.append(tool)
                                        page_tools += 1
                                
                                if page_tools > 0:
                                    tools_found_this_page += page_tools
                                    logger.info(f"📄 {site_name} Page {page}: {page_tools} tools (selector: {selector[:20]})")
                                    break  # Found tools, stop trying selectors
                                    
                            except Exception as e:
                                continue
                        
                        if tools_found_this_page > 0:
                            break  # Found tools, stop trying URL patterns
                            
                except Exception as e:
                    continue
            
            # Stop conditions
            if tools_found_this_page == 0:
                if page <= 2:  # Keep trying for first 2 pages
                    continue
                else:
                    logger.info(f"📄 {site_name}: No tools found on page {page}, stopping")
                    break
        
        logger.info(f"✅ {site_name}: {len(tools)} total tools collected")
        return tools
    
    def scrape_alternative_github_strategy(self):
        """Alternative GitHub scraping strategy using search API"""
        logger.info("🎯 ALTERNATIVE GITHUB STRATEGY")
        tools = []
        
        # GitHub search queries for AI tools
        search_queries = [
            'ai tools awesome list',
            'artificial intelligence tools',
            'machine learning tools',
            'ai applications',
            'gpt tools',
            'openai tools',
            'chatgpt tools',
            'ai generators',
            'neural network tools'
        ]
        
        for query in search_queries:
            try:
                # Search for repositories
                search_url = f"https://api.github.com/search/repositories?q={query.replace(' ', '+')}&sort=stars&order=desc&per_page=30"
                
                response = self.session.get(search_url, timeout=15)
                if response.status_code == 200:
                    data = response.json()
                    
                    for repo in data.get('items', []):
                        try:
                            repo_name = repo.get('name', '')
                            repo_description = repo.get('description', '')
                            repo_url = repo.get('html_url', '')
                            
                            if any(keyword in repo_name.lower() for keyword in ['ai', 'tool', 'awesome', 'list']):
                                tool = {
                                    'name': self.clean_text(repo_name),
                                    'description': self.clean_text(repo_description) if repo_description else f"GitHub repository: {repo_name}",
                                    'website_url': repo_url,
                                    'pricing_model': 'Free',
                                    'category': 'Development',
                                    'source_website': 'github-search',
                                    'source_page_url': search_url,
                                    'free_tier': True
                                }
                                
                                if self.add_tool_to_collection(tool):
                                    tools.append(tool)
                                    
                        except Exception as e:
                            continue
                    
                    logger.info(f"📄 GitHub search '{query}': {len([t for t in tools if query.replace(' ', '+') in t['source_page_url']])} tools")
                
                time.sleep(1)  # Rate limiting
                
            except Exception as e:
                logger.error(f"Error searching GitHub for '{query}': {e}")
                continue
        
        logger.info(f"✅ GITHUB SEARCH: {len(tools)} tools collected")
        return tools
    
    def scrape_reddit_alternative_strategy(self):
        """Alternative Reddit scraping strategy"""
        logger.info("🎯 REDDIT ALTERNATIVE STRATEGY")
        tools = []
        
        # Reddit subreddits and search terms
        reddit_sources = [
            'https://www.reddit.com/r/MachineLearning/search/?q=tools&restrict_sr=1&sort=top',
            'https://www.reddit.com/r/artificial/search/?q=tools&restrict_sr=1&sort=top',
            'https://www.reddit.com/r/MLTools/hot/',
            'https://www.reddit.com/r/ArtificialIntelligence/search/?q=tools&restrict_sr=1',
            'https://www.reddit.com/r/ChatGPT/search/?q=tools&restrict_sr=1',
            'https://www.reddit.com/r/OpenAI/search/?q=tools&restrict_sr=1'
        ]
        
        for reddit_url in reddit_sources:
            try:
                response = self.session.get(reddit_url, timeout=15)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Reddit-specific selectors
                    selectors = ['[data-testid="post-container"]', '.Post', '.thing', '.entry']
                    
                    for selector in selectors:
                        elements = soup.select(selector)
                        if elements:
                            for elem in elements[:20]:  # Limit per page
                                try:
                                    # Extract title/name
                                    title_elem = elem.find(['h3', 'h2', 'h1', '.title'])
                                    if title_elem:
                                        title = self.clean_text(title_elem.get_text())
                                        
                                        # Only include if it seems tool-related
                                        if any(keyword in title.lower() for keyword in ['tool', 'ai', 'generator', 'app', 'platform']):
                                            tool = {
                                                'name': title[:100],  # Truncate long titles
                                                'description': f"AI tool mentioned on Reddit: {title}",
                                                'website_url': '',
                                                'pricing_model': 'Unknown',
                                                'category': 'AI Tools',
                                                'source_website': 'reddit-search',
                                                'source_page_url': reddit_url,
                                                'free_tier': False
                                            }
                                            
                                            if self.add_tool_to_collection(tool):
                                                tools.append(tool)
                                                
                                except Exception as e:
                                    continue
                            break
                
                time.sleep(2)  # Rate limiting
                
            except Exception as e:
                logger.error(f"Error scraping Reddit {reddit_url}: {e}")
                continue
        
        logger.info(f"✅ REDDIT SEARCH: {len(tools)} tools collected")
        return tools
    
    def parallel_scrape_final_sources(self, sites_list, max_workers=10):
        """Parallel scraping of final sources"""
        logger.info(f"🚀 FINAL PARALLEL SCRAPING: {len(sites_list)} sites with {max_workers} workers")
        
        all_tools = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all scraping tasks
            future_to_site = {
                executor.submit(self.scrape_ultra_aggressive_site, url, name, 75): (url, name) 
                for url, name in sites_list
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_site):
                url, name = future_to_site[future]
                try:
                    tools = future.result(timeout=600)  # 10 minute timeout per site
                    if tools:
                        all_tools.extend(tools)
                        logger.info(f"✅ {name}: {len(tools)} tools collected")
                    else:
                        logger.warning(f"❌ {name}: No tools collected")
                        
                except Exception as e:
                    logger.error(f"❌ Error scraping {name}: {e}")
        
        return all_tools
    
    def save_tools_to_database(self, tools):
        """Save tools to database"""
        saved_count = 0
        
        with self.lock:
            for tool in tools:
                try:
                    self.cursor.execute('''
                        INSERT OR IGNORE INTO ai_tools 
                        (name, description, website_url, pricing_model, category, 
                         source_website, source_page_url, free_tier)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        tool['name'],
                        tool['description'],
                        tool['website_url'],
                        tool['pricing_model'],
                        tool['category'],
                        tool['source_website'],
                        tool['source_page_url'],
                        tool['free_tier']
                    ))
                    
                    if self.cursor.rowcount > 0:
                        saved_count += 1
                        
                except Exception as e:
                    logger.error(f"Error saving tool {tool.get('name', 'Unknown')}: {e}")
                    continue
            
            self.conn.commit()
        
        logger.info(f"💾 Saved {saved_count} new tools to database")
        return saved_count
    
    def run_final_push(self):
        """Run final push to reach 5000+ tools"""
        logger.info("🚀🚀🚀 FINAL PUSH TO 5000+ TOOLS 🚀🚀🚀")
        
        start_time = time.time()
        
        # Get initial count
        self.cursor.execute('SELECT COUNT(*) FROM ai_tools')
        initial_count = self.cursor.fetchone()[0]
        logger.info(f"Starting with {initial_count} tools in database")
        logger.info(f"Need {5000 - initial_count} more tools to reach 5000")
        
        all_collected_tools = []
        
        # Strategy 1: Scrape additional sources
        logger.info("\\n" + "="*60)
        logger.info("STRATEGY 1: ADDITIONAL SOURCES")
        logger.info("="*60)
        
        additional_sites = self.get_massive_additional_sources()
        parallel_tools = self.parallel_scrape_final_sources(additional_sites, max_workers=12)
        all_collected_tools.extend(parallel_tools)
        
        # Save intermediate results
        if all_collected_tools:
            saved_count = self.save_tools_to_database(all_collected_tools)
            logger.info(f"💾 Intermediate save: {saved_count} tools saved")
        
        # Strategy 2: Alternative GitHub strategy
        logger.info("\\n" + "="*60)
        logger.info("STRATEGY 2: GITHUB SEARCH API")
        logger.info("="*60)
        
        github_tools = self.scrape_alternative_github_strategy()
        all_collected_tools.extend(github_tools)
        
        # Strategy 3: Reddit alternative strategy
        logger.info("\\n" + "="*60)
        logger.info("STRATEGY 3: REDDIT SEARCH")
        logger.info("="*60)
        
        reddit_tools = self.scrape_reddit_alternative_strategy()
        all_collected_tools.extend(reddit_tools)
        
        # Remove duplicates and save final results
        unique_tools = []
        seen_names = set()
        for tool in all_collected_tools:
            name_key = tool['name'].lower().strip()
            if name_key not in seen_names:
                unique_tools.append(tool)
                seen_names.add(name_key)
        
        # Final save
        final_saved_count = self.save_tools_to_database(unique_tools)
        
        # Final summary
        self.cursor.execute('SELECT COUNT(*) FROM ai_tools')
        final_count = self.cursor.fetchone()[0]
        
        elapsed_time = time.time() - start_time
        
        logger.info(f"\\n{'='*80}")
        logger.info(f"🏁 FINAL PUSH COMPLETED 🏁")
        logger.info(f"{'='*80}")
        logger.info(f"⏱️  Time elapsed: {elapsed_time/60:.2f} minutes")
        logger.info(f"📊 Initial tools: {initial_count}")
        logger.info(f"📊 Final tools: {final_count}")
        logger.info(f"📊 New tools added: {final_count - initial_count}")
        logger.info(f"📊 Tools collected this session: {len(unique_tools)}")
        logger.info(f"📊 Final tools saved: {final_saved_count}")
        
        if final_count >= 5000:
            logger.info(f"🎉🎉🎉 TARGET ACHIEVED! WE DID IT! 🎉🎉🎉")
        else:
            logger.info(f"⚠️ Need {5000 - final_count} more tools to reach 5000")
        
        # Export results
        self.export_results()
        
        return final_count
    
    def export_results(self):
        """Export results to CSV"""
        try:
            df = pd.read_sql_query('SELECT * FROM ai_tools ORDER BY created_at DESC', self.conn)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"final_push_scrape_results_{timestamp}.csv"
            df.to_csv(filename, index=False)
            
            logger.info(f"📁 Results exported to: {filename}")
            logger.info(f"📊 Total rows exported: {len(df)}")
            
            # Show summary by source
            if 'source_website' in df.columns:
                source_summary = df['source_website'].value_counts()
                logger.info(f"\\n📈 Tools by source (Top 15):")
                for source, count in source_summary.head(15).items():
                    logger.info(f"  {source}: {count} tools")
                    
        except Exception as e:
            logger.error(f"Error exporting results: {e}")

def main():
    scraper = FinalPushScraper()
    final_count = scraper.run_final_push()
    
    if final_count >= 5000:
        print(f"\\n🎉🎉🎉 SUCCESS! COLLECTED {final_count} TOOLS - TARGET ACHIEVED! 🎉🎉🎉")
    elif final_count >= 4000:
        print(f"\\n🎉 Almost there! Collected {final_count} tools. Need {5000 - final_count} more to reach 5000.")
    else:
        print(f"\\n⚠️ Collected {final_count} tools. Need {5000 - final_count} more to reach target.")
    
    scraper.conn.close()

if __name__ == "__main__":
    main()
