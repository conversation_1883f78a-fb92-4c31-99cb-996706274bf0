#!/usr/bin/env python3
"""Simple test to check if admin_dashboard.py can import without syntax errors"""

import sys
import traceback

try:
    print("Testing import of admin_dashboard.py...")
    import admin_dashboard
    print("✅ Import successful!")
    
    # Check if the Flask app is created
    if hasattr(admin_dashboard, 'app'):
        print("✅ Flask app found!")
        
        # List all routes
        print("\nRegistered routes:")
        for rule in admin_dashboard.app.url_map.iter_rules():
            print(f"  {rule.endpoint}: {rule.rule}")
            
        # Check specifically for admin_api_scraping
        if 'admin_api_scraping' in [rule.endpoint for rule in admin_dashboard.app.url_map.iter_rules()]:
            print("✅ admin_api_scraping route is registered!")
        else:
            print("❌ admin_api_scraping route NOT found!")
            
    else:
        print("❌ Flask app not found!")
        
except Exception as e:
    print(f"❌ Import failed with error: {e}")
    print("\nFull traceback:")
    traceback.print_exc()
