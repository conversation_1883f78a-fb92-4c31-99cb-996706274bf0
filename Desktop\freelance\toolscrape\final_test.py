import os
import sys
os.chdir('c:\\Users\\<USER>\\Desktop\\freelance\\toolscrape')

try:
    print("Testing route registration...")
    import admin_dashboard
    app = admin_dashboard.app
    
    # Get all routes
    routes = {rule.endpoint: rule.rule for rule in app.url_map.iter_rules()}
    
    # Check for API scraping routes
    api_routes = {k: v for k, v in routes.items() if 'api_scraping' in k}
    print(f"API Scraping routes found: {len(api_routes)}")
    for endpoint, rule in api_routes.items():
        print(f"  {endpoint}: {rule}")
    
    # Test URL generation
    with app.app_context():
        try:
            from flask import url_for
            url = url_for('admin_api_scraping')
            print(f"✅ URL generation works: {url}")
        except Exception as e:
            print(f"❌ URL generation failed: {e}")
            
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
