<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔔 Notifications - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
            color: #e2e8f0;
        }
        
        .admin-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px 0;
        }
        
        .header-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-logo {
            font-size: 1.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .admin-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .nav-link {
            color: #94a3b8;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }
        
        .logout-btn {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
        }
        
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .notification-actions {
            display: flex;
            gap: 15px;
        }
        
        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
            color: white;
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #cbd5e1;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .notifications-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .notification-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
        }
        
        .notification-item:hover {
            transform: translateX(5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        
        .notification-item.unread {
            border-left: 4px solid #3b82f6;
            background: rgba(59, 130, 246, 0.05);
        }
        
        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .notification-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #e2e8f0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .notification-type {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .type-info {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }
        
        .type-success {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
        }
        
        .type-warning {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
        }
        
        .type-error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }
        
        .notification-time {
            color: #94a3b8;
            font-size: 0.9rem;
        }
        
        .notification-message {
            color: #cbd5e1;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .notification-actions-item {
            display: flex;
            gap: 10px;
        }
        
        .mark-read-btn {
            background: none;
            border: none;
            color: #3b82f6;
            font-size: 0.9rem;
            cursor: pointer;
            text-decoration: underline;
        }
        
        .mark-read-btn:hover {
            color: #2563eb;
        }
        
        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: #94a3b8;
        }
        
        .empty-state-icon {
            font-size: 5rem;
            margin-bottom: 20px;
            opacity: 0.6;
        }
        
        .filter-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 5px;
        }
        
        .filter-tab {
            padding: 10px 20px;
            border-radius: 8px;
            text-decoration: none;
            color: #94a3b8;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .filter-tab.active {
            background: #3b82f6;
            color: white;
        }
        
        .notification-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 800;
            color: #3b82f6;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #94a3b8;
            margin-top: 5px;
        }
        
        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                gap: 20px;
                align-items: stretch;
            }
            
            .notification-actions {
                justify-content: center;
            }
            
            .filter-tabs {
                flex-direction: column;
                gap: 5px;
            }
            
            .notification-header {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="header-content">
            <div class="admin-logo">🔔 AI Tools Admin</div>
            <nav class="admin-nav">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-link">Dashboard</a>
                <a href="{{ url_for('admin_tools') }}" class="nav-link">Tools</a>
                <a href="{{ url_for('admin_scraping') }}" class="nav-link">Scraping</a>
                <a href="{{ url_for('admin_publishing') }}" class="nav-link">Publishing</a>
                <a href="{{ url_for('admin_analytics') }}" class="nav-link">Analytics</a>
                <a href="{{ url_for('admin_logout') }}" class="logout-btn">Logout</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="page-header">
            <h1 class="page-title">Notifications</h1>
            <div class="notification-actions">
                <a href="{{ url_for('admin_clear_notifications') }}" class="btn btn-secondary">
                    ✅ Mark All Read
                </a>
                <button class="btn btn-primary" onclick="location.reload()">
                    🔄 Refresh
                </button>
            </div>
        </div>

        <!-- Notification Statistics -->
        <div class="notification-stats">
            {% set unread_count = notifications|selectattr('is_read', 'equalto', 0)|list|length %}
            {% set read_count = notifications|selectattr('is_read', 'equalto', 1)|list|length %}
            {% set total_count = notifications|length %}
            
            <div class="stat-card">
                <div class="stat-value">{{ total_count }}</div>
                <div class="stat-label">Total Notifications</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ unread_count }}</div>
                <div class="stat-label">Unread</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ read_count }}</div>
                <div class="stat-label">Read</div>
            </div>
        </div>

        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <a href="{{ url_for('admin_notifications') }}" class="filter-tab active">
                📋 All Notifications
            </a>
            <a href="{{ url_for('admin_notifications') }}?filter=unread" class="filter-tab">
                🔔 Unread ({{ unread_count }})
            </a>
            <a href="{{ url_for('admin_notifications') }}?filter=read" class="filter-tab">
                ✅ Read ({{ read_count }})
            </a>
        </div>

        <!-- Notifications List -->
        {% if notifications %}
        <div class="notifications-list">
            {% for notification in notifications %}
            <div class="notification-item {% if not notification.is_read %}unread{% endif %}">
                <div class="notification-header">
                    <div class="notification-title">
                        {% if notification.type == 'info' %}🔵
                        {% elif notification.type == 'success' %}🟢
                        {% elif notification.type == 'warning' %}🟡
                        {% elif notification.type == 'error' %}🔴
                        {% else %}ℹ️{% endif %}
                        {{ notification.title }}
                        <span class="notification-type type-{{ notification.type }}">{{ notification.type.title() }}</span>
                    </div>
                    <div class="notification-time">
                        {{ notification.created_at }}
                    </div>
                </div>
                
                <div class="notification-message">
                    {{ notification.message }}
                </div>
                
                {% if not notification.is_read %}
                <div class="notification-actions-item">
                    <button class="mark-read-btn" onclick="markAsRead({{ notification.id }})">
                        Mark as Read
                    </button>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <div class="empty-state-icon">📭</div>
            <h3>No Notifications</h3>
            <p>You're all caught up! No notifications to display.</p>
        </div>
        {% endif %}
    </div>

    <script>
        function markAsRead(notificationId) {
            fetch(`{{ url_for('admin_mark_notification_read', notification_id=0) }}`.replace('0', notificationId), {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
            });
        }
        
        // Auto-refresh notifications every 30 seconds
        setInterval(() => {
            // Check for new notifications without full page reload
            fetch('{{ url_for("admin_api_stats") }}')
            .then(response => response.json())
            .then(data => {
                // You could update the notification count or show a toast here
                console.log('Checking for new notifications...');
            })
            .catch(error => {
                console.error('Error checking for new notifications:', error);
            });
        }, 30000);
        
        // Mark notifications as read when they come into view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && entry.target.classList.contains('unread')) {
                    // Auto-mark as read after 3 seconds of being in view
                    setTimeout(() => {
                        const notificationId = entry.target.dataset.notificationId;
                        if (notificationId) {
                            markAsRead(notificationId);
                        }
                    }, 3000);
                }
            });
        });
        
        // Observe all unread notifications
        document.querySelectorAll('.notification-item.unread').forEach(item => {
            observer.observe(item);
        });
    </script>
</body>
</html>
