<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔌 API Scraping Management - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
            color: #e2e8f0;
        }
        
        .admin-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px 0;
        }
        
        .header-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-logo {
            font-size: 1.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .admin-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .nav-link {
            color: #94a3b8;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }
        
        .logout-btn {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-header {
            margin-bottom: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .page-subtitle {
            color: #94a3b8;
            font-size: 1.1rem;
            margin-top: 10px;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
            color: white;
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #e2e8f0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-label {
            color: #94a3b8;
            font-weight: 500;
        }
        
        .content-section {
            background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
            padding: 30px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .configs-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .configs-table th,
        .configs-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .configs-table th {
            background: rgba(59, 130, 246, 0.1);
            font-weight: 600;
            color: #3b82f6;
        }
        
        .configs-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-active {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
        }
        
        .status-inactive {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }
        
        .api-type-badge {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            background: rgba(139, 92, 246, 0.2);
            color: #8b5cf6;
        }
        
        .config-actions {
            display: flex;
            gap: 8px;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
        }
        
        .recent-results {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result-item {
            padding: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .result-info h4 {
            margin-bottom: 5px;
            color: #e2e8f0;
        }
        
        .result-meta {
            color: #94a3b8;
            font-size: 0.9rem;
        }
        
        .result-stats {
            text-align: right;
        }
        
        .tools-count {
            font-size: 1.2rem;
            font-weight: 600;
            color: #3b82f6;
        }
        
        .execution-time {
            color: #94a3b8;
            font-size: 0.8rem;
        }
        
        .bulk-actions {
            margin: 20px 0;
            padding: 20px;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        
        .checkbox-column {
            width: 40px;
        }
        
        .flash-messages {
            margin-bottom: 30px;
        }
        
        .flash-message {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .flash-success {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }
        
        .flash-error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            
            .action-buttons {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .configs-table {
                font-size: 0.9rem;
            }
            
            .configs-table th,
            .configs-table td {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="header-content">
            <div class="admin-logo">🔌 API Scraping Manager</div>
            <nav class="admin-nav">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-link">Dashboard</a>
                <a href="{{ url_for('admin_tools') }}" class="nav-link">Tools</a>
                <a href="{{ url_for('admin_scraping') }}" class="nav-link">Scraping</a>
                <a href="{{ url_for('admin_api_scraping') }}" class="nav-link active">API Scraping</a>
                <a href="{{ url_for('admin_publishing') }}" class="nav-link">Publishing</a>
                <a href="{{ url_for('admin_analytics') }}" class="nav-link">Analytics</a>
                <a href="{{ url_for('admin_logout') }}" class="logout-btn">Logout</a>
            </nav>
        </div>
    </header>

    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages">
                    {% for category, message in messages %}
                        <div class="flash-message flash-{{ 'success' if category == 'success' else 'error' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <div class="page-header">
            <div>
                <h1 class="page-title">🔌 API Scraping Management</h1>
                <p class="page-subtitle">Configure and manage API-based data sources for automated scraping</p>
            </div>
            <div class="action-buttons">
                <a href="{{ url_for('admin_new_api_config') }}" class="btn btn-primary">
                    ➕ New API Configuration
                </a>
                <button onclick="bulkRunSelected()" class="btn btn-success">
                    🚀 Run Selected
                </button>
                <button onclick="refreshPage()" class="btn btn-secondary">
                    🔄 Refresh
                </button>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{{ configs|length }}</div>
                <div class="stat-label">Total Configurations</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ configs|selectattr('is_active')|list|length }}</div>
                <div class="stat-label">Active Configurations</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ recent_results|length }}</div>
                <div class="stat-label">Recent Executions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ configs|sum(attribute='total_tools_found') or 0 }}</div>
                <div class="stat-label">Total Tools Scraped</div>
            </div>
        </div>

        <!-- API Configurations -->
        <div class="content-section">
            <h2 class="section-title">🔧 API Configurations</h2>
            
            {% if configs %}
                <div class="bulk-actions">
                    <label>
                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()"> Select All
                    </label>
                    <span style="margin-left: 20px; color: #94a3b8;">Select configurations to run bulk operations</span>
                </div>
                
                <table class="configs-table">
                    <thead>
                        <tr>
                            <th class="checkbox-column"></th>
                            <th>Configuration Name</th>
                            <th>API Type</th>
                            <th>Status</th>
                            <th>Last Run</th>
                            <th>Success Rate</th>
                            <th>Tools Found</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for config in configs %}
                        <tr>
                            <td>
                                <input type="checkbox" class="config-checkbox" value="{{ config.id }}">
                            </td>
                            <td>
                                <strong>{{ config.config_name }}</strong>
                                <br>
                                <small style="color: #94a3b8;">{{ config.api_url[:50] }}...</small>
                            </td>
                            <td>
                                <span class="api-type-badge">{{ config.api_type }}</span>
                            </td>
                            <td>
                                <span class="status-badge {{ 'status-active' if config.is_active else 'status-inactive' }}">
                                    {{ 'Active' if config.is_active else 'Inactive' }}
                                </span>
                            </td>
                            <td>
                                {% if config.last_run %}
                                    {{ config.last_run[:19] }}
                                {% else %}
                                    <span style="color: #94a3b8;">Never</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if config.success_rate %}
                                    {{ "%.1f"|format(config.success_rate * 100) }}%
                                {% else %}
                                    <span style="color: #94a3b8;">N/A</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="tools-count">{{ config.total_tools_found or 0 }}</span>
                            </td>
                            <td>
                                <div class="config-actions">
                                    <button onclick="runConfig({{ config.id }})" class="btn btn-success btn-sm" 
                                            {{ 'disabled' if not config.is_active }}>
                                        ▶️ Run
                                    </button>
                                    <button onclick="testConfig({{ config.id }})" class="btn btn-secondary btn-sm">
                                        🧪 Test
                                    </button>
                                    <a href="{{ url_for('admin_edit_api_config', config_id=config.id) }}" class="btn btn-primary btn-sm">
                                        ✏️ Edit
                                    </a>
                                    <a href="{{ url_for('admin_api_scraping_results', config_id=config.id) }}" class="btn btn-secondary btn-sm">
                                        📊 Results
                                    </a>
                                    <form style="display: inline;" method="POST" action="{{ url_for('admin_toggle_api_config', config_id=config.id) }}">
                                        <button type="submit" class="btn btn-secondary btn-sm">
                                            {{ '⏸️ Disable' if config.is_active else '▶️ Enable' }}
                                        </button>
                                    </form>
                                    <form style="display: inline;" method="POST" action="{{ url_for('admin_delete_api_config', config_id=config.id) }}" 
                                          onsubmit="return confirm('Are you sure you want to delete this configuration?')">
                                        <button type="submit" class="btn btn-danger btn-sm">
                                            🗑️ Delete
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div style="text-align: center; padding: 40px; color: #94a3b8;">
                    <h3>No API configurations found</h3>
                    <p>Create your first API configuration to start automated scraping</p>
                    <a href="{{ url_for('admin_new_api_config') }}" class="btn btn-primary" style="margin-top: 20px;">
                        ➕ Create First Configuration
                    </a>
                </div>
            {% endif %}
        </div>

        <!-- Recent Results -->
        {% if recent_results %}
        <div class="content-section">
            <h2 class="section-title">📈 Recent Execution Results</h2>
            <div class="recent-results">
                {% for result in recent_results %}
                <div class="result-item">
                    <div class="result-info">
                        <h4>{{ result.config_name }}</h4>
                        <div class="result-meta">
                            {{ result.started_at[:19] }} • 
                            <span class="status-badge {{ 'status-active' if result.status == 'completed' else 'status-inactive' }}">
                                {{ result.status.title() }}
                            </span>
                        </div>
                        {% if result.error_message %}
                            <div style="color: #ef4444; font-size: 0.8rem; margin-top: 5px;">
                                {{ result.error_message }}
                            </div>
                        {% endif %}
                    </div>
                    <div class="result-stats">
                        <div class="tools-count">{{ result.tools_found }} tools</div>
                        {% if result.execution_time %}
                            <div class="execution-time">{{ "%.2f"|format(result.execution_time) }}s</div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>

    <script>
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.config-checkbox');
            checkboxes.forEach(cb => cb.checked = selectAll.checked);
        }

        function bulkRunSelected() {
            const selected = Array.from(document.querySelectorAll('.config-checkbox:checked')).map(cb => parseInt(cb.value));
            
            if (selected.length === 0) {
                alert('Please select at least one configuration');
                return;
            }

            if (!confirm(`Run ${selected.length} selected configurations?`)) {
                return;
            }

            document.body.classList.add('loading');
            
            fetch('/admin/api-scraping/bulk-run', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({config_ids: selected})
            })
            .then(response => response.json())
            .then(data => {
                document.body.classList.remove('loading');
                if (data.success) {
                    alert(`Bulk run completed! Found ${data.total_tools} total tools.`);
                    location.reload();
                } else {
                    alert(`Error: ${data.error}`);
                }
            })
            .catch(error => {
                document.body.classList.remove('loading');
                alert(`Error: ${error.message}`);
            });
        }

        function runConfig(configId) {
            if (!confirm('Run this API configuration?')) {
                return;
            }

            document.body.classList.add('loading');
            
            fetch(`/admin/api-scraping/run/${configId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                document.body.classList.remove('loading');
                if (data.success) {
                    alert(`Scraping completed! Found ${data.tools_found} tools in ${data.execution_time.toFixed(2)} seconds.`);
                    location.reload();
                } else {
                    alert(`Error: ${data.error}`);
                }
            })
            .catch(error => {
                document.body.classList.remove('loading');
                alert(`Error: ${error.message}`);
            });
        }

        function testConfig(configId) {
            document.body.classList.add('loading');
            
            fetch(`/admin/api-scraping/test/${configId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                document.body.classList.remove('loading');
                if (data.success) {
                    let message = `Test successful! Found ${data.tools_found} tools in ${data.execution_time.toFixed(2)} seconds.`;
                    if (data.sample_tools && data.sample_tools.length > 0) {
                        message += `\n\nSample tools:\n${data.sample_tools.map(t => `- ${t.name}`).join('\n')}`;
                    }
                    alert(message);
                } else {
                    alert(`Test failed: ${data.error}`);
                }
            })
            .catch(error => {
                document.body.classList.remove('loading');
                alert(`Test error: ${error.message}`);
            });
        }

        function refreshPage() {
            location.reload();
        }

        // Auto-refresh every 30 seconds for real-time updates
        setInterval(() => {
            if (!document.body.classList.contains('loading')) {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
