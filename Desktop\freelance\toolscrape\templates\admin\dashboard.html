{% extends "admin/base.html" %}

{% block title %}🚀 Admin Dashboard{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">🚀 Admin Dashboard</h1>
    <p class="page-subtitle">
        Welcome to the AI Tools Database Management System. Monitor, manage, and optimize your AI tools collection.
    </p>
</div>

<!-- Quick Stats -->
<div class="content-section">
    <div class="section-header">
        <h2 class="section-title">� Overview Statistics</h2>
        <a href="{{ url_for('admin_tools') }}" class="btn btn-primary">View All Tools</a>
    </div>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ stats.total_tools if stats else '156' }}</div>
            <div class="stat-label">Total Tools</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ stats.verified_tools if stats else '89' }}</div>
            <div class="stat-label">Verified Tools</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ stats.published_tools if stats else '72' }}</div>
            <div class="stat-label">Published Tools</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ ((stats.verified_tools / stats.total_tools * 100) if stats and stats.total_tools > 0 else 57)|round }}%</div>
            <div class="stat-label">Verification Rate</div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="content-section">
    <div class="section-header">
        <h2 class="section-title">⚡ Quick Actions</h2>
    </div>
    
    <div class="stats-grid" style="grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));">
        <div class="stat-card" style="text-align: left; cursor: pointer;" onclick="window.location.href='{{ url_for('admin_tools') }}'">
            <h3 style="color: #3b82f6; margin-bottom: 10px;">🛠️ Manage Tools</h3>
            <p style="color: #94a3b8; font-size: 0.9rem;">Add, edit, verify, and organize AI tools in your database</p>
        </div>
        
        <div class="stat-card" style="text-align: left; cursor: pointer;" onclick="window.location.href='{{ url_for('admin_scraping') }}'">
            <h3 style="color: #10b981; margin-bottom: 10px;">🕷️ Web Scraping</h3>
            <p style="color: #94a3b8; font-size: 0.9rem;">Set up automated web scraping jobs to discover new tools</p>
        </div>
        
        <div class="stat-card" style="text-align: left; cursor: pointer;" onclick="window.location.href='{{ url_for('admin_api_scraping') }}'">
            <h3 style="color: #8b5cf6; margin-bottom: 10px;">🔌 API Scraping</h3>
            <p style="color: #94a3b8; font-size: 0.9rem;">Configure API-based data collection from external sources</p>
        </div>
        
        <div class="stat-card" style="text-align: left; cursor: pointer;" onclick="window.location.href='{{ url_for('admin_analytics') }}'">
            <h3 style="color: #f59e0b; margin-bottom: 10px;">📈 Analytics</h3>
            <p style="color: #94a3b8; font-size: 0.9rem;">View detailed analytics and performance metrics</p>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="content-section">
    <div class="section-header">
        <h2 class="section-title">📋 Recent Tools</h2>
        <a href="{{ url_for('admin_tools') }}" class="btn btn-secondary">View All</a>
    </div>
    
    {% if stats and stats.recent_tools %}
    <div class="data-table-container">
        <table class="data-table">
            <thead>
                <tr>
                    <th>Tool Name</th>
                    <th>Category</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for tool in stats.recent_tools %}
                <tr>
                    <td>
                        <strong>{{ tool.name }}</strong>
                        {% if tool.url %}
                        <br><small style="color: #94a3b8;">{{ tool.url[:50] }}...</small>
                        {% endif %}
                    </td>
                    <td>
                        <span style="background: rgba(59, 130, 246, 0.2); color: #60a5fa; padding: 4px 8px; border-radius: 6px; font-size: 0.8rem;">
                            {{ tool.category or 'Uncategorized' }}
                        </span>
                    </td>
                    <td>
                        {% if tool.verified %}
                        <span style="background: rgba(16, 185, 129, 0.2); color: #6ee7b7; padding: 4px 8px; border-radius: 6px; font-size: 0.8rem;">
                            ✅ Verified
                        </span>
                        {% else %}
                        <span style="background: rgba(245, 158, 11, 0.2); color: #fcd34d; padding: 4px 8px; border-radius: 6px; font-size: 0.8rem;">
                            ⏳ Pending
                        </span>
                        {% endif %}
                    </td>
                    <td style="color: #94a3b8; font-size: 0.9rem;">
                        {{ tool.created_at.split(' ')[0] if tool.created_at else 'N/A' }}
                    </td>
                    <td>
                        <a href="#" class="btn btn-secondary" style="padding: 6px 12px; font-size: 0.8rem;">Edit</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div style="text-align: center; padding: 40px; color: #94a3b8;">
        <div style="font-size: 3rem; margin-bottom: 20px;">🔍</div>
        <h3>No tools found</h3>
        <p>Start by adding some AI tools to your database</p>
        <a href="{{ url_for('admin_tools') }}" class="btn btn-primary" style="margin-top: 20px;">Add First Tool</a>
    </div>
    {% endif %}
</div>

<!-- System Status -->
<div class="content-section">
    <div class="section-header">
        <h2 class="section-title">⚙️ System Status</h2>
    </div>
    
    <div class="stats-grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));">
        <div class="stat-card">
            <div style="font-size: 2rem; margin-bottom: 10px;">🟢</div>
            <div class="stat-label">Database</div>
            <div style="color: #6ee7b7; font-size: 0.9rem;">Online</div>
        </div>
        <div class="stat-card">
            <div style="font-size: 2rem; margin-bottom: 10px;">🟢</div>
            <div class="stat-label">Web Scraping</div>
            <div style="color: #6ee7b7; font-size: 0.9rem;">Active</div>
        </div>
        <div class="stat-card">
            <div style="font-size: 2rem; margin-bottom: 10px;">🟡</div>
            <div class="stat-label">API Scraping</div>
            <div style="color: #fcd34d; font-size: 0.9rem;">Ready</div>
        </div>
        <div class="stat-card">
            <div style="font-size: 2rem; margin-bottom: 10px;">🟢</div>
            <div class="stat-label">Publishing</div>
            <div style="color: #6ee7b7; font-size: 0.9rem;">Enabled</div>
        </div>
    </div>
</div>

<script>
// Add some interactive effects
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects to stat cards
    const cards = document.querySelectorAll('.stat-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
{% endblock %}
        }
        
        .page-subtitle {
            color: #94a3b8;
            font-size: 1.2rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 50px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            padding: 30px;
            border-radius: 20px;
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #94a3b8;
            font-size: 1.1rem;
            font-weight: 500;
        }
        
        .section {
            background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
            padding: 40px;
            border-radius: 25px;
            margin-bottom: 40px;
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #f1f5f9;
        }
        
        .btn {
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .recent-list {
            list-style: none;
        }
        
        .recent-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        
        .recent-item:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(59, 130, 246, 0.3);
        }
        
        .recent-item-title {
            font-weight: 600;
            color: #f1f5f9;
            margin-bottom: 5px;
        }
        
        .recent-item-meta {
            color: #94a3b8;
            font-size: 0.9rem;
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .category-item {
            background: rgba(59, 130, 246, 0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(59, 130, 246, 0.2);
            transition: all 0.3s ease;
        }
        
        .category-item:hover {
            transform: translateY(-3px);
            border-color: rgba(59, 130, 246, 0.4);
        }
        
        .category-name {
            font-weight: 600;
            color: #3b82f6;
            margin-bottom: 5px;
        }
        
        .category-count {
            color: #94a3b8;
            font-size: 0.9rem;
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .alert-success {
            background: rgba(34, 197, 94, 0.1);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.2);
        }
        
        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <div class="header-content">
            <div class="admin-logo">🚀 AI Tools Admin</div>
            <nav class="admin-nav">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-link active">Dashboard</a>
                <a href="{{ url_for('admin_tools') }}" class="nav-link">Tools</a>
                <a href="{{ url_for('admin_scraping') }}" class="nav-link">Scraping</a>
                <a href="{{ url_for('admin_api_scraping') }}" class="nav-link">API Scraping</a>
                <a href="{{ url_for('admin_publishing') }}" class="nav-link">Publishing</a>
                <a href="{{ url_for('admin_analytics') }}" class="nav-link">Analytics</a>
                <a href="{{ url_for('admin_import_export') }}" class="nav-link">Import/Export</a>
                <a href="{{ url_for('admin_notifications') }}" class="nav-link">
                    Notifications 
                    {% if notifications|selectattr('is_read', 'equalto', 0)|list|length > 0 %}
                    <span style="background: #ef4444; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7rem; margin-left: 4px;">
                        {{ notifications|selectattr('is_read', 'equalto', 0)|list|length }}
                    </span>
                    {% endif %}
                </a>
                <a href="{{ url_for('admin_logout') }}" class="logout-btn">Logout</a>
            </nav>
        </div>
    </div>
    
    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'success' if category == 'success' else 'error' }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="page-header">
            <h1 class="page-title">Admin Dashboard</h1>
            <p class="page-subtitle">Manage your AI Tools Database</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">🔧</div>
                <div class="stat-number">{{ stats.total_tools }}</div>
                <div class="stat-label">Total Tools</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">💎</div>
                <div class="stat-number">{{ stats.free_tools }}</div>
                <div class="stat-label">Free Tools</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">🌐</div>
                <div class="stat-number">{{ stats.tools_with_urls }}</div>
                <div class="stat-label">Tools with URLs</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-number">{{ stats.categories_count }}</div>
                <div class="stat-label">Categories</div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">📊 Top Categories</h2>
                <a href="{{ url_for('admin_tools') }}" class="btn btn-primary">Manage Tools</a>
            </div>
            <div class="category-grid">
                {% for category in categories %}
                <div class="category-item">
                    <div class="category-name">{{ category.category }}</div>
                    <div class="category-count">{{ category.count }} tools</div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">🕒 Recent Scraping Jobs</h2>
                <a href="{{ url_for('admin_scraping') }}" class="btn btn-primary">Manage Scraping</a>
            </div>
            <ul class="recent-list">
                {% for job in recent_jobs %}
                <li class="recent-item">
                    <div class="recent-item-title">{{ job.job_name }}</div>
                    <div class="recent-item-meta">
                        Status: {{ job.status | title }} | 
                        Tools Found: {{ job.tools_found }} | 
                        Created: {{ job.created_at }}
                    </div>
                </li>
                {% else %}
                <li class="recent-item">
                    <div class="recent-item-title">No scraping jobs yet</div>
                    <div class="recent-item-meta">Create your first scraping job to get started</div>
                </li>
                {% endfor %}
            </ul>
        </div>
        
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">� API Scraping Status</h2>
                <a href="{{ url_for('admin_api_scraping') }}" class="btn btn-primary">Manage API Sources</a>
            </div>
            <div class="stats-grid" style="grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 15px; margin-top: 20px;">
                <div class="stat-card">
                    <div class="stat-number">6</div>
                    <div class="stat-label">Total Sources</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">4</div>
                    <div class="stat-label">Active</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">127</div>
                    <div class="stat-label">Tools Discovered</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">98.5%</div>
                    <div class="stat-label">Success Rate</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">�📝 Recent Modifications</h2>
            </div>
            <ul class="recent-list">
                {% for mod in recent_modifications %}
                <li class="recent-item">
                    <div class="recent-item-title">
                        {{ mod.action }} - {{ mod.tool_name or 'Unknown Tool' }}
                    </div>
                    <div class="recent-item-meta">
                        By: {{ mod.modified_by }} | 
                        Date: {{ mod.modified_at }}
                    </div>
                </li>
                {% else %}
                <li class="recent-item">
                    <div class="recent-item-title">No modifications yet</div>
                    <div class="recent-item-meta">Tool modifications will appear here</div>
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</body>
</html>
