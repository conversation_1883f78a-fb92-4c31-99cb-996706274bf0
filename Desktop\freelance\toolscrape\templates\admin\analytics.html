<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Analytics Dashboard - Admin</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
            color: #e2e8f0;
        }
        
        .admin-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px 0;
        }
        
        .header-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-logo {
            font-size: 1.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .admin-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .nav-link {
            color: #94a3b8;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }
        
        .logout-btn {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
        }
        
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .chart-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .chart-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #e2e8f0;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }
        
        .chart-stats {
            display: flex;
            justify-content: space-around;
            gap: 15px;
            margin-top: 15px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #94a3b8;
            margin-top: 2px;
        }
        
        .full-width {
            grid-column: 1 / -1;
        }
        
        .data-table {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: #e2e8f0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .table td {
            padding: 12px 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            color: #cbd5e1;
        }
        
        .table tr:hover {
            background: rgba(255, 255, 255, 0.02);
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            border-radius: 3px;
            transition: width 0.3s ease;
        }
        
        .export-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        @media (max-width: 768px) {
            .analytics-grid {
                grid-template-columns: 1fr;
            }
            
            .chart-container {
                height: 250px;
            }
            
            .chart-stats {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="header-content">
            <div class="admin-logo">📊 AI Tools Admin</div>
            <nav class="admin-nav">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-link">Dashboard</a>
                <a href="{{ url_for('admin_tools') }}" class="nav-link">Tools</a>
                <a href="{{ url_for('admin_scraping') }}" class="nav-link">Scraping</a>
                <a href="{{ url_for('admin_publishing') }}" class="nav-link">Publishing</a>
                <a href="{{ url_for('admin_analytics') }}" class="nav-link active">Analytics</a>
                <a href="{{ url_for('admin_logout') }}" class="logout-btn">Logout</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="page-header">
            <h1 class="page-title">Analytics Dashboard</h1>
            <a href="{{ url_for('admin_export_data', format='csv') }}" class="btn btn-primary">
                📊 Export Data
            </a>
        </div>

        <div class="analytics-grid">
            <!-- Daily Additions Chart -->
            <div class="chart-card">
                <div class="chart-title">📈 Daily Tool Additions (Last 30 Days)</div>
                <div class="chart-container">
                    <canvas id="dailyAdditionsChart"></canvas>
                </div>
                <div class="chart-stats">
                    <div class="stat-item">
                        <div class="stat-value">{{ daily_additions|length }}</div>
                        <div class="stat-label">Active Days</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ daily_additions|map(attribute='count')|sum }}</div>
                        <div class="stat-label">Total Added</div>
                    </div>
                </div>
            </div>

            <!-- Category Distribution -->
            <div class="chart-card">
                <div class="chart-title">🏷️ Category Distribution</div>
                <div class="chart-container">
                    <canvas id="categoryChart"></canvas>
                </div>
                <div class="chart-stats">
                    <div class="stat-item">
                        <div class="stat-value">{{ category_distribution|length }}</div>
                        <div class="stat-label">Categories</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ category_distribution[0].count if category_distribution else 0 }}</div>
                        <div class="stat-label">Top Category</div>
                    </div>
                </div>
            </div>

            <!-- Pricing Distribution -->
            <div class="chart-card">
                <div class="chart-title">💰 Pricing Model Distribution</div>
                <div class="chart-container">
                    <canvas id="pricingChart"></canvas>
                </div>
            </div>

            <!-- Monthly Growth -->
            <div class="chart-card">
                <div class="chart-title">📊 Monthly Growth Trend</div>
                <div class="chart-container">
                    <canvas id="monthlyGrowthChart"></canvas>
                </div>
                <div class="chart-stats">
                    <div class="stat-item">
                        <div class="stat-value">{{ monthly_growth|length }}</div>
                        <div class="stat-label">Months</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ monthly_growth|map(attribute='count')|sum }}</div>
                        <div class="stat-label">Total Growth</div>
                    </div>
                </div>
            </div>

            <!-- Top Sources Table -->
            <div class="chart-card full-width">
                <div class="chart-title">🌐 Top Data Sources</div>
                <div class="data-table">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Source Website</th>
                                <th>Tools Count</th>
                                <th>Percentage</th>
                                <th>Growth</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set total_sources = top_sources|map(attribute='count')|sum %}
                            {% for source in top_sources %}
                            <tr>
                                <td>{{ source.source_website or 'Unknown' }}</td>
                                <td>{{ source.count }}</td>
                                <td>
                                    {{ "%.1f"|format((source.count / total_sources * 100) if total_sources > 0 else 0) }}%
                                    <div class="progress-bar" style="margin-top: 5px;">
                                        <div class="progress-fill" style="width: {{ (source.count / total_sources * 100) if total_sources > 0 else 0 }}%"></div>
                                    </div>
                                </td>
                                <td style="color: #10b981;">+{{ source.count }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Export Section -->
        <div class="export-section">
            <h3 style="margin-bottom: 20px;">📥 Export Analytics Data</h3>
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <a href="{{ url_for('admin_export_data', format='csv') }}" class="btn btn-primary">
                    📄 CSV Export
                </a>
                <a href="{{ url_for('admin_export_data', format='json') }}" class="btn btn-primary">
                    📋 JSON Export
                </a>
                <a href="{{ url_for('admin_export_data', format='excel') }}" class="btn btn-primary">
                    📊 Excel Export
                </a>
            </div>
        </div>
    </div>

    <script>
        // Chart.js configuration
        Chart.defaults.color = '#cbd5e1';
        Chart.defaults.borderColor = 'rgba(255, 255, 255, 0.1)';
        Chart.defaults.backgroundColor = 'rgba(59, 130, 246, 0.1)';

        // Daily Additions Chart
        const dailyCtx = document.getElementById('dailyAdditionsChart').getContext('2d');
        new Chart(dailyCtx, {
            type: 'line',
            data: {
                labels: [{% for day in daily_additions %}'{{ day.date }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    label: 'Tools Added',
                    data: [{% for day in daily_additions %}{{ day.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });

        // Category Distribution Chart
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        new Chart(categoryCtx, {
            type: 'doughnut',
            data: {
                labels: [{% for cat in category_distribution[:8] %}'{{ cat.category }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    data: [{% for cat in category_distribution[:8] %}{{ cat.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                    backgroundColor: [
                        '#3b82f6', '#8b5cf6', '#10b981', '#f59e0b',
                        '#ef4444', '#6366f1', '#ec4899', '#14b8a6'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Pricing Distribution Chart
        const pricingCtx = document.getElementById('pricingChart').getContext('2d');
        new Chart(pricingCtx, {
            type: 'bar',
            data: {
                labels: [{% for price in pricing_distribution %}'{{ price.pricing }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    label: 'Tools Count',
                    data: [{% for price in pricing_distribution %}{{ price.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                    backgroundColor: [
                        '#10b981', '#f59e0b', '#3b82f6', '#ef4444', '#8b5cf6'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });

        // Monthly Growth Chart
        const monthlyCtx = document.getElementById('monthlyGrowthChart').getContext('2d');
        new Chart(monthlyCtx, {
            type: 'bar',
            data: {
                labels: [{% for month in monthly_growth %}'{{ month.month }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    label: 'Tools Added',
                    data: [{% for month in monthly_growth %}{{ month.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                    backgroundColor: 'rgba(59, 130, 246, 0.7)',
                    borderColor: '#3b82f6',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
