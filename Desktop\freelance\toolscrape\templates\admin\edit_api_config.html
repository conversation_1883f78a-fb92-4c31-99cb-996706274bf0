<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✏️ Edit API Configuration - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
            color: #e2e8f0;
        }
        
        .admin-header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px 0;
        }
        
        .header-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-logo {
            font-size: 1.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .admin-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .nav-link {
            color: #94a3b8;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }
        
        .logout-btn {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .page-header {
            margin-bottom: 40px;
        }
        
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }
        
        .page-subtitle {
            color: #94a3b8;
            font-size: 1.1rem;
        }
        
        .form-container {
            background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
            padding: 40px;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .form-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #3b82f6;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #e2e8f0;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            color: #e2e8f0;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-textarea {
            min-height: 120px;
            resize: vertical;
            font-family: 'Courier New', monospace;
        }
        
        .form-select {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            color: #e2e8f0;
            font-size: 1rem;
        }
        
        .form-checkbox {
            margin-right: 10px;
        }
        
        .form-help {
            font-size: 0.9rem;
            color: #94a3b8;
            margin-top: 5px;
            line-height: 1.4;
        }
        
        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
            color: white;
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #e2e8f0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        
        .json-editor {
            position: relative;
        }
        
        .json-format-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(59, 130, 246, 0.2);
            border: 1px solid rgba(59, 130, 246, 0.3);
            color: #3b82f6;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8rem;
            cursor: pointer;
        }
        
        .config-info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .config-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            font-size: 0.9rem;
        }
        
        .meta-item {
            display: flex;
            flex-direction: column;
        }
        
        .meta-label {
            color: #94a3b8;
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .meta-value {
            color: #e2e8f0;
            font-weight: 600;
        }
        
        .flash-messages {
            margin-bottom: 30px;
        }
        
        .flash-message {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .flash-success {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }
        
        .flash-error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px 10px;
            }
            
            .form-container {
                padding: 30px 20px;
            }
            
            .form-actions {
                flex-direction: column;
            }
            
            .config-meta {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="header-content">
            <div class="admin-logo">✏️ Edit API Configuration</div>
            <nav class="admin-nav">
                <a href="{{ url_for('admin_dashboard') }}" class="nav-link">Dashboard</a>
                <a href="{{ url_for('admin_tools') }}" class="nav-link">Tools</a>
                <a href="{{ url_for('admin_scraping') }}" class="nav-link">Scraping</a>
                <a href="{{ url_for('admin_api_scraping') }}" class="nav-link active">API Scraping</a>
                <a href="{{ url_for('admin_publishing') }}" class="nav-link">Publishing</a>
                <a href="{{ url_for('admin_analytics') }}" class="nav-link">Analytics</a>
                <a href="{{ url_for('admin_logout') }}" class="logout-btn">Logout</a>
            </nav>
        </div>
    </header>

    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages">
                    {% for category, message in messages %}
                        <div class="flash-message flash-{{ 'success' if category == 'success' else 'error' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <div class="page-header">
            <h1 class="page-title">✏️ Edit API Configuration</h1>
            <p class="page-subtitle">Modify settings for "{{ config.config_name }}"</p>
        </div>

        <!-- Configuration Info -->
        <div class="config-info">
            <h3 style="margin-bottom: 15px; color: #3b82f6;">📊 Configuration Statistics</h3>
            <div class="config-meta">
                <div class="meta-item">
                    <span class="meta-label">Created</span>
                    <span class="meta-value">{{ config.created_at[:19] if config.created_at else 'Unknown' }}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Last Updated</span>
                    <span class="meta-value">{{ config.updated_at[:19] if config.updated_at else 'Never' }}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Last Run</span>
                    <span class="meta-value">{{ config.last_run[:19] if config.last_run else 'Never' }}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Total Runs</span>
                    <span class="meta-value">{{ config.total_runs or 0 }}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Success Rate</span>
                    <span class="meta-value">{{ "%.1f"|format((config.success_rate or 0) * 100) }}%</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Status</span>
                    <span class="meta-value" style="color: {{ '#10b981' if config.is_active else '#ef4444' }}">
                        {{ 'Active' if config.is_active else 'Inactive' }}
                    </span>
                </div>
            </div>
        </div>

        <div class="form-container">
            <form method="POST" action="{{ url_for('admin_save_api_config') }}">
                <input type="hidden" name="config_id" value="{{ config.id }}">
                
                <!-- Basic Configuration -->
                <div class="form-section">
                    <h2 class="section-title">⚙️ Basic Configuration</h2>
                    
                    <div class="form-group">
                        <label class="form-label" for="config_name">Configuration Name *</label>
                        <input type="text" id="config_name" name="config_name" class="form-input" value="{{ config.config_name }}" required>
                        <div class="form-help">A unique name to identify this API configuration</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="api_type">API Type *</label>
                        <select id="api_type" name="api_type" class="form-select" required>
                            <option value="">Select API Type</option>
                            <option value="github" {{ 'selected' if config.api_type == 'github' }}>GitHub API</option>
                            <option value="producthunt" {{ 'selected' if config.api_type == 'producthunt' }}>Product Hunt API</option>
                            <option value="huggingface" {{ 'selected' if config.api_type == 'huggingface' }}>Hugging Face API</option>
                            <option value="rest" {{ 'selected' if config.api_type == 'rest' }}>Generic REST API</option>
                            <option value="graphql" {{ 'selected' if config.api_type == 'graphql' }}>GraphQL API</option>
                            <option value="rss" {{ 'selected' if config.api_type == 'rss' }}>RSS Feed</option>
                        </select>
                        <div class="form-help">The type of API this configuration will use</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="api_url">API URL *</label>
                        <input type="url" id="api_url" name="api_url" class="form-input" value="{{ config.api_url }}" required>
                        <div class="form-help">The base URL or endpoint for the API</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="api_key">API Key</label>
                        <input type="password" id="api_key" name="api_key" class="form-input" value="{{ config.api_key or '' }}">
                        <div class="form-help">API key or token if required (leave blank to keep current)</div>
                    </div>
                </div>

                <!-- Advanced Configuration -->
                <div class="form-section">
                    <h2 class="section-title">🔧 Advanced Configuration</h2>
                    
                    <div class="form-group">
                        <label class="form-label" for="headers">HTTP Headers (JSON)</label>
                        <div class="json-editor">
                            <textarea id="headers" name="headers" class="form-input form-textarea">{{ config.headers or '{}' }}</textarea>
                            <button type="button" class="json-format-btn" onclick="formatJSON('headers')">Format JSON</button>
                        </div>
                        <div class="form-help">Additional HTTP headers to include with requests (JSON format)</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="parameters">API Parameters (JSON)</label>
                        <div class="json-editor">
                            <textarea id="parameters" name="parameters" class="form-input form-textarea">{{ config.parameters or '{}' }}</textarea>
                            <button type="button" class="json-format-btn" onclick="formatJSON('parameters')">Format JSON</button>
                        </div>
                        <div class="form-help">Query parameters or request body (JSON format)</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="data_mapping">Data Mapping (JSON) *</label>
                        <div class="json-editor">
                            <textarea id="data_mapping" name="data_mapping" class="form-input form-textarea" required>{{ config.data_mapping or '{}' }}</textarea>
                            <button type="button" class="json-format-btn" onclick="formatJSON('data_mapping')">Format JSON</button>
                        </div>
                        <div class="form-help">Map API response fields to our tool fields: name, description, url, category, free_tier</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="rate_limit">Rate Limit (seconds)</label>
                        <input type="number" id="rate_limit" name="rate_limit" class="form-input" value="{{ config.rate_limit or 60 }}" min="1">
                        <div class="form-help">Minimum time between API calls to respect rate limits</div>
                    </div>
                    
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="is_active" name="is_active" class="form-checkbox" {{ 'checked' if config.is_active }}>
                            <span class="form-label">Active Configuration</span>
                        </label>
                        <div class="form-help">Enable this configuration for automatic and manual scraping</div>
                    </div>
                </div>

                <div class="form-actions">
                    <a href="{{ url_for('admin_api_scraping') }}" class="btn btn-secondary">Cancel</a>
                    <button type="button" class="btn btn-success" onclick="testConfiguration()">🧪 Test Configuration</button>
                    <a href="{{ url_for('admin_api_scraping_results', config_id=config.id) }}" class="btn btn-secondary">📊 View Results</a>
                    <button type="submit" class="btn btn-primary">💾 Save Changes</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function formatJSON(fieldId) {
            const field = document.getElementById(fieldId);
            try {
                const parsed = JSON.parse(field.value);
                field.value = JSON.stringify(parsed, null, 2);
            } catch (e) {
                alert('Invalid JSON format in ' + fieldId);
            }
        }

        function testConfiguration() {
            const configId = {{ config.id }};
            
            if (!confirm('Test this API configuration? This will make a real API call.')) {
                return;
            }
            
            // Show loading state
            document.body.style.opacity = '0.6';
            document.body.style.pointerEvents = 'none';
            
            fetch(`/admin/api-scraping/test/${configId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                document.body.style.opacity = '1';
                document.body.style.pointerEvents = 'auto';
                
                if (data.success) {
                    let message = `Test successful! Found ${data.tools_found} tools in ${data.execution_time.toFixed(2)} seconds.`;
                    if (data.sample_tools && data.sample_tools.length > 0) {
                        message += `\n\nSample tools:\n${data.sample_tools.map(t => `- ${t.name}`).join('\n')}`;
                    }
                    alert(message);
                } else {
                    alert(`Test failed: ${data.error}`);
                }
            })
            .catch(error => {
                document.body.style.opacity = '1';
                document.body.style.pointerEvents = 'auto';
                alert(`Test error: ${error.message}`);
            });
        }

        // Auto-format JSON on page load
        window.addEventListener('DOMContentLoaded', () => {
            ['headers', 'parameters', 'data_mapping'].forEach(fieldId => {
                try {
                    formatJSON(fieldId);
                } catch (e) {
                    // Don't format if invalid JSON
                }
            });
        });

        // Auto-format JSON on blur
        ['headers', 'parameters', 'data_mapping'].forEach(fieldId => {
            document.getElementById(fieldId).addEventListener('blur', () => {
                try {
                    const field = document.getElementById(fieldId);
                    const parsed = JSON.parse(field.value);
                    field.value = JSON.stringify(parsed, null, 2);
                } catch (e) {
                    // Don't format if invalid JSON
                }
            });
        });
    </script>
</body>
</html>
