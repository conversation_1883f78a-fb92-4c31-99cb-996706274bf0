#!/usr/bin/env python3
"""Comprehensive dashboard functionality test"""

import requests
import time
import json

def test_dashboard_comprehensive():
    base_url = "http://localhost:5001"
    session = requests.Session()
    
    print("🧪 Testing Admin Dashboard Functionality...")
    
    # Test 1: Basic connectivity
    try:
        response = session.get(f"{base_url}/admin/login", timeout=10)
        print(f"✅ Login page accessible: {response.status_code}")
        if response.status_code != 200:
            print("❌ Login page not working properly")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to dashboard: {e}")
        return False
    
    # Test 2: Login functionality
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        response = session.post(f"{base_url}/admin/login", data=login_data, allow_redirects=True)
        print(f"✅ Login processed: {response.status_code}")
        
        if "login" in response.url:
            print("❌ Login failed - still on login page")
            print("Response text:", response.text[:300])
            return False
        
    except Exception as e:
        print(f"❌ Login failed: {e}")
        return False
    
    # Test 3: Dashboard access
    try:
        response = session.get(f"{base_url}/admin/dashboard")
        print(f"✅ Dashboard access: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ Dashboard not accessible after login")
            return False
            
        # Check if dashboard contains expected content
        if "Dashboard" in response.text or "admin" in response.text.lower():
            print("✅ Dashboard content looks good")
        else:
            print("⚠️  Dashboard content might be incomplete")
            
    except Exception as e:
        print(f"❌ Dashboard access failed: {e}")
        return False
    
    # Test 4: API Scraping route
    try:
        response = session.get(f"{base_url}/admin/api-scraping")
        print(f"✅ API Scraping route: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ API Scraping page accessible")
            if "API Scraping" in response.text:
                print("✅ API Scraping content found")
            else:
                print("⚠️  API Scraping content might be simplified")
        else:
            print(f"❌ API Scraping route returned {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API Scraping route failed: {e}")
        return False
    
    # Test 5: Other admin routes
    test_routes = [
        '/admin/tools',
        '/admin/scraping', 
        '/admin/analytics',
        '/admin/import_export'
    ]
    
    working_routes = 0
    for route in test_routes:
        try:
            response = session.get(f"{base_url}{route}")
            if response.status_code == 200:
                print(f"✅ {route}: Working")
                working_routes += 1
            else:
                print(f"⚠️  {route}: Status {response.status_code}")
        except Exception as e:
            print(f"❌ {route}: Failed - {e}")
    
    print(f"\n📊 Test Results:")
    print(f"   Working routes: {working_routes}/{len(test_routes)}")
    
    if working_routes >= len(test_routes) * 0.8:  # 80% success rate
        print("✅ Dashboard is mostly functional!")
        return True
    else:
        print("❌ Multiple routes are failing")
        return False

if __name__ == "__main__":
    print("Waiting for server to be ready...")
    time.sleep(3)
    
    success = test_dashboard_comprehensive()
    
    if success:
        print("\n🎉 Dashboard testing completed successfully!")
        print("🌐 You can now use the dashboard at: http://localhost:5001/admin/login")
        print("🔐 Login with: admin / admin123")
    else:
        print("\n❌ Dashboard has issues that need to be resolved")
