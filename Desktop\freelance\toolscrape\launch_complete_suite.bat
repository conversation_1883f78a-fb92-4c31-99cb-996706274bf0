@echo off
title AI Tools Complete Interface Suite
color 0B

echo.
echo ================================================================
echo    🚀 AI Tools Complete Interface Suite                       
echo ================================================================
echo.
echo Choose which interface to launch:
echo.
echo [1] 🌐 Enhanced Web Interface (Public View)     - Port 5000
echo [2] 🔧 Admin Dashboard (Management Panel)       - Port 5001  
echo [3] 🚀 Launch Both Interfaces
echo [4] ❌ Exit
echo.
set /p choice="Enter your choice (1-4): "

cd /d "C:\Users\<USER>\Desktop\freelance\toolscrape"

if "%choice%"=="1" goto launch_web
if "%choice%"=="2" goto launch_admin
if "%choice%"=="3" goto launch_both
if "%choice%"=="4" goto exit
goto invalid

:launch_web
echo.
echo 🌐 Starting Enhanced Web Interface...
echo ✨ Features: Modern UI, Tool Search, Categories, Filtering
echo 🔗 URL: http://localhost:5000
echo.
if exist ".venv\Scripts\python.exe" (
    start "AI Tools Web Interface" ".venv\Scripts\python.exe" launch_enhanced.py
) else (
    start "AI Tools Web Interface" python launch_enhanced.py
)
timeout /t 3 >nul
start http://localhost:5000
goto end

:launch_admin
echo.
echo 🔧 Starting Admin Dashboard...
echo ✨ Features: CRUD, Bulk Ops, Scraping, Publishing, Analytics
echo 🔗 URL: http://localhost:5001/admin/login
echo 🔐 Login: admin / admin123
echo.
if exist ".venv\Scripts\python.exe" (
    start "AI Tools Admin Dashboard" ".venv\Scripts\python.exe" admin_dashboard.py
) else (
    start "AI Tools Admin Dashboard" python admin_dashboard.py
)
timeout /t 3 >nul
start http://localhost:5001/admin/login
goto end

:launch_both
echo.
echo 🚀 Starting Complete Interface Suite...
echo.
echo 🌐 Launching Enhanced Web Interface (Port 5000)...
if exist ".venv\Scripts\python.exe" (
    start "AI Tools Web Interface" ".venv\Scripts\python.exe" launch_enhanced.py
) else (
    start "AI Tools Web Interface" python launch_enhanced.py
)

echo 🔧 Launching Admin Dashboard (Port 5001)...
if exist ".venv\Scripts\python.exe" (
    start "AI Tools Admin Dashboard" ".venv\Scripts\python.exe" admin_dashboard.py
) else (
    start "AI Tools Admin Dashboard" python admin_dashboard.py
)

echo.
echo ⏳ Waiting for servers to start...
timeout /t 5 >nul

echo 🌐 Opening Web Interface...
start http://localhost:5000

echo 🔧 Opening Admin Dashboard...
start http://localhost:5001/admin/login

echo.
echo ================================================================
echo    🎉 Both interfaces are now running!                        
echo ================================================================
echo.
echo 🌐 Public Web Interface:  http://localhost:5000
echo    └─ Modern UI for browsing AI tools
echo.
echo 🔧 Admin Dashboard:       http://localhost:5001/admin/login
echo    └─ Management interface (Login: admin/admin123)
echo.
echo 💡 Press Ctrl+C in the server windows to stop them
echo ================================================================
goto end

:invalid
echo.
echo ❌ Invalid choice. Please enter 1, 2, 3, or 4.
timeout /t 2 >nul
goto start

:exit
echo.
echo 👋 Goodbye!
goto end

:end
echo.
echo Press any key to exit this launcher...
pause >nul
