{% extends "admin/base.html" %}

{% block title %}🛠️ Tools Management{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">🛠️ Tools Management</h1>
    <p class="page-subtitle">
        Manage your AI tools database - add, edit, verify, and organize tools efficiently.
    </p>
</div>

<!-- Search and Filters -->
<div class="content-section">
    <div class="section-header">
        <h2 class="section-title">🔍 Search & Filter</h2>
        <a href="#" class="btn btn-primary">➕ Add New Tool</a>
    </div>
    
    <form method="GET" class="search-form" style="display: grid; grid-template-columns: 1fr 200px 150px auto; gap: 15px; align-items: end;">
        <div class="form-group" style="margin-bottom: 0;">
            <label class="form-label">Search Tools</label>
            <input type="text" name="search" class="form-input" 
                   placeholder="Search by name or description..." 
                   value="{{ search or '' }}">
        </div>
        
        <div class="form-group" style="margin-bottom: 0;">
            <label class="form-label">Category</label>
            <select name="category" class="form-select">
                <option value="">All Categories</option>
                {% if categories %}
                    {% for cat in categories %}
                    <option value="{{ cat.category }}" {% if category == cat.category %}selected{% endif %}>
                        {{ cat.category }}
                    </option>
                    {% endfor %}
                {% endif %}
            </select>
        </div>
        
        <div class="form-group" style="margin-bottom: 0;">
            <label class="form-label">Status</label>
            <select name="status" class="form-select">
                <option value="">All Status</option>
                <option value="verified" {% if status == 'verified' %}selected{% endif %}>✅ Verified</option>
                <option value="unverified" {% if status == 'unverified' %}selected{% endif %}>⏳ Unverified</option>
                <option value="published" {% if status == 'published' %}selected{% endif %}>📢 Published</option>
                <option value="unpublished" {% if status == 'unpublished' %}selected{% endif %}>📝 Draft</option>
            </select>
        </div>
        
        <div style="display: flex; gap: 10px;">
            <button type="submit" class="btn btn-primary">🔍 Search</button>
            <a href="{{ url_for('admin_tools') }}" class="btn btn-secondary">🔄 Reset</a>
        </div>
    </form>
</div>

<!-- Tools List -->
<div class="content-section">
    <div class="section-header">
        <h2 class="section-title">📋 Tools Database</h2>
        <div style="display: flex; gap: 10px;">
            <select class="form-select" style="width: 120px;">
                <option>Bulk Actions</option>
                <option>✅ Verify Selected</option>
                <option>📢 Publish Selected</option>
                <option>🗑️ Delete Selected</option>
            </select>
            <button class="btn btn-secondary">Apply</button>
        </div>
    </div>
    
    {% if tools %}
    <div class="data-table-container">
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 30px;">
                        <input type="checkbox" style="accent-color: #3b82f6;">
                    </th>
                    <th>Tool Information</th>
                    <th>Category</th>
                    <th>Status</th>
                    <th>Free Tier</th>
                    <th>Created</th>
                    <th style="width: 120px;">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for tool in tools %}
                <tr>
                    <td>
                        <input type="checkbox" value="{{ tool.id }}" style="accent-color: #3b82f6;">
                    </td>
                    <td>
                        <div>
                            <strong style="color: #e2e8f0; font-size: 1rem;">{{ tool.name }}</strong>
                            {% if tool.description %}
                            <div style="color: #94a3b8; font-size: 0.85rem; margin-top: 4px; line-height: 1.4;">
                                {{ tool.description[:100] }}{% if tool.description|length > 100 %}...{% endif %}
                            </div>
                            {% endif %}
                            {% if tool.url %}
                            <div style="margin-top: 6px;">
                                <a href="{{ tool.url }}" target="_blank" 
                                   style="color: #60a5fa; font-size: 0.8rem; text-decoration: none;">
                                    🔗 {{ tool.url[:40] }}{% if tool.url|length > 40 %}...{% endif %}
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </td>
                    <td>
                        {% if tool.category %}
                        <span style="background: rgba(59, 130, 246, 0.2); color: #60a5fa; padding: 4px 10px; border-radius: 6px; font-size: 0.8rem; font-weight: 500;">
                            {{ tool.category }}
                        </span>
                        {% else %}
                        <span style="color: #94a3b8; font-style: italic;">Uncategorized</span>
                        {% endif %}
                    </td>
                    <td>
                        <div style="display: flex; flex-direction: column; gap: 4px;">
                            {% if tool.verified %}
                            <span style="background: rgba(16, 185, 129, 0.2); color: #6ee7b7; padding: 3px 8px; border-radius: 4px; font-size: 0.75rem; text-align: center;">
                                ✅ Verified
                            </span>
                            {% else %}
                            <span style="background: rgba(245, 158, 11, 0.2); color: #fcd34d; padding: 3px 8px; border-radius: 4px; font-size: 0.75rem; text-align: center;">
                                ⏳ Pending
                            </span>
                            {% endif %}
                            
                            {% if tool.published %}
                            <span style="background: rgba(59, 130, 246, 0.2); color: #93c5fd; padding: 3px 8px; border-radius: 4px; font-size: 0.75rem; text-align: center;">
                                📢 Published
                            </span>
                            {% else %}
                            <span style="background: rgba(71, 85, 105, 0.3); color: #94a3b8; padding: 3px 8px; border-radius: 4px; font-size: 0.75rem; text-align: center;">
                                📝 Draft
                            </span>
                            {% endif %}
                        </div>
                    </td>
                    <td style="text-align: center;">
                        {% if tool.free_tier %}
                        <span style="color: #6ee7b7; font-size: 1.2rem;">💚</span>
                        {% else %}
                        <span style="color: #fca5a5; font-size: 1.2rem;">💰</span>
                        {% endif %}
                    </td>
                    <td style="color: #94a3b8; font-size: 0.85rem;">
                        {{ tool.created_at.split(' ')[0] if tool.created_at else 'N/A' }}
                    </td>
                    <td>
                        <div style="display: flex; gap: 5px; flex-wrap: wrap;">
                            <a href="#" class="btn btn-secondary" style="padding: 6px 8px; font-size: 0.75rem;">
                                ✏️ Edit
                            </a>
                            <button class="btn btn-danger" style="padding: 6px 8px; font-size: 0.75rem;">
                                🗑️
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    {% if total_tools > per_page %}
    <div style="margin-top: 30px; display: flex; justify-content: center; align-items: center; gap: 15px;">
        {% set total_pages = ((total_tools - 1) // per_page) + 1 %}
        
        {% if page > 1 %}
        <a href="?page={{ page - 1 }}&search={{ search or '' }}&category={{ category or '' }}&status={{ status or '' }}" 
           class="btn btn-secondary">← Previous</a>
        {% endif %}
        
        <div style="display: flex; gap: 8px; align-items: center;">
            {% for p in range(max(1, page - 2), min(total_pages + 1, page + 3)) %}
                {% if p == page %}
                <span style="background: #3b82f6; color: white; padding: 8px 12px; border-radius: 6px; font-weight: 600;">
                    {{ p }}
                </span>
                {% else %}
                <a href="?page={{ p }}&search={{ search or '' }}&category={{ category or '' }}&status={{ status or '' }}" 
                   style="color: #94a3b8; padding: 8px 12px; text-decoration: none; border-radius: 6px; transition: all 0.3s;">
                    {{ p }}
                </a>
                {% endif %}
            {% endfor %}
        </div>
        
        {% if page < total_pages %}
        <a href="?page={{ page + 1 }}&search={{ search or '' }}&category={{ category or '' }}&status={{ status or '' }}" 
           class="btn btn-secondary">Next →</a>
        {% endif %}
    </div>
    
    <div style="text-align: center; margin-top: 15px; color: #94a3b8; font-size: 0.9rem;">
        Showing {{ ((page - 1) * per_page) + 1 }} to {{ min(page * per_page, total_tools) }} of {{ total_tools }} tools
    </div>
    {% endif %}
    
    {% else %}
    <div style="text-align: center; padding: 60px 20px; color: #94a3b8;">
        <div style="font-size: 4rem; margin-bottom: 20px;">🔍</div>
        <h3 style="margin-bottom: 10px; color: #e2e8f0;">No tools found</h3>
        {% if search or category or status %}
        <p>Try adjusting your search criteria or filters</p>
        <a href="{{ url_for('admin_tools') }}" class="btn btn-secondary" style="margin-top: 20px;">Clear Filters</a>
        {% else %}
        <p>Start building your AI tools database</p>
        <a href="#" class="btn btn-primary" style="margin-top: 20px;">Add Your First Tool</a>
        {% endif %}
    </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all checkbox functionality
    const selectAllCheckbox = document.querySelector('thead input[type="checkbox"]');
    const toolCheckboxes = document.querySelectorAll('tbody input[type="checkbox"]');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            toolCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }
    
    // Update select all when individual checkboxes change
    toolCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = Array.from(toolCheckboxes).filter(cb => cb.checked).length;
            selectAllCheckbox.checked = checkedCount === toolCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < toolCheckboxes.length;
        });
    });
});
</script>
{% endblock %}
