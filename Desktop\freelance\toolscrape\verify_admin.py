import sys
import os

print("🔍 Admin Dashboard Verification")
print("=" * 40)

# Check current directory
print(f"📍 Current directory: {os.getcwd()}")

# Check if admin_dashboard.py exists
admin_file = "admin_dashboard.py"
if os.path.exists(admin_file):
    print(f"✅ {admin_file} found")
else:
    print(f"❌ {admin_file} not found")
    sys.exit(1)

# Check if database exists
db_file = "ai_tools_master.db"
if os.path.exists(db_file):
    print(f"✅ {db_file} found")
else:
    print(f"❌ {db_file} not found")
    sys.exit(1)

# Test imports
try:
    import flask
    print("✅ Flask available")
except ImportError:
    print("❌ Flask not available - install with: pip install flask")

try:
    import pandas
    print("✅ Pandas available")
except ImportError:
    print("❌ Pandas not available - install with: pip install pandas")

try:
    import sqlite3
    print("✅ SQLite3 available")
except ImportError:
    print("❌ SQLite3 not available")

print("\n🚀 Ready to start admin dashboard!")
print("📋 Run: python admin_dashboard.py")
print("🌐 Then visit: http://localhost:5001/admin/login")
print("🔐 Login: admin / admin123")
