#!/usr/bin/env python3
"""Test the admin dashboard routes"""

import requests
import time

def test_routes():
    base_url = "http://localhost:5001"
    
    # Test basic connectivity
    try:
        response = requests.get(f"{base_url}/admin/login", timeout=5)
        print(f"✅ Login page: {response.status_code}")
    except Exception as e:
        print(f"❌ Cannot connect to dashboard: {e}")
        return False
    
    # Test API scraping route (should redirect to login)
    try:
        response = requests.get(f"{base_url}/admin/api-scraping", timeout=5, allow_redirects=False)
        print(f"✅ API scraping route: {response.status_code} (redirect expected)")
    except Exception as e:
        print(f"❌ API scraping route failed: {e}")
        return False
    
    # Test login functionality
    session = requests.Session()
    try:
        # Get login page first
        response = session.get(f"{base_url}/admin/login")
        
        # Attempt login
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        response = session.post(f"{base_url}/admin/login", data=login_data, allow_redirects=False)
        print(f"✅ Login attempt: {response.status_code}")
        
        # Test dashboard access after login
        if response.status_code in [302, 303]:  # Redirect after successful login
            response = session.get(f"{base_url}/admin/dashboard")
            print(f"✅ Dashboard access: {response.status_code}")
            
            # Test API scraping access after login
            response = session.get(f"{base_url}/admin/api-scraping")
            print(f"✅ API scraping access: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ All routes working correctly!")
                return True
            else:
                print(f"❌ API scraping page returned {response.status_code}")
                print("Response content:", response.text[:200])
        
    except Exception as e:
        print(f"❌ Login test failed: {e}")
        return False
    
    return False

if __name__ == "__main__":
    print("🧪 Testing admin dashboard routes...")
    time.sleep(2)  # Wait for server to start
    success = test_routes()
    if success:
        print("\n✅ All tests passed! Dashboard is working correctly.")
    else:
        print("\n❌ Some tests failed. Check the server logs.")
