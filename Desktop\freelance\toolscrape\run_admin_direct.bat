@echo off
cls
echo ================================================================
echo    🚀 AI Tools Admin Dashboard - Direct Launcher               
echo ================================================================
echo.

cd /d "C:\Users\<USER>\Desktop\freelance\toolscrape"

echo 📍 Current directory: %CD%
echo 🐍 Starting Python Flask server...
echo.
echo 🌐 Admin Dashboard URL: http://localhost:5001/admin/login
echo 🔐 Login: admin / admin123
echo.
echo ⏳ Starting server... (this may take a few seconds)
echo 💡 Once you see "Running on http://127.0.0.1:5001", the server is ready!
echo.

python admin_dashboard.py

echo.
echo 🛑 Server stopped. Press any key to exit...
pause >nul
