#!/usr/bin/env python3
"""Test the minimal admin dashboard"""

import sys
import os

print("Testing minimal admin dashboard...")

try:
    from admin_dashboard_minimal import app
    
    print("✅ Minimal dashboard imported successfully!")
    
    # List routes
    print("\nRegistered routes:")
    for rule in app.url_map.iter_rules():
        print(f"  {rule.endpoint}: {rule.rule}")
    
    # Test API scraping route specifically
    with app.test_client() as client:
        response = client.get('/admin/api-scraping')
        print(f"\nTest GET /admin/api-scraping: {response.status_code}")
        if response.status_code == 302:
            print("  Redirected (probably need to login first)")
        elif response.status_code == 200:
            print("  Success!")
        else:
            print(f"  Error: {response.status_code}")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
