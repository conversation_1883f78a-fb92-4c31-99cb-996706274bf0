"""
Ultra-Aggressive AI Tools Scraper - 5000+ Tools Target
This version uses every possible technique to reach 5000+ tools
"""

import requests
from bs4 import BeautifulSoup
import sqlite3
import time
import logging
from fake_useragent import UserAgent
import pandas as pd
from datetime import datetime
import re
import json
from urllib.parse import urljoin, urlparse, quote
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading
import random

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UltraAggressiveAIToolsScraper:
    def __init__(self):
        self.ua = UserAgent()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache'
        })
        
        # Use existing database
        self.conn = sqlite3.connect('ai_tools_database_fixed.db', check_same_thread=False)
        self.cursor = self.conn.cursor()
        
        self.all_tools = []
        self.seen_names = set()
        self.lock = threading.Lock()
        
        # Load existing tools into memory for deduplication
        self.load_existing_tools()
    
    def load_existing_tools(self):
        """Load existing tools for deduplication"""
        try:
            self.cursor.execute('SELECT name FROM ai_tools')
            existing = self.cursor.fetchall()
            self.seen_names = {name[0].lower().strip() for name in existing}
            logger.info(f"Loaded {len(self.seen_names)} existing tools for deduplication")
        except Exception as e:
            logger.error(f"Error loading existing tools: {e}")
    
    def clean_text(self, text):
        """Clean and normalize text"""
        if not text:
            return ""
        text = re.sub(r'\\s+', ' ', text.strip())
        text = re.sub(r'[^\\w\\s\\-\\.\\,\\!\\?\\:\\;\\(\\)\\[\\]\\/\\&\\%\\$\\@\\#\\+\\=]', '', text)
        return text[:500]
    
    def add_tool_to_collection(self, tool):
        """Add tool to collection with duplicate checking"""
        name_key = tool['name'].lower().strip()
        if name_key and len(name_key) > 2:
            # Quality checks
            skip_keywords = ['read more', 'click here', 'learn more', 'view all', 'see more', 'load more', 'show more', 'continue reading']
            if not any(skip in name_key for skip in skip_keywords):
                with self.lock:
                    if name_key not in self.seen_names:
                        self.all_tools.append(tool)
                        self.seen_names.add(name_key)
                        return True
        return False
    
    def extract_tool_data(self, element, source_name, source_url):
        """Extract tool data from HTML element"""
        try:
            # Extract name with multiple strategies
            name = ""
            
            # Strategy 1: Look for headings
            for tag in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                name_elem = element.find(tag)
                if name_elem:
                    name = self.clean_text(name_elem.get_text())
                    if name and len(name) > 2:
                        break
            
            # Strategy 2: Look for specific classes
            if not name:
                name_selectors = ['.title', '.name', '.tool-name', '.product-name', '.ai-tool-name']
                for sel in name_selectors:
                    name_elem = element.select_one(sel)
                    if name_elem:
                        name = self.clean_text(name_elem.get_text())
                        if name and len(name) > 2:
                            break
            
            # Strategy 3: Look for links with meaningful text
            if not name:
                links = element.find_all('a')
                for link in links:
                    if link.get('title'):
                        name = self.clean_text(link.get('title'))
                        if name and len(name) > 2:
                            break
                    elif link.get_text().strip():
                        link_text = self.clean_text(link.get_text())
                        if link_text and len(link_text) > 2 and len(link_text) < 100:
                            name = link_text
                            break
            
            # Strategy 4: Look for strong/bold text
            if not name:
                strong_elem = element.find(['strong', 'b'])
                if strong_elem:
                    name = self.clean_text(strong_elem.get_text())
            
            if not name or len(name) < 3:
                return None
            
            # Extract description with multiple strategies
            description = ""
            
            # Strategy 1: Look for paragraphs
            desc_elem = element.find('p')
            if desc_elem:
                desc_text = desc_elem.get_text()
                if desc_text and len(desc_text) > 10:
                    description = self.clean_text(desc_text)
            
            # Strategy 2: Look for specific description classes
            if not description:
                desc_selectors = ['.description', '.excerpt', '.summary', '.content', '.desc', '.details']
                for sel in desc_selectors:
                    desc_elem = element.select_one(sel)
                    if desc_elem:
                        desc_text = desc_elem.get_text()
                        if desc_text and len(desc_text) > 10:
                            description = self.clean_text(desc_text)
                            break
            
            # Extract URL
            website_url = ""
            link = element.find('a', href=True)
            if link:
                href = link['href']
                if href.startswith('http'):
                    website_url = href
                elif href.startswith('/'):
                    parsed_source = urlparse(source_url)
                    website_url = f"{parsed_source.scheme}://{parsed_source.netloc}{href}"
            
            # Enhanced category and pricing detection
            element_text = element.get_text().lower()
            
            # Category detection with more keywords
            category = "AI Tools"
            category_map = {
                'Writing': ['writing', 'content', 'text', 'copy', 'blog', 'article'],
                'Design': ['design', 'graphic', 'ui', 'ux', 'visual', 'logo'],
                'Development': ['code', 'develop', 'program', 'api', 'github', 'software'],
                'Video': ['video', 'movie', 'film', 'youtube', 'streaming'],
                'Image': ['image', 'photo', 'picture', 'visual', 'art', 'graphics'],
                'Audio': ['audio', 'music', 'sound', 'voice', 'podcast'],
                'Marketing': ['marketing', 'seo', 'ads', 'social media', 'campaign'],
                'Business': ['business', 'finance', 'crm', 'sales', 'analytics'],
                'Productivity': ['productivity', 'automation', 'workflow', 'task'],
                'Education': ['education', 'learning', 'teach', 'course', 'study']
            }
            
            for cat_name, keywords in category_map.items():
                if any(keyword in element_text for keyword in keywords):
                    category = cat_name
                    break
            
            # Enhanced pricing detection
            pricing = "Unknown"
            if any(word in element_text for word in ['free', 'gratis', '$0', 'no cost', 'open source']):
                pricing = "Free"
            elif any(word in element_text for word in ['premium', 'paid', '$', 'subscription', 'pro', 'price']):
                pricing = "Premium"
            elif 'freemium' in element_text:
                pricing = "Freemium"
            
            return {
                'name': name,
                'description': description,
                'website_url': website_url,
                'pricing_model': pricing,
                'category': category,
                'source_website': source_name.lower().replace(' ', '').replace('.', ''),
                'source_page_url': source_url,
                'free_tier': pricing in ["Free", "Freemium"]
            }
            
        except Exception as e:
            return None
    
    def scrape_single_site(self, url, site_name, max_pages=100):
        """Aggressively scrape a single site"""
        logger.info(f"🚀 AGGRESSIVE SCRAPING: {site_name}")
        tools = []
        
        # Multiple URL patterns to try
        url_patterns = [
            lambda u, p: f"{u}?page={p}",
            lambda u, p: f"{u}?p={p}",
            lambda u, p: f"{u}/page/{p}",
            lambda u, p: f"{u}/page/{p}/",
            lambda u, p: f"{u}?paged={p}",
            lambda u, p: f"{u}?sf_paged={p}",
            lambda u, p: f"{u}?offset={p*20}",
            lambda u, p: f"{u}#page={p}"
        ]
        
        # Generic selectors that work on most sites
        selectors = [
            # Tool-specific selectors
            '[class*="tool"]', '[class*="ai"]', '[class*="item"]',
            '[class*="card"]', '[class*="product"]', '[class*="listing"]',
            '[id*="tool"]', '[id*="ai"]', '[id*="item"]',
            
            # Content structure selectors
            'article', '.grid > div', '.row > div', '.col > div',
            '.list-item', '.directory-item', '.grid-item',
            
            # Common CMS selectors
            '.post', '.entry', '.content-item', '.block',
            
            # Generic container selectors
            'li', 'div[class]', 'section > div'
        ]
        
        for page in range(1, max_pages + 1):
            tools_found_this_page = 0
            
            # Try each URL pattern
            for pattern in url_patterns:
                try:
                    page_url = pattern(url, page)
                    
                    # Add random delay and rotate user agent
                    time.sleep(random.uniform(0.5, 2.0))
                    self.session.headers['User-Agent'] = self.ua.random
                    
                    response = self.session.get(page_url, timeout=15)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        # Try each selector
                        for selector in selectors:
                            try:
                                elements = soup.select(selector)
                                if len(elements) >= 3:  # Must have at least 3 elements
                                    page_tools = 0
                                    for elem in elements[:50]:  # Limit per selector
                                        tool = self.extract_tool_data(elem, site_name, page_url)
                                        if tool and self.add_tool_to_collection(tool):
                                            tools.append(tool)
                                            page_tools += 1
                                    
                                    if page_tools > 0:
                                        tools_found_this_page += page_tools
                                        logger.info(f"📄 {site_name} Page {page}: {page_tools} tools (selector: {selector[:30]})")
                                        break  # Found tools, stop trying selectors
                                        
                            except Exception as e:
                                continue
                        
                        if tools_found_this_page > 0:
                            break  # Found tools, stop trying URL patterns
                            
                except Exception as e:
                    continue
            
            if tools_found_this_page == 0:
                if page <= 3:  # Keep trying for first few pages
                    continue
                else:
                    logger.info(f"📄 {site_name}: No tools found on page {page}, stopping")
                    break
        
        logger.info(f"✅ {site_name}: {len(tools)} total tools collected")
        return tools
    
    def get_massive_site_list(self):
        """Get a massive list of AI tool sites to scrape"""
        return [
            # Known working sites
            ('https://insidr.ai/ai-tools/', 'Insidr AI'),
            ('https://www.aitoolguru.com/', 'AI Tool Guru'),
            ('https://www.ai-finder.net/', 'AI Finder'),
            ('https://allthingsai.com/tools', 'AllThingsAI'),
            ('https://www.aitools.fyi/', 'AI Tools FYI'),
            
            # Additional high-potential sites
            ('https://www.bestaitools.com/', 'Best AI Tools'),
            ('https://www.futuretools.io/', 'Future Tools'),
            ('https://opentools.ai/', 'Open Tools'),
            ('https://theresanaiforthat.com/', 'Theres An AI For That'),
            ('https://aitoolslist.io/', 'AI Tools List'),
            
            # Directory sites
            ('https://aicollection.com/', 'AI Collection'),
            ('https://aitoolbox.co/', 'AI Toolbox'),
            ('https://www.startupstash.com/ai-tools/', 'Startup Stash AI'),
            ('https://www.producthunt.com/topics/artificial-intelligence', 'Product Hunt AI'),
            
            # Newer/Additional sites
            ('https://www.aitoolnet.com/', 'AI Tool Net'),
            ('https://www.toptools.ai/', 'Top Tools AI'),
            ('https://www.aitopia.com/', 'AI Topia'),
            ('https://www.aitools.org/', 'AI Tools Org'),
            ('https://www.aifinder.io/', 'AI Finder IO'),
            ('https://www.aitools.directory/', 'AI Tools Directory'),
            
            # Blog/Review sites that list AI tools
            ('https://blog.google/technology/ai/', 'Google AI Blog'),
            ('https://www.unite.ai/tools/', 'Unite AI'),
            ('https://www.marktechpost.com/', 'MarkTechPost'),
            ('https://venturebeat.com/ai/', 'VentureBeat AI'),
            
            # GitHub awesome lists (direct repos)
            ('https://github.com/mahseema/awesome-ai-tools', 'GitHub Awesome AI'),
            ('https://github.com/steven2358/awesome-generative-ai', 'GitHub Generative AI'),
            
            # Social/Community sites
            ('https://www.reddit.com/r/artificial/', 'Reddit AI'),
            ('https://www.reddit.com/r/MachineLearning/', 'Reddit ML'),
            
            # Tool aggregators
            ('https://www.toolify.ai/', 'Toolify'),
            ('https://www.saasworthy.com/category/artificial-intelligence', 'SaasWorthy AI'),
            ('https://alternativeto.net/category/ai-machine-learning/', 'AlternativeTo AI'),
        ]
    
    def scrape_github_raw_content(self):
        """Scrape GitHub repositories for tools using raw content"""
        logger.info("🎯 SCRAPING GITHUB RAW CONTENT")
        
        github_sources = [
            'https://raw.githubusercontent.com/mahseema/awesome-ai-tools/main/README.md',
            'https://raw.githubusercontent.com/steven2358/awesome-generative-ai/main/README.md',
            'https://raw.githubusercontent.com/f/awesome-chatgpt-prompts/main/README.md',
            'https://raw.githubusercontent.com/josephmisiti/awesome-machine-learning/master/README.md'
        ]
        
        tools = []
        
        for url in github_sources:
            try:
                logger.info(f"🔍 Scraping GitHub raw: {url}")
                
                response = self.session.get(url, timeout=15)
                if response.status_code == 200:
                    content = response.text
                    
                    # Multiple regex patterns to catch different markdown link formats
                    patterns = [
                        r'\\[([^\\]]+)\\]\\(([^\\)]+)\\)',  # Standard [text](url)
                        r'\\* \\[([^\\]]+)\\]\\(([^\\)]+)\\)',  # List item [text](url)
                        r'- \\[([^\\]]+)\\]\\(([^\\)]+)\\)',  # Dash list [text](url)
                        r'\\d+\\. \\[([^\\]]+)\\]\\(([^\\)]+)\\)',  # Numbered list [text](url)
                    ]
                    
                    for pattern in patterns:
                        matches = re.findall(pattern, content)
                        
                        for name, link in matches:
                            # Filter for AI-related tools
                            ai_keywords = ['ai', 'artificial intelligence', 'machine learning', 'neural', 'deep learning', 'gpt', 'chatbot', 'automation', 'generator']
                            
                            if any(keyword in name.lower() for keyword in ai_keywords) or any(keyword in link.lower() for keyword in ai_keywords):
                                if link.startswith('http'):
                                    tool = {
                                        'name': self.clean_text(name),
                                        'description': f"AI tool from GitHub awesome list: {name}",
                                        'website_url': link,
                                        'pricing_model': 'Unknown',
                                        'category': 'AI Tools',
                                        'source_website': 'github-awesome',
                                        'source_page_url': url,
                                        'free_tier': 'open source' in name.lower() or 'free' in name.lower()
                                    }
                                    
                                    if self.add_tool_to_collection(tool):
                                        tools.append(tool)
                    
                    logger.info(f"📄 {url.split('/')[-2]}: {len([t for t in tools if url in t['source_page_url']])} tools extracted")
                
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error scraping GitHub {url}: {e}")
                continue
        
        logger.info(f"✅ GITHUB RAW: {len(tools)} tools collected")
        return tools
    
    def parallel_scrape_sites(self, sites_list, max_workers=5):
        """Scrape multiple sites in parallel"""
        logger.info(f"🚀 PARALLEL SCRAPING: {len(sites_list)} sites with {max_workers} workers")
        
        all_tools = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all scraping tasks
            future_to_site = {
                executor.submit(self.scrape_single_site, url, name, 50): (url, name) 
                for url, name in sites_list
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_site):
                url, name = future_to_site[future]
                try:
                    tools = future.result(timeout=300)  # 5 minute timeout per site
                    if tools:
                        all_tools.extend(tools)
                        logger.info(f"✅ {name}: {len(tools)} tools collected")
                    else:
                        logger.warning(f"❌ {name}: No tools collected")
                        
                except Exception as e:
                    logger.error(f"❌ Error scraping {name}: {e}")
        
        return all_tools
    
    def save_tools_to_database(self, tools):
        """Save tools to database with proper error handling"""
        saved_count = 0
        
        with self.lock:
            for tool in tools:
                try:
                    self.cursor.execute('''
                        INSERT OR IGNORE INTO ai_tools 
                        (name, description, website_url, pricing_model, category, 
                         source_website, source_page_url, free_tier)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        tool['name'],
                        tool['description'],
                        tool['website_url'],
                        tool['pricing_model'],
                        tool['category'],
                        tool['source_website'],
                        tool['source_page_url'],
                        tool['free_tier']
                    ))
                    
                    if self.cursor.rowcount > 0:
                        saved_count += 1
                        
                except Exception as e:
                    logger.error(f"Error saving tool {tool.get('name', 'Unknown')}: {e}")
                    continue
            
            self.conn.commit()
        
        logger.info(f"💾 Saved {saved_count} new tools to database")
        return saved_count
    
    def run_ultra_aggressive_scraping(self):
        """Run ultra-aggressive scraping to reach 5000+ tools"""
        logger.info("🚀🚀🚀 STARTING ULTRA-AGGRESSIVE SCRAPING - TARGET: 5000+ TOOLS 🚀🚀🚀")
        
        start_time = time.time()
        
        # Get initial count
        self.cursor.execute('SELECT COUNT(*) FROM ai_tools')
        initial_count = self.cursor.fetchone()[0]
        logger.info(f"Starting with {initial_count} tools in database")
        
        all_collected_tools = []
        
        # Strategy 1: Parallel scraping of high-volume sites
        logger.info("\\n" + "="*60)
        logger.info("STRATEGY 1: PARALLEL HIGH-VOLUME SCRAPING")
        logger.info("="*60)
        
        sites_list = self.get_massive_site_list()
        parallel_tools = self.parallel_scrape_sites(sites_list, max_workers=8)
        all_collected_tools.extend(parallel_tools)
        
        # Save intermediate results
        if all_collected_tools:
            saved_count = self.save_tools_to_database(all_collected_tools)
            logger.info(f"💾 Intermediate save: {saved_count} tools saved")
        
        # Strategy 2: GitHub repositories
        logger.info("\\n" + "="*60)
        logger.info("STRATEGY 2: ENHANCED GITHUB SCRAPING")
        logger.info("="*60)
        
        github_tools = self.scrape_github_raw_content()
        all_collected_tools.extend(github_tools)
        
        # Strategy 3: Secondary parallel scraping with different approach
        logger.info("\\n" + "="*60)
        logger.info("STRATEGY 3: SECONDARY SCRAPING ROUND")
        logger.info("="*60)
        
        # Try sites that might have been missed with more aggressive settings
        secondary_sites = [site for site in sites_list if random.random() < 0.5]  # Random subset
        secondary_tools = self.parallel_scrape_sites(secondary_sites, max_workers=10)
        all_collected_tools.extend(secondary_tools)
        
        # Remove duplicates and save final results
        unique_tools = []
        seen_names = set()
        for tool in all_collected_tools:
            name_key = tool['name'].lower().strip()
            if name_key not in seen_names:
                unique_tools.append(tool)
                seen_names.add(name_key)
        
        # Final save
        final_saved_count = self.save_tools_to_database(unique_tools)
        
        # Final summary
        self.cursor.execute('SELECT COUNT(*) FROM ai_tools')
        final_count = self.cursor.fetchone()[0]
        
        elapsed_time = time.time() - start_time
        
        logger.info(f"\\n{'='*80}")
        logger.info(f"🏁 ULTRA-AGGRESSIVE SCRAPING COMPLETED 🏁")
        logger.info(f"{'='*80}")
        logger.info(f"⏱️  Time elapsed: {elapsed_time/60:.2f} minutes")
        logger.info(f"📊 Initial tools: {initial_count}")
        logger.info(f"📊 Final tools: {final_count}")
        logger.info(f"📊 New tools added: {final_count - initial_count}")
        logger.info(f"📊 Tools collected this session: {len(unique_tools)}")
        logger.info(f"📊 Final tools saved: {final_saved_count}")
        
        if final_count >= 5000:
            logger.info(f"🎉🎉🎉 TARGET ACHIEVED! 🎉🎉🎉")
        elif final_count >= 1000:
            logger.info(f"🎉 Great progress! Need {5000 - final_count} more tools")
        else:
            logger.info(f"⚠️ Need {5000 - final_count} more tools to reach target")
        
        # Export results
        self.export_results()
        
        return final_count
    
    def export_results(self):
        """Export results to CSV"""
        try:
            df = pd.read_sql_query('SELECT * FROM ai_tools ORDER BY created_at DESC', self.conn)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"ultra_aggressive_scrape_results_{timestamp}.csv"
            df.to_csv(filename, index=False)
            
            logger.info(f"📁 Results exported to: {filename}")
            logger.info(f"📊 Total rows exported: {len(df)}")
            
            # Show summary by source
            if 'source_website' in df.columns:
                source_summary = df['source_website'].value_counts()
                logger.info(f"\\n📈 Tools by source:")
                for source, count in source_summary.head(10).items():
                    logger.info(f"  {source}: {count} tools")
                    
        except Exception as e:
            logger.error(f"Error exporting results: {e}")

def main():
    scraper = UltraAggressiveAIToolsScraper()
    final_count = scraper.run_ultra_aggressive_scraping()
    
    if final_count >= 5000:
        print(f"\\n🎉🎉🎉 SUCCESS! Collected {final_count} tools - TARGET ACHIEVED! 🎉🎉🎉")
    elif final_count >= 1000:
        print(f"\\n🎉 Great progress! Collected {final_count} tools. Need {5000 - final_count} more to reach 5000.")
    else:
        print(f"\\n⚠️ Collected {final_count} tools. Need {5000 - final_count} more to reach target.")
    
    scraper.conn.close()

if __name__ == "__main__":
    main()
