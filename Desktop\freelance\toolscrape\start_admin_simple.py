import os
import sys
import time
import webbrowser

def main():
    print("🚀 Starting Admin Dashboard...")
    print("📍 Working directory:", os.getcwd())
    print("🐍 Python path:", sys.executable)

    try:
        # Check if required files exist
        required_files = ['admin_dashboard.py', 'templates/admin/login.html']
        for file_path in required_files:
            if not os.path.exists(file_path):
                print(f"❌ Missing required file: {file_path}")
                input("Press Enter to exit...")
                return

        print("📁 Required files found")

        # Import the admin dashboard
        print("📥 Importing admin dashboard...")
        from admin_dashboard import app
        
        print("✅ Admin dashboard imported successfully!")
        print("🌐 Starting server on http://localhost:5001")
        print("🔐 Login credentials: admin / admin123")
        print("📋 Main Dashboard: http://localhost:5001/admin")
        print("🔌 API Scraping: http://localhost:5001/admin/api-scraping")
        
        # Open browser after 3 seconds
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open("http://localhost:5001/admin/login")
                print("🌐 <PERSON>rowser opened automatically")
            except Exception as e:
                print(f"⚠️ Could not open browser: {e}")
                print("Please manually open: http://localhost:5001/admin/login")
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        print("\n🔄 Starting Flask server...")
        print("Press Ctrl+C to stop")
        print("-" * 40)
        
        # Start the Flask app
        app.run(host='127.0.0.1', port=5001, debug=False, use_reloader=False)
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("💡 Try installing dependencies:")
        print("   pip install flask pandas requests beautifulsoup4")
        input("Press Enter to exit...")
        
    except Exception as e:
        print(f"❌ Startup Error: {e}")
        print("� Run the diagnostic tool: python fix_admin_dashboard.py")
        input("Press Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Admin dashboard stopped by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        input("Press Enter to exit...")
