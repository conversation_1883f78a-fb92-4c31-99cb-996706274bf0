{% extends "admin/base.html" %}

{% block title %}✏️ Edit API Configuration{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">✏️ Edit API Configuration</h1>
    <p class="page-subtitle">Update API configuration settings</p>
</div>

<div class="form-container">
    <form method="POST" class="api-form">
        <div class="form-group">
            <label for="config_name">Configuration Name *</label>
            <input type="text" id="config_name" name="config_name" value="{{ config.config_name }}" required>
            <div class="form-help">Give this API configuration a descriptive name</div>
        </div>
        
        <div class="form-group">
            <label for="api_type">API Type *</label>
            <select id="api_type" name="api_type" required>
                <option value="">Select API Type</option>
                <option value="github" {% if config.api_type == 'github' %}selected{% endif %}>GitHub API</option>
                <option value="producthunt" {% if config.api_type == 'producthunt' %}selected{% endif %}>Product Hunt API</option>
                <option value="reddit" {% if config.api_type == 'reddit' %}selected{% endif %}>Reddit API</option>
                <option value="custom" {% if config.api_type == 'custom' %}selected{% endif %}>Custom REST API</option>
            </select>
            <div class="form-help">Choose the type of API you want to configure</div>
        </div>
        
        <div class="form-group">
            <label for="api_endpoint">API Endpoint *</label>
            <input type="url" id="api_endpoint" name="api_endpoint" value="{{ config.api_endpoint }}" required>
            <div class="form-help">The full URL endpoint for the API</div>
        </div>
        
        <div class="form-group">
            <label for="api_key">API Key</label>
            <input type="password" id="api_key" name="api_key" value="{{ config.api_key or '' }}" placeholder="Enter API key if required">
            <div class="form-help">API key for authentication (if required)</div>
        </div>
        
        <div class="form-group">
            <label for="headers">Custom Headers</label>
            <textarea id="headers" name="headers" rows="3">{{ config.headers or '' }}</textarea>
            <div class="form-help">JSON format custom headers (optional)</div>
        </div>
        
        <div class="form-group">
            <label for="rate_limit">Rate Limit (requests per minute)</label>
            <input type="number" id="rate_limit" name="rate_limit" value="{{ config.rate_limit or 60 }}" min="1" max="1000">
            <div class="form-help">Number of requests per minute to avoid hitting API limits</div>
        </div>
        
        <div class="form-group">
            <div class="checkbox-group">
                <input type="checkbox" id="enabled" name="enabled" value="1" {% if config.enabled %}checked{% endif %}>
                <label for="enabled">Enable this configuration</label>
            </div>
        </div>
        
        <div class="form-actions">
            <a href="{{ url_for('admin_api_scraping') }}" class="btn btn-secondary">Cancel</a>
            <button type="submit" class="btn btn-primary">💾 Save Changes</button>
        </div>
    </form>
</div>

<style>
/* Form specific styles */
.form-container {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.api-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.checkbox-group input {
    transform: scale(1.2);
}

.checkbox-group label {
    margin: 0;
    color: #94a3b8;
    font-weight: 500;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 40px;
}

.form-help {
    font-size: 0.9rem;
    color: #94a3b8;
    margin-top: 5px;
}

@media (max-width: 768px) {
    .form-container {
        padding: 25px;
        margin: 0 15px;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}
