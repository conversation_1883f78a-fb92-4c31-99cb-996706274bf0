{% extends "admin/base.html" %}

{% block title %}✏️ Edit Tool{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">✏️ Edit Tool</h1>
    <p class="page-subtitle">Update tool information</p>
</div>

<div class="form-container">
    <form method="POST" class="tool-form">
        <div class="form-group">
            <label for="name">Tool Name *</label>
            <input type="text" id="name" name="name" value="{{ tool.name }}" required>
            <div class="form-help">Enter the full name of the AI tool</div>
        </div>
        
        <div class="form-group">
            <label for="description">Description</label>
            <textarea id="description" name="description" placeholder="Describe what this tool does and its key features..." rows="4">{{ tool.description or '' }}</textarea>
            <div class="form-help">Provide a detailed description of the tool's functionality</div>
        </div>
        
        <div class="form-group">
            <label for="category">Category</label>
            <select id="category" name="category">
                <option value="">Select a category</option>
                <option value="Writing" {% if tool.category == 'Writing' %}selected{% endif %}>Writing</option>
                <option value="Design" {% if tool.category == 'Design' %}selected{% endif %}>Design</option>
                <option value="AI Tools" {% if tool.category == 'AI Tools' %}selected{% endif %}>AI Tools</option>
                <option value="Development" {% if tool.category == 'Development' %}selected{% endif %}>Development</option>
                <option value="Image" {% if tool.category == 'Image' %}selected{% endif %}>Image</option>
                <option value="Video" {% if tool.category == 'Video' %}selected{% endif %}>Video</option>
                <option value="Audio" {% if tool.category == 'Audio' %}selected{% endif %}>Audio</option>
                <option value="Marketing" {% if tool.category == 'Marketing' %}selected{% endif %}>Marketing</option>
                <option value="Business" {% if tool.category == 'Business' %}selected{% endif %}>Business</option>
                <option value="Education" {% if tool.category == 'Education' %}selected{% endif %}>Education</option>
                <option value="Research" {% if tool.category == 'Research' %}selected{% endif %}>Research</option>
                <option value="Other" {% if tool.category == 'Other' %}selected{% endif %}>Other</option>
            </select>
            <div class="form-help">Choose the most appropriate category for this tool</div>
        </div>
        
        <div class="form-group">
            <label for="website_url">Website URL</label>
            <input type="url" id="website_url" name="website_url" value="{{ tool.website_url or '' }}" placeholder="https://example.com">
            <div class="form-help">The official website or main URL for this tool</div>
        </div>
        
        <div class="form-group">
            <label for="source_website">Source Website</label>
            <input type="text" id="source_website" name="source_website" value="{{ tool.source_website or 'Manual Entry' }}" readonly>
            <div class="form-help">Source where this tool information was obtained</div>
        </div>
        
        <div class="form-group">
            <div class="checkbox-group">
                <input type="checkbox" id="free_tier" name="free_tier" value="1" {% if tool.free_tier %}checked{% endif %}>
                <label for="free_tier">This tool has a free tier or is completely free</label>
            </div>
        </div>
        
        <div class="form-group">
            <div class="checkbox-group">
                <input type="checkbox" id="is_verified" name="is_verified" value="1" {% if tool.is_verified %}checked{% endif %}>
                <label for="is_verified">Mark this tool as verified</label>
            </div>
        </div>
        
        <div class="form-actions">
            <a href="{{ url_for('admin_tools') }}" class="btn btn-secondary">Cancel</a>
            <button type="submit" class="btn btn-primary">💾 Save Changes</button>
        </div>
    </form>
</div>

<style>
/* Form specific styles */
.form-container {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.tool-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.checkbox-group input {
    transform: scale(1.2);
}

.checkbox-group label {
    margin: 0;
    color: #94a3b8;
    font-weight: 500;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 40px;
}

.form-help {
    font-size: 0.9rem;
    color: #94a3b8;
    margin-top: 5px;
}

@media (max-width: 768px) {
    .form-container {
        padding: 25px;
        margin: 0 15px;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}
