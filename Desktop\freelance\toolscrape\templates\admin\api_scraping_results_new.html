{% extends "admin/base.html" %}

{% block title %}📊 API Scraping Results{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">📊 API Scraping Results</h1>
    <p class="page-subtitle">View and manage results from API scraping operations</p>
</div>

<!-- Results Stats -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-info">
            <div class="stat-value">{{ total_results or 0 }}</div>
            <div class="stat-label">Total Results</div>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-info">
            <div class="stat-value">{{ successful_results or 0 }}</div>
            <div class="stat-label">Successful</div>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">⚠️</div>
        <div class="stat-info">
            <div class="stat-value">{{ failed_results or 0 }}</div>
            <div class="stat-label">Failed</div>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">🆕</div>
        <div class="stat-info">
            <div class="stat-value">{{ new_tools or 0 }}</div>
            <div class="stat-label">New Tools Added</div>
        </div>
    </div>
</div>

<!-- Results Table -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Recent API Results</h3>
        <div class="header-actions">
            <button class="btn btn-secondary" onclick="refreshResults()">🔄 Refresh</button>
            <a href="{{ url_for('admin_api_scraping') }}" class="btn btn-primary">⚙️ Manage APIs</a>
        </div>
    </div>
    
    {% if results %}
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th>API Config</th>
                    <th>Status</th>
                    <th>Tools Found</th>
                    <th>Execution Time</th>
                    <th>Created At</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for result in results %}
                <tr>
                    <td>
                        <div class="config-info">
                            <div class="config-name">{{ result.config_name }}</div>
                            <div class="config-type">{{ result.api_type }}</div>
                        </div>
                    </td>
                    <td>
                        <span class="status-badge status-{{ result.status }}">
                            {{ result.status.title() }}
                        </span>
                    </td>
                    <td>
                        {% if result.tools_found %}
                            <span class="metric-value">{{ result.tools_found }}</span>
                        {% else %}
                            <span class="text-muted">0</span>
                        {% endif %}
                    </td>
                    <td>
                        <span class="execution-time">{{ result.execution_time }}s</span>
                    </td>
                    <td>
                        <div class="timestamp">{{ result.created_at }}</div>
                    </td>
                    <td>
                        <div class="action-buttons">
                            {% if result.result_data %}
                            <button class="btn btn-sm btn-secondary" onclick="viewDetails({{ result.id }})">
                                👁️ View
                            </button>
                            {% endif %}
                            {% if result.status == 'failed' and result.error_message %}
                            <button class="btn btn-sm btn-warning" onclick="viewError({{ result.id }})">
                                ⚠️ Error
                            </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="empty-state">
        <div class="empty-state-icon">📊</div>
        <div class="empty-state-title">No API Results Yet</div>
        <div class="empty-state-description">
            No API scraping results found. Configure and run some API scrapers to see results here.
        </div>
        <a href="{{ url_for('admin_api_scraping') }}" class="btn btn-primary">Configure APIs</a>
    </div>
    {% endif %}
</div>

<style>
/* Results page specific styles */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.stat-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 800;
    color: #3b82f6;
    line-height: 1;
}

.stat-label {
    color: #94a3b8;
    font-size: 0.9rem;
    margin-top: 5px;
}

.config-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.config-name {
    font-weight: 600;
    color: #e2e8f0;
}

.config-type {
    font-size: 0.8rem;
    color: #94a3b8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.execution-time {
    font-family: 'Monaco', 'Consolas', monospace;
    color: #94a3b8;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.metric-value {
    font-weight: 600;
    color: #10b981;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-success {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.status-running {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.status-failed {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .header-actions {
        flex-direction: column;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 5px;
    }
}
</style>

<script>
function refreshResults() {
    location.reload();
}

function viewDetails(resultId) {
    // Implementation for viewing result details
    alert('View details for result ID: ' + resultId);
}

function viewError(resultId) {
    // Implementation for viewing error details
    alert('View error for result ID: ' + resultId);
}
</script>
{% endblock %}
