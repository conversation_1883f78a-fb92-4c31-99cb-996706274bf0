#!/usr/bin/env python3

"""
Quick Admin Dashboard Test & Manual Startup Guide
"""

import os
import sys

def main():
    print("🔍 AI Tools Admin Dashboard - Setup Verification")
    print("=" * 55)
    print()
    
    # Check current directory
    current_dir = os.getcwd()
    print(f"📍 Current directory: {current_dir}")
    
    # Check if we're in the right directory
    expected_files = [
        "admin_dashboard.py",
        "ai_tools_master.db", 
        "templates"
    ]
    
    print("\n🔍 Checking required files:")
    all_good = True
    
    for file in expected_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} - NOT FOUND")
            all_good = False
    
    if not all_good:
        print("\n❌ Some required files are missing!")
        print("📋 Please ensure you're in the correct directory with all files.")
        return False
    
    print("\n🐍 Testing Python imports:")
    
    # Test imports
    required_modules = [
        ("flask", "Flask"),
        ("pandas", "Pandas"), 
        ("sqlite3", "SQLite3"),
        ("datetime", "DateTime"),
        ("json", "JSON")
    ]
    
    for module, name in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {name}")
        except ImportError:
            print(f"  ❌ {name} - Please install: pip install {module}")
            all_good = False
    
    if not all_good:
        print("\n❌ Some required modules are missing!")
        print("📦 Please install missing dependencies.")
        return False
    
    print("\n✅ All checks passed!")
    print("\n" + "=" * 55)
    print("🚀 MANUAL STARTUP INSTRUCTIONS")
    print("=" * 55)
    print()
    print("1️⃣ Open a new Command Prompt or PowerShell window")
    print()
    print("2️⃣ Navigate to the project directory:")
    print(f'   cd "{current_dir}"')
    print()
    print("3️⃣ Start the admin dashboard:")
    print("   python admin_dashboard.py")
    print()
    print("4️⃣ Wait for the server to start (you'll see):")
    print("   * Running on http://127.0.0.1:5001")
    print("   * Debug mode: on")
    print()
    print("5️⃣ Open your web browser and visit:")
    print("   🌐 http://localhost:5001/admin/login")
    print()
    print("6️⃣ Login with:")
    print("   👤 Username: admin")
    print("   🔑 Password: admin123")
    print()
    print("=" * 55)
    print("🎉 You should now see the admin dashboard!")
    print("=" * 55)
    
    return True

if __name__ == "__main__":
    success = main()
    print()
    input("Press Enter to continue...")
    sys.exit(0 if success else 1)
